import moment from 'moment';
import {ChatMessageType, ConciergeChatMessageType} from '~types/chat';

type Message = ChatMessageType | ConciergeChatMessageType;
interface Timestamped {
  timestamp: string;
}

type DateHeaderType = {
  timestamp: string;
  message: string;
  type: 'date';
};

type ChatOrDateHeaderType<T> = T | DateHeaderType;
export const getLastMessage = <T extends Message>(messages: T[]): T | null => {
  if (!Array.isArray(messages) || messages.length === 0) {
    return null;
  }
  const lastKey = messages.length - 1;
  return messages[lastKey];
};

export const addDateHeaders = <T extends Timestamped>(messagesArr: T[]): ChatOrDateHeaderType<T>[] => {
  const newMessages = [];
  const dayFormat = 'YYYY-MM-DD';

  for (let i = 0; i < messagesArr.length; i++) {
    const currentMessage = messagesArr[i];
    const currentMessageDate = currentMessage?.timestamp;
    const formattedDate = moment(currentMessageDate).format(dayFormat);

    if (i === 0 || formattedDate !== moment(messagesArr[i - 1]?.timestamp).format(dayFormat)) {
      let displayDate = '';
      if (moment().isSame(formattedDate, 'day')) {
        displayDate = 'Today';
      } else if (moment().subtract(1, 'days').isSame(formattedDate, 'day')) {
        displayDate = 'Yesterday';
      } else {
        displayDate = moment(currentMessageDate).format('DD MMMM YYYY');
      }

      newMessages.push({
        timestamp: currentMessage.timestamp,
        message: displayDate,
        type: 'date',
      });
    }

    newMessages.push(currentMessage);
  }

  return newMessages;
};
