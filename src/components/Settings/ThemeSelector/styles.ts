import {StyleSheet} from 'react-native';
const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      borderRadius: 12,
      overflow: 'hidden',
      marginVertical: 8,
    },
    header: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    optionsContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
      marginVertical: 4,
    },
    optionContent: {
      flex: 1,
    },
    optionLabel: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 2,
    },
    optionDescription: {
      fontSize: 14,
      opacity: 0.8,
    },
    checkmark: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: 12,
    },
    checkmarkText: {
      color: colors.white,
      fontSize: 14,
      fontWeight: 'bold',
    },
  });

export default createStyles;
