import React, {useState} from 'react';
import {View, Text, TouchableOpacity, Modal, Platform} from 'react-native';
import {RNCamera} from 'react-native-camera';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Svg, {Path} from 'react-native-svg';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {useTheme} from '~contexts/ThemeContext';

const isAndroid = Platform.OS === 'android';

const ScannerQRCode = ({user, event}) => {
  const {colors} = useTheme();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedData, setScannedData] = useState<object | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMessage, setModalMessage] = useState({title: '', message: '', isSuccess: false});

  const {top} = useSafeAreaInsets();

  const requestCameraPermission = async () => {
    const result = await check(isAndroid ? PERMISSIONS.ANDROID.CAMERA : PERMISSIONS.IOS.CAMERA);

    switch (result) {
      case RESULTS.UNAVAILABLE:
        setModalMessage({title: 'Error', message: 'Camera is not available on this device.', isSuccess: false});
        setModalVisible(true);
        return false;
      case RESULTS.DENIED:
        const requestResult = await request(isAndroid ? PERMISSIONS.ANDROID.CAMERA : PERMISSIONS.IOS.CAMERA);
        return requestResult === RESULTS.GRANTED;
      case RESULTS.GRANTED:
        return true;
      case RESULTS.BLOCKED:
        setModalMessage({
          title: 'Error',
          message: 'Camera permission is blocked. Please enable it in settings.',
          isSuccess: false,
        });
        setModalVisible(true);
        return false;
      default:
        return false;
    }
  };

  const handleBarCodeRead = scanEvent => {
    if (!isScanning) {
      return;
    } // Запобігання дублювання
    setIsScanning(false);
    try {
      const qrData = scanEvent.data.split('###');

      if (qrData.length == 3) {
        const uid = qrData[0];
        const eventId = qrData[1];

        if (user?.some(attendee => attendee.user.uid === uid) && eventId === event?.event_id + '') {
          const matchedUser = user.find(attendee => attendee.user.uid === uid).user;
          setModalMessage({
            title: 'Success!',
            message: `The user ${matchedUser.first_name} ${matchedUser.last_name} is registered for the event ${event?.name}.`,
            isSuccess: true,
          });
          setModalVisible(true);
        } else {
          setModalMessage({
            title: 'Error!',
            message: 'The ticket is not valid for this event.',
            isSuccess: false,
          });
          setModalVisible(true);
        }
      } else {
        setModalMessage({
          title: 'Error!',
          message: 'The ticket is not valid for this event.',
          isSuccess: false,
        });
      }

      setScannedData(qrData);
    } catch (error) {
      console.log('errorerrorerror', error);

      setModalMessage({
        title: 'Error!',
        message: 'Could not parse data from QR code',
        isSuccess: false,
      });
      setModalVisible(true);
    }
  };

  const startScanning = async () => {
    const hasPermission = await requestCameraPermission();
    if (hasPermission) {
      setIsScanning(true);
    }
  };

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <TouchableOpacity
        style={{
          position: 'absolute',
          right: 20,
          top: isAndroid ? top + 210 : top + 180,
          zIndex: 1000,
          paddingVertical: 8,
          backgroundColor: colors.white,
          height: 50,
          width: 50,
          borderRadius: 25,
          alignItems: 'center',
        }}
        onPress={startScanning}>
        <Svg width="34" height="34" viewBox="0 0 24 24" fill="none">
          <Path d="M11 3H13V21H11V3Z" fill={colors.black} />
          <Path
            d="M5 8C5 7.44771 5.44772 7 6 7H9V5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19H9V17H6C5.44772 17 5 16.5523 5 16V8Z"
            fill={colors.black}
          />
          <Path
            d="M19 8C19 7.44771 18.5523 7 18 7H15V5H18C19.6569 5 21 6.34315 21 8V16C21 17.6569 19.6569 19 18 19H15V17H18C18.5523 17 19 16.5523 19 16V8Z"
            fill={colors.black}
          />
        </Svg>
      </TouchableOpacity>

      <Modal visible={isScanning} transparent={true} animationType="fade" onRequestClose={() => setIsScanning(false)}>
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <RNCamera
            style={{flex: 1, width: '100%', justifyContent: 'center', alignItems: 'center'}}
            type={RNCamera.Constants.Type.back}
            flashMode={RNCamera.Constants.FlashMode.off}
            onBarCodeRead={handleBarCodeRead}
            captureAudio={false}>
            <View
              style={{
                flex: 0,
                width: 250,
                height: 250,
                borderWidth: 2,
                borderColor: colors.white,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent',
              }}>
              <Text style={{color: colors.white, fontSize: 16, textAlign: 'center'}}>
                Scan the Qr-code of your Guests
              </Text>
            </View>
          </RNCamera>
          <TouchableOpacity
            style={{
              backgroundColor: colors.black,
              borderRadius: 10,
              position: 'absolute',
              top: 50,
              right: 20,
              height: 30,
              width: 60,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => setIsScanning(false)}>
            <Text style={{color: colors.white, fontSize: 14, fontWeight: 'bold'}}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          onPress={() => setModalVisible(false)}
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              width: 300,
              padding: 20,
              borderRadius: 10,
              alignItems: 'center',
              backgroundColor: modalMessage.isSuccess ? colors.statusGreen + 'E6' : colors.error + 'E6',
            }}>
            <Text
              style={{
                fontSize: 20,
                fontWeight: 'bold',
                marginBottom: 10,
                color: colors.white,
              }}>
              {modalMessage.title}
            </Text>
            <Text
              style={{
                fontSize: 16,
                marginBottom: 20,
                color: colors.white,
                textAlign: 'center',
              }}>
              {modalMessage.message}
            </Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text
                style={{
                  fontSize: 16,
                  color: colors.white,
                  fontWeight: 'bold',
                }}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default ScannerQRCode;
