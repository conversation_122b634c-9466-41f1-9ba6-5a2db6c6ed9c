import {<PERSON>, Clip<PERSON><PERSON>, Defs, G, <PERSON>, Path, Rect, Svg} from 'react-native-svg';

const EyeOffIcon = () => (
  <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
    <G clip-path="url(#clip0_74_2730)">
      <Mask id="path-1-inside-1_74_2730" fill="white">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9 2.75C14.22 2.75 18 6.53 18 9C18 11.47 14.22 15.25 9 15.25C3.78 15.25 0 11.47 0 9C0 6.53 3.78 2.75 9 2.75ZM9 13C11.2091 13 13 11.2091 13 9C13 6.79086 11.2091 5 9 5C6.79086 5 5 6.79086 5 9C5 11.2091 6.79086 13 9 13Z"
        />
      </Mask>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 2.75C14.22 2.75 18 6.53 18 9C18 11.47 14.22 15.25 9 15.25C3.78 15.25 0 11.47 0 9C0 6.53 3.78 2.75 9 2.75ZM9 13C11.2091 13 13 11.2091 13 9C13 6.79086 11.2091 5 9 5C6.79086 5 5 6.79086 5 9C5 11.2091 6.79086 13 9 13Z"
        fill="#C7C7CC"
      />
      <Path d="M-1 -1L19 19" stroke="#C7C7CC" strokeWidth="2" fill="#C7C7CC" mask="url(#path-1-inside-1_74_2730)" />
      <Circle cx="9" cy="9" r="2.5" fill="#C7C7CC" />
      {/* Diagonal line to indicate "off" state */}
      <Path d="M2 2L16 16" stroke="#C7C7CC" strokeWidth="1.5" />
    </G>
    <Defs>
      <ClipPath id="clip0_74_2730">
        <Rect width="18" height="18" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default EyeOffIcon;
