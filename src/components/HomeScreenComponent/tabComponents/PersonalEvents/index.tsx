import {useCallback, useMemo, useState, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {useDebounce} from '~components/SearchBarWithAutocomplete';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {Event} from '~types/api/event';
import {EVENTS_TIMEFRAME, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {responsive, spacing} from '~constants';
import ModernEventsList from '../components/ModernEventsList';
import {useTheme} from '~contexts/ThemeContext';

const PersonalEvents = ({
  timeframe,
  isBusiness,
  scrollHandler,
  filterEvents,
  selectedTimeframe,
  globalInputValue,
  type,
  isNeighbourhood,
}: {
  timeframe: EVENTS_TIMEFRAME | null;
  isBusiness?: boolean;
  scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any;
  selectedTimeframe: {id: number; title: string};
  globalInputValue: string;
  type?: string;
  isNeighbourhood?: boolean;
}) => {
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const {colors} = useTheme();

  const {data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch} = useAllEvents({
    distance_km: 10000000,
    tab: TABS.MY_EVENTS,
    order_by: timeframe === EVENTS_TIMEFRAME.PAST ? ORDER_BY.END_DATE : ORDER_BY.CREATED_AT,
    order_dir: ORDER_DIR.DESC,
    event_age_group: null,
    q: isBusiness ? searchText : '',
    enabled: false,
    filter_my_event_type: type,
    isNeighbourhood: isNeighbourhood,
  });
  const {top} = useSafeAreaInsets();
  const {width, height} = useSafeAreaFrame();
  const {t} = useTranslation();

  const eventsList = getListFromPages<Event>(data);

  useEffect(() => {
    refetch();
  }, [type, isNeighbourhood]);

  useDebounce(
    () => {
      refetch();
    },
    300,
    [searchText, timeframe],
  );

  const renderFooter = () => {
    return isFetchingNextPage ? <></> : null;
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const contentLayout = useMemo(() => {
    const platformLayout = Platform.OS === 'ios' ? 115 : 125;
    return platformLayout + top;
  }, [top]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      refetch();
      setRefreshing(false);
    }, 2000);
  }, []);

  const filteredEvents = useMemo(() => {
    if (!eventsList) {
      return [];
    }
    const events = filterEvents(eventsList, selectedTimeframe);
    if (globalInputValue && events) {
      return events.filter(event => event.name.toLowerCase().includes(globalInputValue.toLowerCase()));
    }
    return events || [];
  }, [eventsList, selectedTimeframe, globalInputValue, filterEvents]);

  useEffect(() => {
    if (!isLoading && !isFetching && !isFetchingNextPage && eventsList?.length * 170 < height) {
      handleLoadMore();
    }
  }, [isLoading, isFetching, isFetchingNextPage, eventsList, handleLoadMore, height]);

  const content = useMemo(() => {
    if (isLoading || !eventsList?.length) {
      return <View style={{flex: 1, backgroundColor: colors.background}} />;
    }

    return (
      <ModernEventsList
        data={filteredEvents}
        isLoading={isLoading}
        isFetching={isFetching}
        isFetchingNextPage={isFetchingNextPage}
        refreshing={refreshing}
        onRefresh={onRefresh}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        isMyEvents={true}
        emptyStateTitle={t('home.empty_list_personal_events')}
        emptyStateSubtitle="Create your first event or join events from other organizers!"
        estimatedItemSize={280}
        contentContainerStyle={{
          paddingTop: 0, // Remove top padding since we have search bar
        }}
      />
    );
  }, [
    eventsList,
    handleLoadMore,
    isFetching,
    isFetchingNextPage,
    isLoading,
    t,
    filteredEvents,
    refreshing,
    onRefresh,
    colors.background,
  ]);

  return (
    <View
      style={{
        paddingTop: contentLayout,
        height: '100%',
        backgroundColor: colors.background,
      }}>
      {isBusiness && (
        <View
          style={{
            paddingHorizontal: responsive.getValue({xs: spacing.md, sm: spacing.lg, md: spacing.xl}, spacing.lg),
            paddingBottom: spacing.sm,
          }}>
          <TextInput
            style={{
              paddingVertical: spacing.md,
              paddingHorizontal: spacing.lg,
              backgroundColor: colors.surface,
              borderWidth: 1,
              borderColor: colors.border,
              borderRadius: responsive.getValue({xs: 8, sm: 10, md: 12}, 10),
              color: colors.textPrimary,
              fontSize: responsive.getValue({xs: 14, sm: 16, md: 18}, 16),
              shadowColor: colors.black,
              shadowOffset: {width: 0, height: 1},
              shadowOpacity: 0.05,
              shadowRadius: 2,
              elevation: 2,
            }}
            placeholder="Search by title"
            placeholderTextColor={colors.textTertiary}
            value={searchText}
            onChangeText={value => setSearchText(value)}
            returnKeyType="search"
          />
        </View>
      )}
      <View style={{flex: 1}}>{content}</View>
    </View>
  );
};

export default PersonalEvents;
