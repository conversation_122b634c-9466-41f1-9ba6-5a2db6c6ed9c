import React from 'react';
import {View, TouchableOpacity, Text} from 'react-native';
import Svg, {Path} from 'react-native-svg';
import {useTheme} from '~contexts/ThemeContext';

// Define the prop types
interface MapListViewProps {
  isMapView: boolean; // Prop to track whether it's map or list view
  toggleView: () => void; // Prop to toggle the view
}

const MapListView: React.FC<MapListViewProps> = ({isMapView, toggleView}) => {
  const {colors} = useTheme();

  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row', // Change to row to place text next to icon
        width: 'auto', // Change from fixed width to auto
        paddingHorizontal: 6, // Add horizontal padding
        borderRadius: 8,
        backgroundColor: colors.white,
        borderColor: colors.separatorLine,
        borderWidth: 1,
        height: 36,
        marginEnd: 8,
      }}>
      <TouchableOpacity
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row', // Change to row to place text next to icon
          height: '100%',
          paddingHorizontal: 4, // Add some padding between icon and text
        }}
        onPress={toggleView}>
        {!isMapView ? (
          <>
            <Svg width="22" height="22" viewBox="0 0 24 24" fill="none">
              <Path
                d="M12 6H12.01M9 20L3 17V4L5 5M9 20L15 17M9 20V14M15 17L21 20V7L19 6M15 17V14M15 6.2C15 7.96731 13.5 9.4 12 11C10.5 9.4 9 7.96731 9 6.2C9 4.43269 10.3431 3 12 3C13.6569 3 15 4.43269 15 6.2Z"
                stroke={colors.black}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
            <Text style={{marginLeft: 8, color: colors.black}}>Switch to Map</Text>
          </>
        ) : (
          <>
            <Svg width="22" height="22" viewBox="0 0 24 24" fill="none">
              <Path
                d="M8 6.00067L21 6.00139M8 12.0007L21 12.0015M8 18.0007L21 18.0015M3.5 6H3.51M3.5 12H3.51M3.5 18H3.51M4 6C4 6.27614 3.77614 6.5 3.5 6.5C3.22386 6.5 3 6.27614 3 6C3 5.72386 3.22386 5.5 3.5 5.5C3.77614 5.5 4 5.72386 4 6ZM4 12C4 12.2761 3.77614 12.5 3.5 12.5C3.22386 12.5 3 12.2761 3 12C3 11.7239 3.22386 11.5 3.5 11.5C3.77614 11.5 4 11.7239 4 12ZM4 18C4 18.2761 3.77614 18.5 3.5 18.5C3.22386 18.5 3 18.2761 3 18C3 17.7239 3.22386 17.5 3.5 17.5C3.77614 17.5 4 17.7239 4 18Z"
                stroke={colors.black}
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
            <Text style={{marginLeft: 8, color: colors.black}}>Switch to List</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default MapListView;
