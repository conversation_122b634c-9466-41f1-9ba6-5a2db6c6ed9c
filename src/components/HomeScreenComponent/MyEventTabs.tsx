import React, {useEffect, useLayoutEffect, useMemo, useState} from 'react';
import {
  Modal,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {FadeInRight} from 'react-native-reanimated';
import useHomeScreenAnimation from './useHomeScreenAnimation';
import HomeScreenHeader from './HomeScreenHeader';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useTranslation} from 'react-i18next';
import PersonalEvents from './tabComponents/PersonalEvents';
import {useGetUserType} from '~hooks/event/useGetUserType';
import auth from '@react-native-firebase/auth';
import Svg, {Path} from 'react-native-svg';
import Slider from '@react-native-community/slider';
import {useHomeStore} from '~providers/home/<USER>';
import {DATE_FORMAT, DateToggle} from '~components/DateTimeToggle';
import moment from 'moment';
import {widthPercentageToDP as wp, heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {Switch} from 'react-native-switch';
import MapListView from '~components/MapListView';
// import HomeScreenLocationButton from './HomeScreenLocationButton';
import {SCREENS} from '~constants';
import {FloatingPlusIcon} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';

const MyEventTabs = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {top} = useSafeAreaInsets();
  const [isMapView, setIsMapView] = useState<boolean>(true);

  const eventTypes = [
    {
      id: 1,
      title: t('events.type'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
        />
      ),
    },
    {
      id: 2,
      title: t('events.created'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
          type={'created'}
        />
      ),
    },
    {
      id: 3,
      title: t('events.attending'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
          type={'attending'}
        />
      ),
    },
    {
      id: 4,
      title: t('events.attended'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
          type={'attended'}
        />
      ),
    },
    {
      id: 5,
      title: t('events.liked'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
          type={'liked'}
        />
      ),
    },
    {
      id: 7,
      title: t('events.neighbourhood_gropus'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          isNeighbourhood={true}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
  ];

  const timeframes = [
    {
      id: 1,
      title: t('events.anytime'),
    },
    {
      id: 2,
      title: t('events.today'),
    },
    {
      id: 3,
      title: t('events.tomorrow'),
    },
    {
      id: 5,
      title: t('events.past'),
    },
    {
      id: 4,
      title: t('events.thisweekend'),
    },
  ];

  const {setIsTabBarDisabled} = useTabBar();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const navigation = useNavigation<NavigationProps>();
  const [selectedEventType, setSelectedEventType] = useState(eventTypes[0]);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframes[0]);
  const {scrollHandler, scrollAnimatedStyle, addEventButtonStyle} = useHomeScreenAnimation();
  const {radius, isForKids, setRadius, setIsForKids} = useHomeStore();

  // const [dynamicStartDate, setDynamicStartDate] = useState(moment(new Date(), DATE_FORMAT));
  // const [dynamicEndDate, setDynamicEndDate] = useState(moment(new Date(), DATE_FORMAT));

  const [dynamicStartDate, setDynamicStartDate] = useState(null);
  const [dynamicEndDate, setDynamicEndDate] = useState(null);

  const [globalInputValue, setGlobalInputValue] = useState('');

  // console.log('dynamicStartDate', dynamicStartDate);
  // console.log('dynamicEndDate', dynamicEndDate);
  // const [radius, setRadius] = useState(50);
  // const [isChildrenEvents, setChildrenEvents] = useState(isForKids);

  useLayoutEffect(() => {
    navigation.addListener('focus', e => {
      setIsTabBarDisabled(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filterEvents = (data, selectedTimeframe) => {
    const now = new Date();

    // Set today's start and end
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date(now);
    todayEnd.setHours(23, 59, 59, 999);

    // Set tomorrow's start and end
    const tomorrowStart = new Date(todayStart);
    tomorrowStart.setDate(todayStart.getDate() + 1);
    const tomorrowEnd = new Date(tomorrowStart);
    tomorrowEnd.setHours(23, 59, 59, 999);

    var curr = new Date(); // get current date
    var first = curr.getDate() - curr.getDay(); // First day is the day of the month - the day of the week
    var last = first + 6; // last day is the first day + 6

    const saturday = new Date(new Date(curr.setDate(last)).setHours(0, 0, 0, 0));
    var lastday = new Date(new Date(curr.setDate(last)).setHours(23, 59, 59, 999));
    const sunday = new Date(lastday.setDate(lastday.getDate() + 1));

    if (selectedTimeframe.title === t('events.now')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return startDate <= now && endDate >= now && startDate.getHours() <= now.getHours();
      });
    } else if (selectedTimeframe.title === t('events.anytime')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return endDate > now;
      });
    } else if (selectedTimeframe.title === t('events.today')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return startDate <= todayEnd && endDate >= todayStart;
      });
    } else if (selectedTimeframe.title === t('events.tomorrow')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return (
          (startDate >= tomorrowStart && startDate <= tomorrowEnd) ||
          (endDate >= tomorrowStart && endDate <= tomorrowEnd) ||
          (startDate <= tomorrowStart && endDate >= tomorrowEnd)
        );
      });
    } else if (selectedTimeframe.title === t('events.thisweekend')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return endDate > saturday && endDate < sunday;
      });
    } else if (selectedTimeframe.title === t('events.past')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        return endDate < now && endDate >= thirtyDaysAgo;
      });
    } else if (selectedTimeframe.id === 0) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return endDate > dynamicStartDate && endDate < dynamicEndDate;
      });
    } else {
      return [];
    }
  };

  interface FilterButtonsProps {
    setIsMapView: (value: boolean) => void;
    isMapView: boolean;
  }

  const FilterButtons = ({setIsMapView, isMapView}: FilterButtonsProps) => {
    const [isNowModalVisible, setIsNowModalVisible] = useState(false);
    const [isAllModalVisible, setIsAllModalVisible] = useState(false);
    const [startDateEnabled, setStartDateEnabled] = useState(false);
    const [endDateEnabled, setEndDateEnabled] = useState(false);
    const [tempSelectedTimeframe, setTempSelectedTimeframe] = useState(selectedTimeframe);
    const [tempSelectedEventType, setTempSelectedEventType] = useState(selectedEventType);

    const applyChanges = () => {
      setSelectedTimeframe(tempSelectedTimeframe);
      setSelectedEventType(tempSelectedEventType);
      setIsNowModalVisible(false);
      setIsAllModalVisible(false);
    };

    const toggleView = () => {
      setIsMapView(!isMapView);
      console.log('Toggling view to:', !isMapView);
    };

    return (
      <View style={{flexDirection: 'row', justifyContent: 'flex-start', marginLeft: hp('1.2%')}}>
        {/* Now Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: selectedTimeframe.title == t('events.anytime') ? colors.gray400 : colors.warning,
            borderRadius: 8,
            paddingHorizontal: hp('1.5%'),
            marginRight: hp('1%'),
            height: 36,
          }}
          onPress={() => {
            setTempSelectedTimeframe(selectedTimeframe);
            setIsNowModalVisible(true);
          }}>
          <Text style={{color: colors.white, marginRight: hp('1%'), fontSize: hp('2%')}}>
            {selectedTimeframe.title}
          </Text>
          <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 24 24" fill="none">
            <Path d="M7 10l5 5 5-5z" fill={colors.white} />
          </Svg>
        </TouchableOpacity>

        {/* All Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: selectedEventType.title == t('events.type') ? colors.gray400 : colors.warning,
            borderRadius: 8,
            paddingHorizontal: hp('1.25%'),
            height: 36,
          }}
          onPress={() => {
            setTempSelectedEventType(selectedEventType);
            setIsAllModalVisible(true);
          }}>
          <Text style={{color: colors.white, marginRight: hp('1%'), fontSize: hp('2%')}}>
            {selectedEventType.title}
          </Text>
          <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 24 24" fill="none">
            <Path d="M7 10l5 5 5-5z" fill={colors.white} />
          </Svg>
        </TouchableOpacity>

        <View style={{flex: 1}} />
        {false && <MapListView isMapView={isMapView} toggleView={toggleView} />}

        {/* Now Modal */}
        <Modal visible={isNowModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsNowModalVisible(false);
              applyChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2.5%'),
                height: hp('51%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('1%')}}>
                {t('events.lookingforevents')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                  marginBottom: hp('0.5%'),
                }}>
                {timeframes.map(timeframe => (
                  <TouchableOpacity
                    key={timeframe.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('2%'),
                      borderRadius: 20,
                      backgroundColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.black,
                      margin: hp('0.5%'),
                    }}
                    onPress={() => setTempSelectedTimeframe(timeframe)}>
                    <Text style={{color: tempSelectedTimeframe.id === timeframe.id ? colors.white : colors.black}}>
                      {timeframe.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Date Selection */}
              <View style={{width: '100%', marginTop: hp('1.5%')}}>
                <Text style={{fontWeight: 'bold', marginBottom: hp('2.55%'), fontSize: hp('1.7%')}}>
                  {t('events.dateselection')}
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: hp('0.5%'),
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                      <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                        <Path
                          d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                          fill={colors.white}
                        />
                      </Svg>
                    </View>
                    <Text style={{marginLeft: hp('1%')}}>{t('events.startdate')}</Text>
                  </View>
                  <DateToggle
                    label={t('events.startdate')}
                    date={dynamicStartDate}
                    onChangeDate={date => {
                      setDynamicStartDate(moment(date, DATE_FORMAT));
                      setTempSelectedTimeframe({id: 0, title: 'Selected Date'});
                    }}
                  />
                </View>
                <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('0%')}} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: hp('0.5%'),
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                      <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                        <Path
                          d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                          fill={colors.white}
                        />
                      </Svg>
                    </View>
                    <Text style={{marginLeft: hp('1%')}}>{t('events.enddate')}</Text>
                  </View>
                  <DateToggle
                    label={t('events.enddate')}
                    date={dynamicEndDate}
                    onChangeDate={date => setDynamicEndDate(moment(date, DATE_FORMAT))}
                  />
                </View>
                <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('1%')}} />
              </View>
              <TouchableOpacity
                onPress={applyChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>

        {/* All Modal */}
        <Modal visible={isAllModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsAllModalVisible(false);
              applyChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2%'),
                height: hp('35%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('2%')}}>
                {t('events.eventtype')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                {eventTypes.map(eventType => (
                  <TouchableOpacity
                    key={eventType.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('1.5%'),
                      borderRadius: 20,
                      backgroundColor:
                        tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.gray400,
                      margin: hp('0.5%'),
                      marginBottom: hp('1%'),
                    }}
                    onPress={() => setTempSelectedEventType(eventType)}>
                    <Text style={{color: tempSelectedEventType.id === eventType.id ? colors.white : colors.black}}>
                      {eventType.title === t('events.type') ? t('events.all') : eventType.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <TouchableOpacity
                onPress={applyChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1.5%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  const EventSearch = () => {
    const [isModalVisible, setModalVisible] = useState(false);
    const [startDateEnabled, setStartDateEnabled] = useState(false);
    const [endDateEnabled, setEndDateEnabled] = useState(false);
    const [tempSelectedTimeframe, setTempSelectedTimeframe] = useState(selectedTimeframe);
    const [tempSelectedEventType, setTempSelectedEventType] = useState(selectedEventType);
    const [isChildrenEventsLocal, setChildrenEventsLocal] = useState(isForKids);
    const [radiusLocal, setRadiusLocal] = useState(radius); // Встановіть початкове значення радіуса, наприклад, 50 км

    const [inputValue, setInputValue] = useState('');

    useEffect(() => {
      // Оновлюємо глобальну змінну при зміні inputValue
      setGlobalInputValue(inputValue);
      // console.log("Глобальне значення:", globalInputValue);
    }, [inputValue]);

    const applyChanges = () => {
      setSelectedTimeframe(tempSelectedTimeframe);
      setSelectedEventType(tempSelectedEventType);
      setIsForKids(isChildrenEventsLocal);
      setRadius(radiusLocal);
      setModalVisible(false);
    };

    const toggleModal = () => {
      setModalVisible(!isModalVisible);
    };

    return (
      <View style={{flex: 1, paddingHorizontal: 8}}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          {/* <HomeScreenLocationButton
            setSelectedTab={() => {
              setSelectedEventType(selectedEventType);
              setRadius(radius + 1);
            }}
          /> */}
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: hp('1.25%'),
              margin: hp('0.5%'),
              borderRadius: 10,
              backgroundColor: colors.white,
              borderColor: colors.separatorLine,
              borderWidth: 1,
            }}>
            <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 16 16" fill="none">
              <Path
                d="M6.50301 12.8767C7.89827 12.8767 9.18492 12.4285 10.2376 11.6814L14.1978 15.616C14.3816 15.7986 14.6239 15.8899 14.8829 15.8899C15.426 15.8899 15.8103 15.4749 15.8103 14.9436C15.8103 14.6946 15.7268 14.4539 15.543 14.2795L11.6078 10.3616C12.435 9.28247 12.9279 7.94604 12.9279 6.49341C12.9279 2.98218 10.0371 0.110107 6.50301 0.110107C2.97726 0.110107 0.078125 2.97388 0.078125 6.49341C0.078125 10.0046 2.96891 12.8767 6.50301 12.8767ZM6.50301 11.4988C3.74591 11.4988 1.46503 9.23267 1.46503 6.49341C1.46503 3.75415 3.74591 1.48804 6.50301 1.48804C9.26011 1.48804 11.541 3.75415 11.541 6.49341C11.541 9.23267 9.26011 11.4988 6.50301 11.4988Z"
                fill={colors.textPrimary}
                fillOpacity="0.6"
              />
            </Svg>
            <TextInput
              style={{flex: 1, fontSize: hp('2%'), color: colors.black, marginLeft: hp('1.5%'), height: 40}}
              placeholder="Search for events..."
              placeholderTextColor={colors.placeholderText}
              underlineColorAndroid="transparent"
              value={inputValue}
              onChangeText={text => setInputValue(text)}
            />
          </View>
          {false && (
            <View
              style={{
                alignItems: 'center',
                margin: hp('1%'),
                borderRadius: 10,
                backgroundColor: colors.white,
                borderColor: colors.separatorLine,
                borderWidth: 1,
                height: hp('5%'),
              }}>
              <TouchableOpacity
                style={{
                  padding: hp('2%'),
                  paddingTop: hp('1%'),
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={toggleModal}>
                <Svg width={hp('2.75%')} height={hp('4%')} viewBox="0 0 20 14" fill="none">
                  <Path
                    d="M0.428467 0.993789C0.428467 0.73022 0.528807 0.477446 0.707413 0.291074C0.886019 0.104703 1.12826 0 1.38085 0H18.5237C18.7763 0 19.0185 0.104703 19.1971 0.291074C19.3757 0.477446 19.4761 0.73022 19.4761 0.993789C19.4761 1.25736 19.3757 1.51013 19.1971 1.6965C19.0185 1.88288 18.7763 1.98758 18.5237 1.98758H1.38085C1.12826 1.98758 0.886019 1.88288 0.707413 1.6965C0.528807 1.51013 0.428467 1.25736 0.428467 0.993789ZM4.23799 6.95652C4.23799 6.69295 4.33833 6.44018 4.51694 6.25381C4.69554 6.06744 4.93778 5.96273 5.19037 5.96273H14.7142C14.9668 5.96273 15.209 6.06744 15.3876 6.25381C15.5662 6.44018 15.6666 6.69295 15.6666 6.95652C15.6666 7.22009 15.5662 7.47287 15.3876 7.65924C15.209 7.84561 14.9668 7.95031 14.7142 7.95031H5.19037C4.93778 7.95031 4.69554 7.84561 4.51694 7.65924C4.33833 7.47287 4.23799 7.22009 4.23799 6.95652ZM8.04751 11.9255C7.79493 11.9255 7.55269 12.0302 7.37408 12.2165C7.19547 12.4029 7.09513 12.6557 7.09513 12.9193C7.09513 13.1828 7.19547 13.4356 7.37408 13.622C7.55269 13.8083 7.79493 13.913 8.04751 13.913H11.857C12.1096 13.913 12.3519 13.8083 12.5305 13.622C12.7091 13.4356 12.8094 13.1828 12.8094 12.9193C12.8094 12.6557 12.7091 12.4029 12.5305 12.2165C12.3519 12.0302 12.1096 11.9255 11.857 11.9255H8.04751Z"
                    fill={colors.textSecondary}
                  />
                </Svg>
              </TouchableOpacity>
            </View>
          )}

          <Pressable
            style={e => {
              return {
                width: 40,
                height: 40,
                marginLeft: 5,
                borderRadius: 8,
                alignItems: 'center',
                alignContent: 'center',
                justifyContent: 'center',
                backgroundColor: e.pressed ? colors.warning : colors.secondary,
              };
            }}
            onPress={() => {
              setIsTabBarDisabled(true);
              navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE);
            }}>
            <FloatingPlusIcon />
          </Pressable>
        </View>

        <Modal animationType="fade" transparent={true} visible={isModalVisible} onRequestClose={toggleModal}>
          <TouchableOpacity
            onPress={() => {
              setModalVisible(false);
              applyChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              alignItems: 'center',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                padding: hp('2%'),
                height: hp('57%'),
              }}>
              <Text
                style={{
                  fontSize: hp('2.75%'),
                  marginBottom: hp('1.75%'),
                  fontWeight: 'bold',
                }}>
                Filters
              </Text>
              <View
                style={{
                  width: '100%',
                  backgroundColor: colors.white,
                  borderTopLeftRadius: 10,
                  borderTopRightRadius: 10,
                }}>
                {false && (
                  <>
                    <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('1%')}}>
                      {t('events.lookingforevents')}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        width: '100%',
                        alignItems: 'flex-start',
                      }}>
                      {timeframes.map(timeframe => (
                        <TouchableOpacity
                          key={timeframe.id}
                          style={{
                            paddingVertical: hp('1%'),
                            paddingHorizontal: hp('1.5%'),
                            borderRadius: 20,
                            backgroundColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.white,
                            borderWidth: 1,
                            borderColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.black,
                            margin: hp('0.5%'),
                          }}
                          onPress={() => setTempSelectedTimeframe(timeframe)}>
                          <Text
                            style={{color: tempSelectedTimeframe.id === timeframe.id ? colors.white : colors.black}}>
                            {timeframe.title}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </>
                )}

                {/* Date Selection */}
                <View style={{width: '100%', marginTop: hp('1.5%')}}>
                  <Text style={{fontWeight: 'bold', marginBottom: hp('1%'), fontSize: hp('2%')}}>
                    {t('events.dateselection')}
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: hp('0.25%'),
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                        <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                          <Path
                            d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                            fill={colors.white}
                          />
                        </Svg>
                      </View>
                      <Text style={{marginLeft: hp('0.75%')}}>{t('events.startdate')}</Text>
                    </View>
                    <DateToggle
                      label={t('events.startdate')}
                      date={dynamicStartDate}
                      onChangeDate={date => {
                        setDynamicStartDate(moment(date, DATE_FORMAT));
                        setTempSelectedTimeframe({id: 0, title: 'Selected Date'});
                      }}
                    />
                  </View>
                  <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('0.35%')}} />
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: hp('0.25%'),
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                        <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                          <Path
                            d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                            fill={colors.white}
                          />
                        </Svg>
                      </View>
                      <Text style={{marginLeft: hp('0.75%')}}>{t('events.enddate')}</Text>
                    </View>
                    <DateToggle
                      label={t('events.enddate')}
                      date={dynamicEndDate}
                      onChangeDate={date => setDynamicEndDate(moment(date, DATE_FORMAT))}
                    />
                  </View>
                  <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('1.25%')}} />
                </View>

                {false && (
                  <View
                    style={{
                      width: '100%',
                      backgroundColor: colors.white,
                      borderTopLeftRadius: 10,
                      borderTopRightRadius: 10,
                    }}>
                    <Text style={{fontSize: hp('2%'), fontWeight: 'bold', marginBottom: hp('0.75%')}}>
                      {t('events.eventtype')}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        justifyContent: 'space-between',
                        width: '100%',
                        alignItems: 'flex-start',
                      }}>
                      {eventTypes.map(eventType => (
                        <TouchableOpacity
                          key={eventType.id}
                          style={{
                            paddingVertical: hp('1%'),
                            paddingHorizontal: hp('2%'),
                            borderRadius: 20,
                            backgroundColor: tempSelectedEventType.id === eventType.id ? colors.gray400 : colors.white,
                            borderWidth: 1,
                            borderColor: tempSelectedEventType.id === eventType.id ? colors.gray400 : colors.gray400,
                            marginBottom: hp('0.5%'),
                            marginTop: hp('0.75%'),
                          }}
                          onPress={() => setTempSelectedEventType(eventType)}>
                          <Text
                            style={{color: tempSelectedEventType.id === eventType.id ? colors.white : colors.black}}>
                            {eventType.title === t('events.type') ? t('events.all') : eventType.title}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                )}

                <Text
                  style={{
                    fontSize: hp('2%'),
                    marginBottom: 0,
                    marginTop: hp('0.75%'),
                    fontWeight: 'bold',
                  }}>
                  More
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                    marginVertical: hp('1%'),
                    marginTop: hp('0.25%'),
                  }}>
                  <Text style={{fontSize: hp('1.75%')}}>Children events</Text>
                  <Switch
                    value={isChildrenEventsLocal}
                    onValueChange={setChildrenEventsLocal}
                    backgroundActive={colors.statusGreen}
                    backgroundInactive={colors.gray400 + '29'}
                    renderActiveText={false}
                    renderInActiveText={false}
                    circleBorderWidth={0}
                    circleSize={hp('2.5%')}
                    switchWidthMultiplier={2.2}
                    barHeight={hp('3%')}
                  />
                </View>
                <View style={{height: 1, backgroundColor: colors.separatorLine, marginVertical: hp('0.5%')}} />
                <View
                  style={{
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    width: '100%',
                    marginVertical: hp('0.5%'),
                  }}>
                  <Text
                    style={{
                      fontSize: hp('2%'),
                      marginBottom: hp('0.25%'),
                      fontWeight: 'bold',
                    }}>
                    Radius
                  </Text>
                  <Text style={{fontSize: hp('1.5%'), color: colors.textSecondary, marginBottom: hp('0.15%')}}>
                    {radiusLocal} km
                  </Text>
                  <Slider
                    style={{width: '100%', height: hp('4%')}}
                    minimumValue={0}
                    maximumValue={1000}
                    minimumTrackTintColor={colors.statusGreen}
                    maximumTrackTintColor={colors.border}
                    step={1}
                    value={radiusLocal}
                    onValueChange={setRadiusLocal}
                  />
                </View>
                <TouchableOpacity
                  onPress={applyChanges}
                  style={{
                    backgroundColor: colors.secondary,
                    paddingVertical: hp('1.25%'),
                    paddingHorizontal: hp('5%'),
                    borderRadius: 25,
                    width: '100%',
                    alignItems: 'center',
                    marginBottom: hp('1%'),
                  }}>
                  <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  const selectedEventTypeData = eventTypes.find(item => item.id === selectedEventType.id);
  // const selectedTimeframeData = timeframes.find(item => item.id === selectedTimeframe.id);

  const listComponent = useMemo(() => {
    const EventTypeComponent = selectedEventTypeData?.component(
      scrollHandler,
      filterEvents,
      selectedTimeframe,
      globalInputValue,
    );
    // const TimeframeComponent = selectedTimeframeData?.component(scrollHandler);
    // console.log(selectedTimeframe);
    return (
      <>
        {/* {TimeframeComponent} */}
        {EventTypeComponent}
      </>
    );
  }, [scrollHandler, selectedEventType, selectedTimeframe]);

  const header = useMemo(() => {
    return (
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            overflow: 'hidden',
            zIndex: 300,
            paddingTop: Platform.OS === 'ios' ? top : 10,
            backgroundColor: 'FCF9ED',
          },
          scrollAnimatedStyle,
        ]}>
        {false && (
          <HomeScreenHeader
            setSelectedTab={() => {
              setSelectedEventType(selectedEventType);
              setRadius(radius + 1);
            }}
          />
        )}
        <EventSearch />
        <View style={{height: 8}} />
        <FilterButtons setIsMapView={setIsMapView} isMapView={isMapView} />
      </Animated.View>
    );
  }, [scrollAnimatedStyle, top, selectedEventType, selectedTimeframe, radius, isForKids]);

  return (
    <>
      <Animated.View
        entering={FadeInRight.duration(300)}
        style={{
          zIndex: 10000,
          position: 'absolute',
          bottom: hp('15%'),
          right: hp('2.5%'),
          backgroundColor: colors.background,
        }}
      />
      <View style={{flex: 1, backgroundColor: colors.background}}>
        {header}
        <Animated.View style={[{flex: 1, backgroundColor: colors.background}]}>{listComponent}</Animated.View>
      </View>
    </>
  );
};

export default MyEventTabs;
