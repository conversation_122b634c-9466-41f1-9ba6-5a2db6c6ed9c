import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetUserGroupsResponse} from '~types/api/user';
import auth from '@react-native-firebase/auth';

export function useGetUserGroups() {
  return useQuery<GetUserGroupsResponse, Error>('userGroups', async () => {
    const token = await FirebaseAuth.getAuthToken();

    const response = await fetch(`${Config.BASE_API_URL}users/${auth().currentUser!.uid}/groups`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user groups');
    }

    const data = await response.json();

    return data as GetUserGroupsResponse;
  });
}
