import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const UserInterestsList = ({options}: {options: string[]}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const styles = StyleSheet.create({
    vStack: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    button: {
      padding: 12,
      marginRight: 16,
      marginTop: 4,
      marginBottom: 8,
      borderRadius: 100,
      borderWidth: 1,
      borderColor: colors.textSecondary,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.eventInfluencer,
    },
  });

  const optionsList = useMemo(
    () =>
      options?.map((item, index) => (
        <TouchableOpacity style={styles.button} disabled={true} key={index.toString()}>
          <Text style={styles.buttonText}>{t(`subcategories.${item}`)}</Text>
        </TouchableOpacity>
      )),
    [options, t, styles],
  );

  return <View style={styles.vStack}>{optionsList}</View>;
};

export default UserInterestsList;
