import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import ProgressDots from '~components/ProgressDots';
import {SCREENS} from '~constants';
import {useCreateBusiness} from '~hooks/business/useCreateBusiness';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useHandleLogOut} from '../hooks/useHandleLogOut';
import createStyles from './styles';
import React from 'react';
import CheckBox from '@react-native-community/checkbox';
import {useTheme} from '~contexts/ThemeContext';
interface IBox {
  id: number;
  title: string;
  imageUrl: any;
  sizes: number;
  selectedItems: number[];
  setSelectedItems: (value: number[]) => void;
}
const BoxComponent = ({title, imageUrl, sizes, selectedItems, setSelectedItems, id}: IBox) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const onPress = () => {
    if (setSelectedItems) {
      selectedItems.find(s => s === id)
        ? setSelectedItems([...selectedItems.filter(selectedGroup => selectedGroup !== id)])
        : setSelectedItems([...selectedItems, id]);
    }
  };
  return (
    <TouchableOpacity
      style={[
        styles.box,

        {
          borderColor: selectedItems.find(val => val === id) ? colors.statusPurple : colors.border,
          borderWidth: 1.5,
        },
      ]}
      onPress={onPress}>
      <FastImage style={{width: '80%', height: `${80 / sizes}%`, top: 5}} source={imageUrl} />
      <View style={{flex: 1, paddingBottom: 10, justifyContent: 'flex-end'}}>
        <Text style={styles.text}>{title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const SelectTypeOfProfile = ({
  isModal,
  onSubmit,
  withoutLabel = false,
  selectedGroups,
  isFromSettings,
}: {
  isModal?: boolean;
  onSubmit?: (values: number[]) => void;
  withoutLabel?: boolean;
  selectedGroups?: number[];
  isFromSettings?: boolean;
}) => {
  const {width} = useSafeAreaFrame();
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.ONBOARDING_GROUP>>();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {setPersonalGroups, personalOnboarding, businessOnboarding} = useOnboardingStore();
  const {user} = useUserStore();
  const {handleLogOut} = useHandleLogOut();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const {mutateAsync} = useCreateBusiness();
  const [selectedItems, setSelectedItems] = useState<number[]>(selectedGroups || personalOnboarding.groups || []);

  const boxes = useMemo(
    () => [
      {
        title: t('onboarding.groups_2'),
        imageUrl: require('~assets/images/studentsImage.jpg'),
        sizes: 0.9615,
        id: 2,
      },
      {
        title: t('onboarding.groups_3'),
        imageUrl: require('~assets/images/professionalsImage.jpg'),
        sizes: 1.093,
        id: 3,
      },
      {
        title: t('onboarding.groups_1'),
        imageUrl: require('~assets/images/familiesImage.jpg'),
        sizes: 1.01,
        id: 1,
      },
      {
        title: t('onboarding.groups_4'),
        imageUrl: require('~assets/images/seniorsImage.jpg'),
        sizes: 1.101,
        id: 4,
      },
      {
        title: t('onboarding.groups_5'),
        imageUrl: require('~assets/images/couplesImage.jpg'),
        sizes: 1,
        id: 5,
      },
    ],
    [],
  );

  const submit = async () => {
    setIsLoading(true);
    if (params?.isBusiness) {
      try {
        if (businessOnboarding.photo[0] != 'h') {
          await storage()
            .ref('users/' + auth().currentUser!.uid + '/profile.png')
            .putFile(businessOnboarding.photo);
        }

        const urlPhoto =
          businessOnboarding.photo[0] == 'h'
            ? auth().currentUser?.photoURL
            : await storage()
                .ref('users/' + auth().currentUser!.uid + '/profile.png')
                .getDownloadURL();

        await mutateAsync({
          ...businessOnboarding,
          photo: urlPhoto,
          email: auth().currentUser!.email!,
          target_audiences: selectedItems,
          uid: auth().currentUser!.uid,
          is_registration_finished: true,
        });
        return;
      } catch (e) {
        setIsLoading(false);
        console.error('Hello', e);
        return;
      }
    }
    setPersonalGroups(selectedItems);
    selectedItems.find(group => group === 1)
      ? navigation.navigate(SCREENS.ONBOARDING_CHILDREN)
      : navigation.navigate(SCREENS.ONBOARDING_SUBCATEGORIES, {isBusiness: false});
  };

  const {bottom} = useSafeAreaInsets();

  return (
    <>
      {!onSubmit && (
        <GoBackHeader
          customCallback={!user?.is_registration_finished ? handleLogOut : undefined}
          customText={!user?.is_registration_finished ? t('settings.sign_out') : undefined}
        />
      )}
      <View style={{flex: 1}}>
        <TouchableOpacity
          style={{flexDirection: 'row', alignItems: 'center', position: 'absolute', right: 20, top: -40}}
          onPress={() => {
            if (selectedItems.length === boxes.length) {
              setSelectedItems([]);
            } else {
              setSelectedItems(boxes.map(group => group.id));
            }
          }}>
          <CheckBox
            value={selectedItems.length === boxes.length} // Check if all items are selected
            onValueChange={newValue => {
              if (newValue) {
                setSelectedItems(boxes.map(group => group.id)); // Select all
              } else {
                setSelectedItems([]); // Deselect all
              }
            }}
            style={{
              width: 20,
              height: 20,
              marginRight: 5,
            }}
            tintColors={{true: colors.statusBlue, false: colors.textPrimary}}
            boxType="square"
          />
          <Text style={{fontSize: 16, color: colors.textPrimary}}>{t('onboarding.select_all')}</Text>
        </TouchableOpacity>
        <ScrollView
          contentContainerStyle={{paddingBottom: 100}}
          bounces={false}
          style={[styles.container, {paddingTop: isModal ? 0 : 100, backgroundColor: colors.background}]}>
          {!withoutLabel && (
            <Text style={{fontWeight: '600', fontSize: 18, color: colors.textPrimary, marginBottom: 24}}>
              {t('onboarding.related_groups')}
            </Text>
          )}

          <View style={styles.row}>
            <BoxComponent
              id={boxes[0].id}
              title={boxes[0].title}
              imageUrl={boxes[0].imageUrl}
              sizes={boxes[0].sizes}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
            <BoxComponent
              id={boxes[1].id}
              title={boxes[1].title}
              imageUrl={boxes[1].imageUrl}
              sizes={boxes[1].sizes}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
          </View>
          <View style={styles.row}>
            <BoxComponent
              id={boxes[2].id}
              title={boxes[2].title}
              imageUrl={boxes[2].imageUrl}
              sizes={boxes[2].sizes}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
            <BoxComponent
              id={boxes[3].id}
              title={boxes[3].title}
              imageUrl={boxes[3].imageUrl}
              sizes={boxes[3].sizes}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
          </View>
          <View style={styles.row}>
            <View style={{width: '25%'}} />
            <BoxComponent
              id={boxes[4].id}
              title={boxes[4].title}
              imageUrl={boxes[4].imageUrl}
              sizes={boxes[4].sizes}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
            />
            <View style={{width: '25%'}} />
          </View>

          {params?.isBusiness && (
            <TouchableOpacity
              style={{
                width: '100%',
                height: 40,
                borderRadius: 6,
                borderWidth: 1.25,
                marginTop: 10,
                borderColor: colors.border,
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onPress={() => {
                setSelectedItems(boxes.map(group => group.id));
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontWeight: '500',
                  color: colors.textPrimary,
                }}>
                {t('onboarding.select_all')}
              </Text>
            </TouchableOpacity>
          )}
        </ScrollView>
        <View style={{width: width, justifyContent: 'flex-end'}}>
          <View
            style={{
              width: '100%',
              paddingHorizontal: 25,
              paddingTop: 16,
              borderTopWidth: isModal ? 1 : 0,
              borderTopColor: colors.border,
              justifyContent: 'flex-end',
              marginBottom: Platform.OS === 'android' ? 20 : bottom,
            }}>
            <Button
              disabled={!selectedItems?.length}
              label={params?.isBusiness ? t('generic.submit') : t('generic.continue')}
              isLoading={isLoading}
              onPress={() => {
                if (isModal && !!onSubmit) {
                  onSubmit(selectedItems || []);
                  return;
                }
                submit();
              }}
              textStyle={{
                fontSize: 15,
                fontWeight: '500',
                color: colors.white,
              }}
              containerStyle={{
                width: '100%',
                height: 40,
                borderRadius: 6,
                backgroundColor: !selectedItems?.length ? colors.gray100 : colors.primary,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 24,
              }}
            />
            {!isFromSettings && (
              <ProgressDots
                dotsNumber={isModal ? 4 : params?.isBusiness ? 3 : 6}
                selectedDotNumber={isModal ? 4 : params?.isBusiness ? 3 : 2}
              />
            )}
          </View>
        </View>
      </View>
    </>
  );
};

export default SelectTypeOfProfile;
