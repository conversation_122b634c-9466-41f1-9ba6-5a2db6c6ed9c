import {GestureDetector, GestureHandlerRootView, ScrollView} from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import useBottomSheetAnimations from './hooks/useBottomSheetAnimations';
import {View} from 'react-native';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

interface BottomSheetProps {
  children?: React.ReactNode;
  button?: JSX.Element;
  onPositionChange?: (position: 'open' | 'closed' | 'moving') => void;
}

const BottomSheet = ({children, button, onPositionChange}: BottomSheetProps) => {
  const {gesture, animatedStyles, onScrollHandler, scrollRef} = useBottomSheetAnimations(onPositionChange);

  return (
    <>
      <Animated.View style={animatedStyles.sheet}>
        {button && <View style={{position: 'absolute', top: 0, right: 0, zIndex: 1000}}>{button}</View>}
        <GestureHandlerRootView>
          <GestureDetector gesture={gesture}>
            <AnimatedScrollView
              ref={scrollRef}
              showsVerticalScrollIndicator={false}
              scrollEventThrottle={1}
              onScroll={onScrollHandler}
              animatedProps={animatedStyles.scrollViewProps}>
              {children}
            </AnimatedScrollView>
          </GestureDetector>
        </GestureHandlerRootView>
      </Animated.View>
    </>
  );
};

export default BottomSheet;
