import React, {useEffect} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {onboardingScreens} from '../screens';
import {useUserStore} from '~providers/userStore/zustand';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';

const Stack = createNativeStackNavigator();

const OnboardingScreens = () => {
  const {user} = useUserStore();
  const {navigate} = useNavigation<NavigationProps>();

  useEffect(() => {
    console.log('🔄 [DEBUG] OnboardingScreens - User data:', {
      user,
      hasUser: !!user,
      isRegistrationFinished: user?.is_registration_finished,
      hasOnboardingAnswers: user?.onboarding_answers?.length || 0,
      onboardingAnswers: user?.onboarding_answers,
    });

    if (!user) {
      console.log('📱 [DEBUG] OnboardingScreens - No user data, waiting...');
      return;
    }
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      console.log('📱 [DEBUG] OnboardingScreens - No onboarding answers, navigating to EDIT_PREFERANCE');
      navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
      return;
    } else if (!user?.is_registration_finished) {
      console.log('📱 [DEBUG] OnboardingScreens - Registration not finished');
      if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
        console.log('📱 [DEBUG] OnboardingScreens - No onboarding answers (2nd check), navigating to EDIT_PREFERANCE');
        navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        return;
      } else {
        if (!user.photo) {
          console.log('📱 [DEBUG] OnboardingScreens - No photo, navigating to ONBOARDING_PERSONAL_INFO');
          navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
          return;
        } else {
          console.log('📱 [DEBUG] OnboardingScreens - Has photo, navigating to ONBOARDING_GROUP');
          navigate(SCREENS.ONBOARDING_GROUP);
        }
      }
    } else {
      console.log(
        '📱 [DEBUG] OnboardingScreens - Registration finished and has onboarding answers, should not be here!',
      );
    }
  }, [user, navigate]);

  return (
    <Stack.Navigator initialRouteName={user ? SCREENS.ONBOARDING_GROUP : undefined}>
      {onboardingScreens.map(screen => {
        return (
          <Stack.Screen key={screen.name} name={screen.name} component={screen.component} options={screen.options} />
        );
      })}
    </Stack.Navigator>
  );
};

export default OnboardingScreens;
