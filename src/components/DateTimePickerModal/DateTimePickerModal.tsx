import {FC, useCallback, useState} from 'react';
import {CustomModal} from '../CustomModal';
import {DatePicker} from '~components/DatePicker';
import {Text, TouchableOpacity, View} from 'react-native';
import getStyles from './styles';
import {ModalHeight} from '../../types/modal';
import moment from 'moment-timezone';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
  minDate: moment.Moment | null;
  maxDate: moment.Moment | null;
  handleChooseMinDate: (date?: Date) => void;
  handleChooseMaxDate: (date?: Date) => void;
}

const DateTimePickerModal: FC<Props> = ({
  isVisible,
  onClose,
  onSubmit,
  handleChooseMinDate,
  handleChooseMaxDate,
  minDate,
  maxDate,
}) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const [selectedRange, setSelectedRange] = useState<'from' | 'to'>('from');

  const {t} = useTranslation();

  const handleSelectRange = useCallback((value: 'from' | 'to') => () => setSelectedRange(value), []);

  return (
    <CustomModal
      height={ModalHeight.MIDDLE}
      onCloseModal={onClose}
      modalIsVisible={isVisible}
      backgroundColor={colors.white}>
      <View style={styles.container}>
        <Text style={styles.title}>{t('generic.select_range')}</Text>
        <View style={styles.datePickerContainer}>
          <Text style={styles.label}>From</Text>
          <TouchableOpacity
            onPress={handleSelectRange('from')}
            style={[styles.buttonContainer, selectedRange === 'from' && styles.selectedButton]}>
            <Text style={[styles.buttonLabel, {color: minDate ? colors.textPrimary : colors.textSecondary}]}>
              {minDate?.format('DD.MM.YY') || 'Date'}
            </Text>
          </TouchableOpacity>
          <Text style={styles.label}>To</Text>
          <TouchableOpacity
            onPress={handleSelectRange('to')}
            style={[styles.buttonContainer, selectedRange === 'to' && styles.selectedButton]}>
            <Text style={[styles.buttonLabel, {color: maxDate ? colors.textPrimary : colors.textSecondary}]}>
              {maxDate?.format('DD.MM.YY') || 'Date'}
            </Text>
          </TouchableOpacity>
        </View>
        <DatePicker
          type={selectedRange}
          minDate={minDate}
          maxDate={maxDate}
          handleChooseMaxDate={handleChooseMaxDate}
          handleChooseMinDate={handleChooseMinDate}
        />
        <TouchableOpacity style={styles.doneButton} onPress={onSubmit}>
          <Text style={styles.doneButtonLabel}>Done</Text>
        </TouchableOpacity>
      </View>
    </CustomModal>
  );
};

export default DateTimePickerModal;
