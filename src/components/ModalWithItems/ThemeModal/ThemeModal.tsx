import React from 'react';
import {Modal, View, Text, TouchableOpacity, SafeAreaView} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {ModernCloseIcon} from '~assets/icons';
import createStyles from './styles';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const ThemeModal: React.FC<ThemeModalProps> = ({isVisible, onClose}) => {
  const {colors, themeMode, setThemeMode} = useTheme();
  const {t} = useTranslation();
  const styles = createStyles(colors);

  const themeOptions: {mode: ThemeMode; label: string; description: string}[] = [
    {
      mode: 'light',
      label: t('settings.theme_light') || 'Light',
      description: t('settings.theme_light_desc') || 'Use light theme for a bright, clean interface',
    },
    {
      mode: 'dark',
      label: t('settings.theme_dark') || 'Dark',
      description: t('settings.theme_dark_desc') || 'Use dark theme for a comfortable viewing experience in low light',
    },
    {
      mode: 'system',
      label: t('settings.theme_system') || 'System',
      description: t('settings.theme_system_desc') || "Automatically match your device's system theme setting",
    },
  ];

  const handleThemeSelect = (mode: ThemeMode) => {
    setThemeMode(mode);
    onClose();
  };

  const getThemeDisplayName = (mode: ThemeMode) => {
    const option = themeOptions.find(opt => opt.mode === mode);
    return option?.label || mode;
  };

  return (
    <Modal visible={isVisible} animationType="slide" presentationStyle="pageSheet" onRequestClose={onClose}>
      <SafeAreaView style={[styles.container, {backgroundColor: colors.background}]}>
        <View style={[styles.header, {borderBottomColor: colors.border}]}>
          <Text style={[styles.headerTitle, {color: colors.textPrimary}]}>
            {t('settings.appearance') || 'Appearance'}
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <ModernCloseIcon size={24} color={colors.textSecondary} variant="minimal" />
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={[styles.sectionTitle, {color: colors.textPrimary}]}>
            {t('settings.theme_selection') || 'Theme Selection'}
          </Text>

          {themeOptions.map(option => (
            <TouchableOpacity
              key={option.mode}
              style={[
                styles.optionItem,
                {
                  backgroundColor: themeMode === option.mode ? colors.primary + '15' : colors.surface,
                  borderColor: themeMode === option.mode ? colors.primary : colors.border,
                },
              ]}
              onPress={() => handleThemeSelect(option.mode)}>
              <View style={styles.optionContent}>
                <Text style={[styles.optionLabel, {color: colors.textPrimary}]}>{option.label}</Text>
                <Text style={[styles.optionDescription, {color: colors.textSecondary}]}>{option.description}</Text>
              </View>
              {themeMode === option.mode && (
                <View style={[styles.checkmark, {backgroundColor: colors.primary}]}>
                  <Text style={styles.checkmarkText}>✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default ThemeModal;
