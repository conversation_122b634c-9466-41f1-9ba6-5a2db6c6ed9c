import React, {useEffect, useRef} from 'react';
import {Dimensions, ViewStyle} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
  cancelAnimation,
} from 'react-native-reanimated';
import {useNavigation} from '@react-navigation/native';
import {haptics} from '~utils/haptics';
import {useAnimationCleanup} from '~utils/viewCleanup';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface SwipeBackGestureProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
  style?: ViewStyle;
}

export const SwipeBackGesture: React.FC<SwipeBackGestureProps> = ({
  children,
  enabled = true,
  threshold = SCREEN_WIDTH * 0.3,
  style,
}) => {
  const navigation = useNavigation();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(translateX, opacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
    opacity: opacity.value,
  }));

  // Simplified version without gesture handlers to avoid Reanimated v3 issues
  // TODO: Implement with new Gesture API when needed
  return <Animated.View style={[{flex: 1}, style, animatedStyle]}>{children}</Animated.View>;
};

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => void;
  refreshing?: boolean;
  threshold?: number;
  style?: ViewStyle;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  refreshing = false,
  threshold = 80,
  style,
}) => {
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(translateY, scale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateY: translateY.value}, {scale: scale.value}],
  }));

  // Simplified version without gesture handlers to avoid Reanimated v3 issues
  // TODO: Implement with new Gesture API when needed
  return <Animated.View style={[{flex: 1}, style, animatedStyle]}>{children}</Animated.View>;
};

interface SwipeToActionProps {
  children: React.ReactNode;
  leftAction?: {
    onAction: () => void;
    color: string;
    icon?: React.ReactNode;
    threshold?: number;
  };
  rightAction?: {
    onAction: () => void;
    color: string;
    icon?: React.ReactNode;
    threshold?: number;
  };
  style?: ViewStyle;
}

export const SwipeToAction: React.FC<SwipeToActionProps> = ({children, leftAction, rightAction, style}) => {
  const translateX = useSharedValue(0);
  const leftActionOpacity = useSharedValue(0);
  const rightActionOpacity = useSharedValue(0);
  const isMountedRef = useAnimationCleanup(translateX, leftActionOpacity, rightActionOpacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
  }));

  const leftActionStyle = useAnimatedStyle(() => ({
    opacity: leftActionOpacity.value,
  }));

  const rightActionStyle = useAnimatedStyle(() => ({
    opacity: rightActionOpacity.value,
  }));

  // Simplified version without gesture handlers to avoid Reanimated v3 issues
  // TODO: Implement with new Gesture API when needed
  return (
    <Animated.View style={[{position: 'relative'}, style]}>
      {/* Left Action Background */}
      {leftAction && (
        <Animated.View
          style={[
            {
              position: 'absolute',
              left: 0,
              top: 0,
              bottom: 0,
              width: 100,
              backgroundColor: leftAction.color,
              justifyContent: 'center',
              alignItems: 'center',
            },
            leftActionStyle,
          ]}>
          {leftAction.icon}
        </Animated.View>
      )}

      {/* Right Action Background */}
      {rightAction && (
        <Animated.View
          style={[
            {
              position: 'absolute',
              right: 0,
              top: 0,
              bottom: 0,
              width: 100,
              backgroundColor: rightAction.color,
              justifyContent: 'center',
              alignItems: 'center',
            },
            rightActionStyle,
          ]}>
          {rightAction.icon}
        </Animated.View>
      )}

      {/* Main Content */}
      <Animated.View style={animatedStyle}>{children}</Animated.View>
    </Animated.View>
  );
};

export default {
  SwipeBackGesture,
  PullToRefresh,
  SwipeToAction,
};
