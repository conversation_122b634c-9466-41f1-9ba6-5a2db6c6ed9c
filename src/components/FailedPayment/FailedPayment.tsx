import React from 'react';
import {Modal, View, Text, StyleSheet, TouchableOpacity, Image, Dimensions} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const {width, height} = Dimensions.get('window');

interface FailedPaymentProps {
  isVisible: boolean;
  onRetry: () => void;
  onClose: () => void;
}

const FailedPayment: React.FC<FailedPaymentProps> = ({isVisible, onRetry, onClose}) => {
  const {colors} = useTheme();

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: colors.overlayBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    container: {
      width: width * 0.9,
      backgroundColor: colors.white,
      borderRadius: 20,
      padding: 20,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    failedIcon: {
      width: 80,
      height: 80,
      marginBottom: 20,
      tintColor: colors.eventInfluencer,
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      color: colors.black,
      marginBottom: 10,
    },
    message: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: 20,
      paddingHorizontal: 10,
    },
    retryButton: {
      width: '100%',
      backgroundColor: colors.eventInfluencer,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
      marginBottom: 15,
    },
    retryText: {
      color: colors.white,
      fontSize: 18,
      fontWeight: 'bold',
    },
    closeButton: {
      width: '100%',
      backgroundColor: colors.gray100,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
    },
    closeText: {
      color: colors.textPrimary,
      fontSize: 16,
    },
  });

  return (
    <Modal animationType="slide" transparent={true} visible={isVisible} onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.container}>
          {/* Failed Icon */}
          <Image
            source={require('~assets/icons/failed-icon.png')} // Replace with your failed icon
            style={styles.failedIcon}
          />

          {/* Failed Text */}
          <Text style={styles.title}>Payment Failed</Text>
          <Text style={styles.message}>Something went wrong while processing your payment. Please try again.</Text>

          {/* Retry Button */}
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>

          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default FailedPayment;
