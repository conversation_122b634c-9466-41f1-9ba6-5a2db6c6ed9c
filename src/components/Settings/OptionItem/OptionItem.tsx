import {Image, Text, TouchableOpacity, View} from 'react-native';
import {OptionItemProps} from '~types/settings/settings.type';
import {Switch} from 'react-native-switch';
import {useNotifications} from '~containers/Core/navigation/SettingsStack/zustand';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography} from '~constants/design';

const OptionItem = ({name, icon, subName, callback, isSwitch}: OptionItemProps) => {
  const {colors} = useTheme();
  const {isNotificationsEnabled, setIsNotificationsEnabled} = useNotifications();

  // Modern styles without background/borders (since ModernCard handles that)
  const modernStyles = {
    container: {
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
    wrapper: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'flex-start' as const,
    },
    nameText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textPrimary,
      marginLeft: spacing.md,
      flex: 1,
    },
    subTitleWrapper: {
      alignItems: 'flex-end' as const,
      justifyContent: 'center' as const,
    },
    subTitleText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.normal,
      color: colors.textSecondary,
      textTransform: 'capitalize' as const,
    },
  };

  return (
    <TouchableOpacity onPress={callback} style={modernStyles.container} disabled={isSwitch} activeOpacity={0.7}>
      <View style={modernStyles.wrapper}>
        {typeof icon === 'function' ? icon() : <Image source={icon} style={{width: 30, height: 30}} />}
        <Text style={modernStyles.nameText}>{name}</Text>
        <View style={modernStyles.subTitleWrapper}>
          {isSwitch ? (
            <Switch
              value={isNotificationsEnabled}
              onValueChange={value => setIsNotificationsEnabled(value)}
              backgroundActive={colors.primary}
              backgroundInactive={colors.border}
              renderActiveText={false}
              renderInActiveText={false}
              circleBorderWidth={0}
              circleSize={24}
              switchWidthMultiplier={2}
              barHeight={28}
            />
          ) : (
            <Text style={modernStyles.subTitleText}>{subName}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default OptionItem;
