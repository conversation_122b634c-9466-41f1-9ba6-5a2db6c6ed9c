import {StyleSheet} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {width: '100%', aspectRatio: 1.3},
    wrapper: {flex: 1, alignItems: 'flex-start', justifyContent: 'flex-end', paddingLeft: 8, paddingBottom: 8},
    titleText: {color: colors.white, fontSize: 24, fontWeight: '700'},
    subTitleText: {color: colors.white, fontWeight: '500'},
    buttonWrapper: {position: 'absolute', bottom: 8, right: 8},
  });

export default getStyles;
