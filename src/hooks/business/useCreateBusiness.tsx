import Config from 'react-native-config';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import FirebaseChatsService from '~services/FirebaseChats';
import {Business} from '~types/api/business';

export function useCreateBusiness() {
  const queryClient = useQueryClient();

  const mutation = useMutation(
    async (businessData: Omit<Business, 'created_at' | 'updated_at'>) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}businesses/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(businessData),
      });

      if (!response.ok) {
        throw new Error('Failed to create business user');
      }

      const data = await response.json();

      await FirebaseChatsService.createPyxiConciergeChat({
        user_id: data.uid,
        user_image: data.photo,
        user_name: data.name,
      });

      return data;
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(['businessAccount']);
      },
    },
  );

  return mutation;
}
