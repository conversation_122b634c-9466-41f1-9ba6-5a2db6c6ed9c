import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useEffect, useLayoutEffect, useMemo, useState, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, StatusBar, Text, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getTimestamp} from '~Utils/Time';
import {getLastMessage} from '~Utils/chat';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {ChevronIcon, ChatIcon, PlusIcon} from '~assets/icons';
import Button from '~components/Button';
import {ModernChatList, ModernChatHeader} from '~components/Chat';
import {ThemedStatusBar} from '~components/ThemedStatusBar/ThemedStatusBar';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import {
  ChatType,
  ChatTypeWithKey,
  ConciergeChatMessageType,
  ConciergeChatType,
  ConciergeChatTypeWithKey,
} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {haptics} from '~utils/haptics';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';

export default function Chat() {
  const {colors} = useTheme();
  const uid = auth().currentUser?.uid;
  const [chats, setChats] = useState<ChatTypeWithKey[]>([]);
  // const [loading, setLoading] = useState(true);

  const [sortedByTypeChats, setSortedByTypeChats] = useState<(ConciergeChatTypeWithKey | ChatTypeWithKey)[]>([]);
  const [chatWithConcierge, setChatWithConcierge] = useState<ConciergeChatTypeWithKey>();
  const [broadcastMessages, setBroadcastMessages] = useState([]);
  const [oneToOneMessages, setOneToOneMessages] = useState<ConciergeChatMessageType[]>([]);
  const [contactChat, setContactChat] = useState<ChatTypeWithKey | undefined>();
  // const [adminChats, setAdminChats] = useState<ConciergeChatTypeWithKey[]>([]);
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {t} = useTranslation();

  const {data: userAccount} = useGetUserAccount(auth().currentUser?.uid);
  const {data: hostBusiness} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');

  useLayoutEffect(() => {
    navigation.addListener('focus', () => {
      setIsTabBarDisabled(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2' && hostBusiness?.uid && userAccount?.uid) {
      FirebaseChatsService.createContactUsChat({
        user_id1: auth().currentUser!.uid + '',
        user_id2: hostBusiness?.uid + '',
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: hostBusiness?.name || 'Pyxi',
      });
    }
  }, [uid, hostBusiness, userAccount]);

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
      const unsubscribe = firestore()
        .collection('chats')
        .where('userIds', 'array-contains', uid)
        .onSnapshot(querySnapshot => {
          const conversations: ChatTypeWithKey[] = [];
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data() as ChatType; // Cast Firestore data to our ChatData type
            conversations.push({
              ...data,
              key: documentSnapshot.id,
            });
          });
          // Sort chats by timestamp of the last message
          const sortedConversations =
            conversations?.sort((a, b) => {
              const timestampA = getTimestamp(a.history);
              const timestampB = getTimestamp(b.history);

              return timestampB - timestampA; // For descending order
            }) || [];

          const supportChat = sortedConversations.filter(chat => chat.type === 'contact-pyxi');
          if (supportChat && supportChat.length > 0) {
            console.log(supportChat, 'supportChat');

            setContactChat(supportChat[0]);
          }
          setChats([...sortedConversations]);
        });

      // Clean up subscription on unmount
      return () => unsubscribe();
    }
  }, [uid]);

  useEffect(() => {
    logScreenView('Chat', 'ChatView');
  }, []);

  useEffect(() => {
    const oneOnOneUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('userId', '==', uid)
      .where('type', '==', 'one-on-one')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data() as ConciergeChatType;
          if (data) {
            const sortedMessages = [...broadcastMessages, ...data.messages].sort((a, b) => {
              return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
            });
            setChatWithConcierge({
              ...data,
              key: documentSnapshot.id,
              messages: [...sortedMessages],
            });
            setOneToOneMessages(data.messages);
          }
        });
      });

    const broadcastUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('type', '==', 'broadcast')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data();
          setBroadcastMessages(data.messages);
        });
      });

    return () => {
      oneOnOneUnsubscribe();
      broadcastUnsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const sortedMessages = [...oneToOneMessages, ...broadcastMessages].sort((a, b) => {
      return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
    });
    setChatWithConcierge(prev => {
      if (prev) {
        return {...prev, messages: [...sortedMessages]};
      }
    });
  }, [oneToOneMessages, broadcastMessages]);

  useEffect(() => {
    // Show all chats (no filtering by type since we removed tabs)
    let allChats = [...chats];

    // Add concierge chat if it exists
    if (chatWithConcierge) {
      allChats = [chatWithConcierge, ...allChats];
    }

    // Sort by timestamp (most recent first)
    const sortedChats = allChats.sort((a, b) => {
      const timestampA = getTimestamp(a.history);
      const timestampB = getTimestamp(b.history);
      return timestampB - timestampA;
    });

    setSortedByTypeChats(sortedChats);
  }, [chats, chatWithConcierge, uid]);

  const handleRefreshChats = useCallback(async () => {
    // Refresh logic can be added here if needed
    haptics.light();
  }, []);

  const modernChatsList = useMemo(() => {
    return (
      <ModernChatList
        chats={sortedByTypeChats}
        onRefresh={handleRefreshChats}
        emptyStateTitle={t('chat.no_conversations')}
        emptyStateSubtitle={t('chat.start_conversation')}
      />
    );
  }, [sortedByTypeChats, handleRefreshChats, t]);

  const handleChatWithPyxi = () => {
    if (contactChat) {
      const interlocutorId = contactChat?.userIds?.find(userId => userId !== uid);
      const interlocutorMessage = contactChat?.history?.find(message => message.sender_id === interlocutorId);
      const interlocutorImage = interlocutorMessage?.sender_image;

      const image = contactChat?.eventImage || interlocutorImage;
      const userIndex = contactChat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
      const userName = contactChat?.users?.[userIndex];

      navigation.navigate(SCREENS.USER_CHAT, {chatId: contactChat?.key, image: image, userName: userName});
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ThemedStatusBar />

      {/* Modern Header */}
      <ModernChatHeader userName={t('chat.conversations')} showCallButtons={false} showMoreButton={false} />

      {/* Support Chat Quick Access */}
      {contactChat && (
        <FadeIn delay={100} duration={300}>
          <View
            style={{
              marginHorizontal: spacing.md,
              marginTop: spacing.md,
              marginBottom: spacing.sm,
            }}>
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary,
                borderRadius: borderRadius.xl,
                padding: spacing.md,
                flexDirection: 'row',
                alignItems: 'center',
                ...shadows.md,
              }}
              onPress={handleChatWithPyxi}
              activeOpacity={0.8}>
              <View
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: borderRadius.full,
                  backgroundColor: colors.white,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: spacing.md,
                }}>
                <ChatIcon color={colors.primary} />
              </View>

              <View style={{flex: 1}}>
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    fontWeight: typography.fontWeight.semibold,
                    color: colors.white,
                    marginBottom: 2,
                  }}>
                  {t('chat.support_chat')}
                </Text>
                <Text
                  style={{
                    fontSize: typography.fontSize.sm,
                    color: colors.white,
                    opacity: 0.9,
                  }}>
                  {t('chat.get_help_instantly')}
                </Text>
              </View>

              <ChevronIcon color={colors.white} style={{transform: [{rotate: '180deg'}]}} />
            </TouchableOpacity>
          </View>
        </FadeIn>
      )}

      {/* Modern Chat List */}
      {modernChatsList}
    </View>
  );
}
