import {PaginatedResponse} from '~types/api';
import {Event} from '~types/api/event';

export enum TABS {
  DISCOVER = 'discover',
  PERSONAL_EVENTS = 'personal_events',
  SUGGESTIONS = 'suggestions',
  MY_EVENTS = 'my_events',
  SUBSCRIBED_EVENTS = 'subscribed_events',
  USER_SPECIFIC_EVENT = 'user_specific_event',
  USER_SPECIFIC_GOING_EVENT = 'user_specific_going_event',
  NEIGHBOURHOOD_EVENT = 'neighbourhood_event',
}

export enum ORDER_BY {
  START_DATE = 'start_date',
  CREATED_AT = 'created_at',
  EVENT_ID = 'event_id',
  DISTANCE = 'distance',
  END_DATE = 'end_date',
}

export enum ORDER_DIR {
  ASC = 'asc',
  DESC = 'desc',
}

export enum EVENTS_TIMEFRAME {
  PAST = 'past',
  FUTURE = 'future',
}

export enum EVENT_AGE_GROUP {
  CHILDREN = 'children',
  ADULTS = 'adults',
}

export interface GetEventsParams {
  tab: TABS | string;
  limit: number;
  offset: number;
  order_by: ORDER_BY;
  order_dir: ORDER_DIR;
  distance_km: number;
  event_age_group: EVENT_AGE_GROUP | null;
  is_for_kids?: boolean;
  q?: string;
  filter_my_event_type?: string;
  filter_type?: string;
  user_id?: string;
  timeframe?: string;
  isNeighbourhood?: boolean;
}

export type PaginatedEventsList = PaginatedResponse<Event>;
