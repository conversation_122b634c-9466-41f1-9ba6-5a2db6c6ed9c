import {useMemo} from 'react';
import {DimensionValue, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal/dist/modal';
import Animated from 'react-native-reanimated';

import getStyles from './styles';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Directions, ModalHeight} from '~types/modal';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  children: React.ReactNode;
  backgroundColor?: string;
  height: ModalHeight | DimensionValue;
  onCloseModal: () => void;
  modalIsVisible?: boolean;
  isOverflowHidden?: boolean;
}

const CustomModal = ({
  children,
  height = ModalHeight.MIDDLE,
  onCloseModal,
  modalIsVisible = false,
  backgroundColor,
  isOverflowHidden = true,
}: IProps) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const isModalOnFullScreen = height === ModalHeight.MAX;
  const swipeDirections = !isModalOnFullScreen ? [Directions.DOWN] : [];

  const slider = useMemo(
    () => !isModalOnFullScreen && <TouchableOpacity onPress={onCloseModal} style={styles.slider} />,
    [isModalOnFullScreen, onCloseModal],
  );
  return (
    <Modal
      propagateSwipe={true}
      isVisible={modalIsVisible}
      onSwipeComplete={onCloseModal}
      swipeDirection={swipeDirections}
      style={[styles.modal, {overflow: isOverflowHidden ? 'hidden' : 'visible'}]}>
      <SafeAreaView edges={isModalOnFullScreen ? [] : ['left', 'right']} style={styles.safeAreaContainer}>
        <Animated.View
          style={[styles.modalContent, {height, backgroundColor, paddingTop: isModalOnFullScreen ? 0 : 4}]}>
          {slider}
          {children}
        </Animated.View>
      </SafeAreaView>
    </Modal>
  );
};

export default CustomModal;
