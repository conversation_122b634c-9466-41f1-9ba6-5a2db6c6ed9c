import React, {useEffect, useLayoutEffect, useMemo, useState} from 'react';
import {
  DeviceEventEmitter,
  Modal,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ScrollView,
  Keyboard,
} from 'react-native';
import {TouchableWithoutFeedback} from 'react-native';
import HomeContent from './tabComponents/HomeContent';
import Animated, {FadeInRight} from 'react-native-reanimated';
import useHomeScreenAnimation from './useHomeScreenAnimation';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import UserEvents from './tabComponents/UserEvents';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useTranslation} from 'react-i18next';
import BusinessEvents from './tabComponents/BusinessEvents';
import {useGetUserType} from '~hooks/event/useGetUserType';
import auth from '@react-native-firebase/auth';
import Svg from 'react-native-svg';
import {Path} from 'react-native-svg';
import Slider from '@react-native-community/slider';
import {useHomeStore} from '~providers/home/<USER>';
import LikedContened from './tabComponents/LikedContent';
import {DATE_FORMAT, DateToggle} from '~components/DateTimeToggle';
import moment from 'moment';
import {widthPercentageToDP as wp, heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {Switch} from 'react-native-switch';
import OneSignal from 'react-native-onesignal';
import MapListView from '~components/MapListView';
// import HomeScreenLocationButton from './HomeScreenLocationButton';
import FloatingPlusIcon from '~assets/icons/FloatingPlusIcon';
import {Event} from '~types/api/event';
import EventDetailsBottomSheet from './tabComponents/HomeContent/EventDetailsBottomSheet';
import Config from 'react-native-config';
import {useTheme} from '~contexts/ThemeContext';
import ModernHomeHeader from './ModernHomeHeader';
import FullScreenFilterModal from './FullScreenFilterModal';

interface FilterButtonsProps {
  setIsMapView: (value: boolean) => void;
  isMapView: boolean;
}

const HomeScreenTabs = () => {
  const {t} = useTranslation();
  const {colors} = useTheme();

  const [globalInputValue, setGlobalInputValue] = useState('');
  const [isMapView, setIsMapView] = useState<boolean>(true);

  const eventTypes = [
    {
      id: 1,
      title: t('home.all_events'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <HomeContent
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
    {
      id: 2,
      title: t('events.users'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <UserEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
    {
      id: 3,
      title: t('events.suggestions'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <BusinessEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
    /* {
      id: 4,
      title: 'My events',
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
      ) => (
        <PersonalEvents
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          timeframe={null}
        />
      ),
    }, */
    {
      id: 5,
      title: t('events.liked'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <LikedContened
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ), // Update this as needed
    },
    {
      id: 6,
      title: t('events.free'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <HomeContent
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          isFree={true}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
    {
      id: 7,
      title: t('events.neighbourhood_gropus'),
      component: (
        scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void,
        filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any,
        selectedTimeframe: {id: number; title: string},
        globalInputValue: string,
        isMapView: boolean,
        setIsMapView: (value: boolean) => void,
      ) => (
        <HomeContent
          scrollHandler={scrollHandler}
          filterEvents={filterEvents}
          isNeighbourhood={true}
          selectedTimeframe={selectedTimeframe}
          globalInputValue={globalInputValue}
          isMapView={isMapView}
          setIsMapView={setIsMapView}
        />
      ),
    },
  ];

  const timeframes = [
    {
      id: 1,
      title: t('home.upcoming_events'),
    },
    {
      id: 2,
      title: t('events.today'),
    },
    {
      id: 3,
      title: t('events.tomorrow'),
    },
    {
      id: 4,
      title: t('events.this_week'),
    },
    {
      id: 5,
      title: t('events.thisweekend'),
    },
    {
      id: 6,
      title: t('events.next_week'),
    },
    {
      id: 7,
      title: t('events.past'),
    },
  ];

  const {top} = useSafeAreaInsets();

  const {setIsTabBarDisabled} = useTabBar();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const navigation = useNavigation<NavigationProps>();
  const [selectedEventType, setSelectedEventType] = useState(eventTypes[0]);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframes[0]);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  // Filter modal states
  const [isFullScreenFilterVisible, setIsFullScreenFilterVisible] = useState(false);
  const [isTimeframeModalVisible, setIsTimeframeModalVisible] = useState(false);
  const [isEventTypeModalVisible, setIsEventTypeModalVisible] = useState(false);
  const {radius, isForKids, setRadius, setIsForKids} = useHomeStore();
  const [tempSelectedTimeframe, setTempSelectedTimeframe] = useState(selectedTimeframe);
  const [tempSelectedEventType, setTempSelectedEventType] = useState(selectedEventType);
  const [isChildrenEventsLocal, setChildrenEventsLocal] = useState(isForKids);
  const [radiusLocal, setRadiusLocal] = useState(radius);
  const {scrollHandler, scrollAnimatedStyle, addEventButtonStyle} = useHomeScreenAnimation();

  // const [dynamicStartDate, setDynamicStartDate] = useState(moment(new Date(), DATE_FORMAT));
  // const [dynamicEndDate, setDynamicEndDate] = useState(moment(new Date(), DATE_FORMAT));

  const [dynamicStartDate, setDynamicStartDate] = useState<moment.Moment | null>(null);
  const [dynamicEndDate, setDynamicEndDate] = useState<moment.Moment | null>(null);

  // console.log('dynamicStartDate', dynamicStartDate);
  // console.log('dynamicEndDate', dynamicEndDate);
  // const [radius, setRadius] = useState(50);
  // const [isChildrenEvents, setChildrenEvents] = useState(isForKids);

  useLayoutEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      console.log('Focus event triggered, selectedEvent:', selectedEvent);
      if (!selectedEvent) {
        console.log('setIsTabBarDisabled false');
        setIsTabBarDisabled(false);
      }
    });

    // Cleanup the listener on unmount or when selectedEvent changes
    return () => {
      focusListener();
    };
  }, [selectedEvent, navigation, setIsTabBarDisabled]);

  useEffect(() => {
    // Setting External User Id with Callback Available in SDK Version 3.9.3+
    console.log(auth().currentUser!.uid, 'auth().currentUser!.uid');

    OneSignal.setExternalUserId(auth().currentUser!.uid, (results: any) => {
      // The results will contain push and email success statuses
      console.log('Results of setting external user id');
      console.log(results);
      // Push can be expected in almost every situation with a success status, but
      // as a pre-caution its good to verify it exists
      if (results.push && results.push.success) {
        console.log('Results of setting external user id push status:');
        console.log(results.push.success);
      }
      // Verify the email is set or check that the results have an email success status
      if (results.email && results.email.success) {
        console.log('Results of setting external user id email status:');
        console.log(results.email.success);
      }
      // Verify the number is set or check that the results have an sms success status
      if (results.sms && results.sms.success) {
        console.log('Results of setting external user id sms status:');
        console.log(results.sms.success);
      }
    });

    DeviceEventEmitter.addListener('eventSelection', data => {
      setSelectedEvent(data.event);
    });

    return () => {
      DeviceEventEmitter.removeAllListeners('eventSelection');
    };
  }, []);

  const filterEvents = (data, selectedTimeframe) => {
    const now = new Date();

    // Set today's start and end
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date(now);
    todayEnd.setHours(23, 59, 59, 999);

    // Set tomorrow's start and end
    const tomorrowStart = new Date(todayStart);
    tomorrowStart.setDate(todayStart.getDate() + 1);
    const tomorrowEnd = new Date(tomorrowStart);
    tomorrowEnd.setHours(23, 59, 59, 999);

    var curr = new Date(); // get current date
    var first = curr.getDate() - curr.getDay(); // First day is the day of the month - the day of the week
    var last = first + 6; // last day is the first day + 6

    const saturday = new Date(new Date(curr.setDate(last)).setHours(0, 0, 0, 0));
    var lastday = new Date(new Date(curr.setDate(last)).setHours(23, 59, 59, 999));
    const sunday = new Date(lastday.setDate(lastday.getDate() + 1));

    if (selectedTimeframe.title === t('events.now')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return startDate <= now && endDate >= now && startDate.getHours() <= now.getHours();
      });
    } else if (selectedTimeframe.title === t('home.upcoming_events')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return endDate > now;
      });
    } else if (selectedTimeframe.title === t('events.today')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return startDate <= todayEnd && endDate >= todayStart;
      });
    } else if (selectedTimeframe.title === t('events.tomorrow')) {
      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);
        return (
          (startDate >= tomorrowStart && startDate <= tomorrowEnd) ||
          (endDate >= tomorrowStart && endDate <= tomorrowEnd) ||
          (startDate <= tomorrowStart && endDate >= tomorrowEnd)
        );
      });
    } else if (selectedTimeframe.title === t('events.thisweekend')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return endDate > saturday && endDate < sunday;
      });
    } else if (selectedTimeframe.title === t('events.this_week')) {
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)
      startOfWeek.setHours(0, 0, 0, 0);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // End of current week (Saturday)
      endOfWeek.setHours(23, 59, 59, 999);

      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        return startDate >= startOfWeek && startDate <= endOfWeek;
      });
    } else if (selectedTimeframe.title === t('events.weekend')) {
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)

      const saturday = new Date(startOfWeek);
      saturday.setDate(startOfWeek.getDate() + 6); // Saturday
      saturday.setHours(0, 0, 0, 0);

      const sunday = new Date(startOfWeek);
      sunday.setDate(startOfWeek.getDate() + 7); // Next Sunday
      sunday.setHours(23, 59, 59, 999);

      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        return startDate >= saturday && startDate <= sunday;
      });
    } else if (selectedTimeframe.title === t('events.next_week')) {
      const startOfNextWeek = new Date(now);
      startOfNextWeek.setDate(now.getDate() - now.getDay() + 7); // Start of next week (Sunday)
      startOfNextWeek.setHours(0, 0, 0, 0);

      const endOfNextWeek = new Date(startOfNextWeek);
      endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // End of next week (Saturday)
      endOfNextWeek.setHours(23, 59, 59, 999);

      return data?.filter(event => {
        const startDate = new Date(event.start_date);
        return startDate >= startOfNextWeek && startDate <= endOfNextWeek;
      });
    } else if (selectedTimeframe.title === t('events.past')) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        return endDate < now && endDate >= thirtyDaysAgo;
      });
    } else if (selectedTimeframe.id === 0) {
      return data?.filter(event => {
        const endDate = new Date(event.end_date);
        return (
          dynamicStartDate && dynamicEndDate && endDate > dynamicStartDate.toDate() && endDate < dynamicEndDate.toDate()
        );
      });
    } else {
      return [];
    }
  };

  const FilterButtons = ({setIsMapView, isMapView}: FilterButtonsProps) => {
    const [isNowModalVisible, setIsNowModalVisible] = useState(false);
    const [isAllModalVisible, setIsAllModalVisible] = useState(false);
    const [startDateEnabled, setStartDateEnabled] = useState(false);
    const [endDateEnabled, setEndDateEnabled] = useState(false);
    const [tempSelectedTimeframe, setTempSelectedTimeframe] = useState(selectedTimeframe);
    const [tempSelectedEventType, setTempSelectedEventType] = useState(selectedEventType);
    const [isChildrenEventsLocal, setChildrenEventsLocal] = useState(isForKids);
    const [radiusLocal, setRadiusLocal] = useState(radius);

    const toggleView = () => {
      setIsMapView(!isMapView);
      console.log('Toggling view to:', !isMapView);
    };

    const applyChanges = () => {
      setSelectedTimeframe(tempSelectedTimeframe);
      setSelectedEventType(tempSelectedEventType);
      setIsNowModalVisible(false);
      setRadius(radiusLocal);
      setIsAllModalVisible(false);
      setIsForKids(isChildrenEventsLocal);
    };

    return (
      <View style={{flexDirection: 'row', justifyContent: 'flex-start', marginLeft: hp('1.2%')}}>
        {/* Now Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: selectedTimeframe.title == t('home.upcoming_events') ? colors.gray400 : colors.warning,
            borderRadius: 8,
            paddingHorizontal: hp('1%'),
            marginRight: hp('1%'),
            height: 36,
            maxWidth: '29%',
          }}
          onPress={() => {
            setTempSelectedTimeframe(selectedTimeframe);
            setRadiusLocal(radius);
            setChildrenEventsLocal(isForKids);
            setIsNowModalVisible(true);
          }}>
          <Text
            numberOfLines={1}
            style={{color: colors.white, flexShrink: 1, fontSize: hp('1.8%'), fontWeight: '500'}}
            adjustsFontSizeToFit={true}>
            {selectedTimeframe.title}
          </Text>
          <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 24 24" fill="none">
            <Path d="M7 10l5 5 5-5z" fill={colors.white} />
          </Svg>
        </TouchableOpacity>

        {/* All Button */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: selectedEventType.title == t('events.type') ? colors.gray400 : colors.warning,
            borderRadius: 8,
            paddingHorizontal: hp('1%'),
            height: 36,
            maxWidth: '29%',
          }}
          onPress={() => {
            setTempSelectedEventType(selectedEventType);
            setIsAllModalVisible(true);
          }}>
          <Text
            numberOfLines={1}
            style={{color: colors.white, flexShrink: 1, fontSize: hp('1.8%'), fontWeight: '500'}}
            adjustsFontSizeToFit={true}>
            {selectedEventType.title}{' '}
          </Text>
          <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 24 24" fill="none">
            <Path d="M7 10l5 5 5-5z" fill={colors.white} />
          </Svg>
        </TouchableOpacity>

        <View style={{flex: 1}} />

        <MapListView isMapView={isMapView} toggleView={toggleView} />

        {/* Now Modal */}
        <Modal visible={isNowModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsNowModalVisible(false);
              applyChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2.5%'),
                height: hp('71%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('1%')}}>
                {t('events.lookingforevents')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                  marginBottom: hp('0.5%'),
                }}>
                {timeframes.map(timeframe => (
                  <TouchableOpacity
                    key={timeframe.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('2%'),
                      borderRadius: 20,
                      backgroundColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.black,
                      margin: hp('0.5%'),
                    }}
                    onPress={() => setTempSelectedTimeframe(timeframe)}>
                    <Text
                      style={{
                        color: tempSelectedTimeframe.id === timeframe.id ? colors.white : colors.black,
                        fontSize: hp('1.8%'),
                        fontWeight: '500',
                      }}>
                      {timeframe.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Date Selection */}
              <View style={{width: '100%', marginTop: hp('1.5%')}}>
                <Text style={{fontWeight: 'bold', marginBottom: hp('2.55%'), fontSize: hp('1.7%')}}>
                  {t('events.dateselection')}
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: hp('0.5%'),
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                      <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                        <Path
                          d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                          fill={colors.white}
                        />
                      </Svg>
                    </View>
                    <Text style={{marginLeft: hp('1%')}}>{t('events.startdate')}</Text>
                  </View>
                  <DateToggle
                    label={t('events.startdate')}
                    date={dynamicStartDate || moment()}
                    timeFrame={selectedTimeframe.id}
                    onChangeDate={date => {
                      setDynamicStartDate(moment(date, DATE_FORMAT));
                      setTempSelectedTimeframe({id: 0, title: 'Selected Date'});
                    }}
                  />
                </View>
                <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('0%')}} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: hp('0.5%'),
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <View style={{backgroundColor: colors.statusGray, padding: hp('1.25%'), borderRadius: 15}}>
                      <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 18 16" fill="none">
                        <Path
                          d="M3.33057 15.6602H14.6694C16.4043 15.6602 17.2676 14.7969 17.2676 13.0869V2.95166C17.2676 1.2417 16.4043 0.378418 14.6694 0.378418H3.33057C1.5957 0.378418 0.724121 1.2334 0.724121 2.95166V13.0869C0.724121 14.8052 1.5957 15.6602 3.33057 15.6602ZM3.20605 14.3237C2.46729 14.3237 2.06055 13.9336 2.06055 13.1616V5.33398C2.06055 4.57031 2.46729 4.17188 3.20605 4.17188H14.7773C15.5161 4.17188 15.9312 4.57031 15.9312 5.33398V13.1616C15.9312 13.9336 15.5161 14.3237 14.7773 14.3237H3.20605ZM7.38135 7.15186H7.87109C8.16162 7.15186 8.25293 7.06885 8.25293 6.77832V6.28857C8.25293 5.99805 8.16162 5.90674 7.87109 5.90674H7.38135C7.09082 5.90674 6.99121 5.99805 6.99121 6.28857V6.77832C6.99121 7.06885 7.09082 7.15186 7.38135 7.15186ZM10.1372 7.15186H10.627C10.9175 7.15186 11.0171 7.06885 11.0171 6.77832V6.28857C11.0171 5.99805 10.9175 5.90674 10.627 5.90674H10.1372C9.84668 5.90674 9.74707 5.99805 9.74707 6.28857V6.77832C9.74707 7.06885 9.84668 7.15186 10.1372 7.15186ZM12.8931 7.15186H13.3828C13.6733 7.15186 13.7729 7.06885 13.7729 6.77832V6.28857C13.7729 5.99805 13.6733 5.90674 13.3828 5.90674H12.8931C12.6025 5.90674 12.5112 5.99805 12.5112 6.28857V6.77832C12.5112 7.06885 12.6025 7.15186 12.8931 7.15186ZM4.62549 9.86621H5.10693C5.40576 9.86621 5.49707 9.7832 5.49707 9.49268V9.00293C5.49707 8.7124 5.40576 8.62939 5.10693 8.62939H4.62549C4.32666 8.62939 4.23535 8.7124 4.23535 9.00293V9.49268C4.23535 9.7832 4.32666 9.86621 4.62549 9.86621ZM7.38135 9.86621H7.87109C8.16162 9.86621 8.25293 9.7832 8.25293 9.49268V9.00293C8.25293 8.7124 8.16162 8.62939 7.87109 8.62939H7.38135C7.09082 8.62939 6.99121 8.7124 6.99121 9.00293V9.49268C6.99121 9.7832 7.09082 9.86621 7.38135 9.86621ZM10.1372 9.86621H10.627C10.9175 9.86621 11.0171 9.7832 11.0171 9.49268V9.00293C11.0171 8.7124 10.9175 8.62939 10.627 8.62939H10.1372C9.84668 8.62939 9.74707 8.7124 9.74707 9.00293V9.49268C9.74707 9.7832 9.84668 9.86621 10.1372 9.86621ZM12.8931 9.86621H13.3828C13.6733 9.86621 13.7729 9.7832 13.7729 9.49268V9.00293C13.7729 8.7124 13.6733 8.62939 13.3828 8.62939H12.8931C12.6025 8.62939 12.5112 8.7124 12.5112 9.00293V9.49268C12.5112 9.7832 12.6025 9.86621 12.8931 9.86621ZM4.62549 12.5889H5.10693C5.40576 12.5889 5.49707 12.4976 5.49707 12.207V11.7173C5.49707 11.4268 5.40576 11.3438 5.10693 11.3438H4.62549C4.32666 11.3438 4.23535 11.4268 4.23535 11.7173V12.207C4.23535 12.4976 4.32666 12.5889 4.62549 12.5889ZM7.38135 12.5889H7.87109C8.16162 12.5889 8.25293 12.4976 8.25293 12.207V11.7173C8.25293 11.4268 8.16162 11.3438 7.87109 11.3438H7.38135C7.09082 11.3438 6.99121 11.4268 6.99121 11.7173V12.207C6.99121 12.4976 7.09082 12.5889 7.38135 12.5889ZM10.1372 12.5889H10.627C10.9175 12.5889 11.0171 12.4976 11.0171 12.207V11.7173C11.0171 11.4268 10.9175 11.3438 10.627 11.3438H10.1372C9.84668 11.3438 9.74707 11.4268 9.74707 11.7173V12.207C9.74707 12.4976 9.84668 12.5889 10.1372 12.5889Z"
                          fill={colors.white}
                        />
                      </Svg>
                    </View>
                    <Text style={{marginLeft: hp('1%')}}>{t('events.enddate')}</Text>
                  </View>
                  <DateToggle
                    label={t('events.enddate')}
                    date={dynamicEndDate || moment()}
                    timeFrame={selectedTimeframe.id}
                    onChangeDate={date => setDynamicEndDate(moment(date, DATE_FORMAT))}
                  />
                </View>
                <View style={{height: 1, backgroundColor: colors.separatorLine, marginBottom: hp('1%')}} />
              </View>
              <Text
                style={{
                  fontSize: hp('2%'),
                  marginBottom: 0,
                  marginTop: hp('0.75%'),
                  fontWeight: 'bold',
                }}>
                More
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                  marginVertical: hp('1%'),
                  marginTop: hp('0.25%'),
                }}>
                <Text style={{fontSize: hp('1.75%')}}>Children events</Text>
                <Switch
                  value={isChildrenEventsLocal}
                  onValueChange={setChildrenEventsLocal}
                  backgroundActive={colors.statusGreen}
                  backgroundInactive={colors.gray400 + '29'}
                  renderActiveText={false}
                  renderInActiveText={false}
                  circleBorderWidth={0}
                  circleSize={hp('2.5%')}
                  switchWidthMultiplier={2.2}
                  barHeight={hp('3%')}
                />
              </View>
              <View style={{height: 1, backgroundColor: colors.separatorLine, marginVertical: hp('0.5%')}} />
              <View
                style={{
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  width: '100%',
                  marginVertical: hp('0.5%'),
                }}>
                <Text
                  style={{
                    fontSize: hp('2%'),
                    marginBottom: hp('0.25%'),
                    fontWeight: 'bold',
                  }}>
                  Radius
                </Text>
                <Text style={{fontSize: hp('1.5%'), color: colors.textSecondary, marginBottom: hp('0.15%')}}>
                  {radiusLocal} km
                </Text>
                <Slider
                  style={{width: '100%', height: hp('4%')}}
                  minimumValue={0}
                  maximumValue={1000}
                  minimumTrackTintColor={colors.statusGreen}
                  maximumTrackTintColor={colors.border}
                  step={1}
                  value={radiusLocal}
                  onValueChange={setRadiusLocal}
                />
              </View>
              <TouchableOpacity
                onPress={applyChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>

        {/* All Modal */}
        <Modal visible={isAllModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsAllModalVisible(false);
              applyChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2%'),
                height: hp('30%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('2%')}}>
                {t('events.eventtype')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                {eventTypes.map(eventType => (
                  <TouchableOpacity
                    key={eventType.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('1.5%'),
                      borderRadius: 20,
                      backgroundColor:
                        tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.gray400,
                      margin: hp('0.5%'),
                      marginBottom: hp('1%'),
                    }}
                    onPress={() => setTempSelectedEventType(eventType)}>
                    <Text
                      style={{
                        color: tempSelectedEventType.id === eventType.id ? colors.white : colors.black,
                        fontSize: hp('1.8%'),
                        fontWeight: '500',
                      }}>
                      {eventType.title === t('events.type') ? t('events.all') : eventType.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <TouchableOpacity
                onPress={applyChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1.5%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  const EventSearch = () => {
    const [isModalVisible, setModalVisible] = useState(false);
    const [startDateEnabled, setStartDateEnabled] = useState(false);
    const [endDateEnabled, setEndDateEnabled] = useState(false);
    const [tempSelectedTimeframe, setTempSelectedTimeframe] = useState(selectedTimeframe);
    const [tempSelectedEventType, setTempSelectedEventType] = useState(selectedEventType);
    const [isChildrenEventsLocal, setChildrenEventsLocal] = useState(isForKids);
    const [radiusLocal, setRadiusLocal] = useState(radius);
    const [suggestions, setSuggestions] = useState([
      'Music Festival',
      'Art Exhibition',
      'Food Truck Event',
      'Book Fair',
      'Tech Meetup',
      'Yoga Class',
    ]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [eventsList, setEventList] = useState([]);
    const [showLocationResults, setShowLocationResults] = useState(false);
    const [locationResults, setLocationResults] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
      setGlobalInputValue(inputValue);
    }, [inputValue]);

    useEffect(() => {
      DeviceEventEmitter.addListener('onSearchResult', data => {
        setEventList(data.events);
      });

      DeviceEventEmitter.addListener('hideSuggestion', () => {
        setShowSuggestions(false);
      });

      return () => {
        DeviceEventEmitter.removeAllListeners('onSearchResult');
        DeviceEventEmitter.removeAllListeners('hideSuggestion');
      };
    }, []);

    const searchLocations = async (text: string) => {
      if (!text) {
        return;
      }
      const apiKey = Config.GOOGLE_API_KEY;
      setIsLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
            text,
          )}&key=${apiKey}`,
          {
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
            },
          },
        );
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status === 'OK' && data.predictions && data.predictions.length > 0) {
          const locations: any = [];
          locations.push(data.predictions[0]);
          setLocationResults(locations);
          setShowLocationResults(true);
        } else {
          console.error('Google Places API error:', data.status);
          setLocationResults([]);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
        setLocationResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    const handleLocationSelect = async (placeId: string) => {
      Keyboard.dismiss();
      const apiKey = Config.GOOGLE_API_KEY;
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${apiKey}`,
          {
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
            },
          },
        );
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status === 'OK') {
          const location = data.result;
          setShowLocationResults(false);
          setShowSuggestions(false);
          setInputValue('');

          DeviceEventEmitter.emit('homeLocationChange', {
            lat: location.geometry.location.lat,
            long: location.geometry.location.lng,
          });
        } else {
          console.error('Google Places API error:', data.status);
        }
      } catch (error) {
        console.error('Error fetching location details:', error);
      }
    };

    const onChangeText = (text: string) => {
      setInputValue(text);
      if (text.length > 0) {
        setShowSuggestions(true);
        searchLocations(text);
      } else {
        setShowSuggestions(false);
        setLocationResults([]);
      }
    };

    return (
      <TouchableWithoutFeedback
        onPress={() => {
          setShowSuggestions(false);
          setShowLocationResults(false);
          Keyboard.dismiss();
        }}>
        <View style={{flex: 1, paddingHorizontal: 8, zIndex: 1000000000}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            {/* <HomeScreenLocationButton
              setSelectedTab={() => {
                setSelectedEventType(selectedEventType);
                setRadius(radius + 1);
              }}
            /> */}
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: hp('1.25%'),
                margin: hp('0.5%'),
                borderRadius: 10,
                backgroundColor: colors.white,
                borderColor: colors.separatorLine,
                borderWidth: 1,
                zIndex: 1000000,
              }}>
              <Svg width={hp('2.5%')} height={hp('2.5%')} viewBox="0 0 16 16" fill="none">
                <Path
                  d="M6.50301 12.8767C7.89827 12.8767 9.18492 12.4285 10.2376 11.6814L14.1978 15.616C14.3816 15.7986 14.6239 15.8899 14.8829 15.8899C15.426 15.8899 15.8103 15.4749 15.8103 14.9436C15.8103 14.6946 15.7268 14.4539 15.543 14.2795L11.6078 10.3616C12.435 9.28247 12.9279 7.94604 12.9279 6.49341C12.9279 2.98218 10.0371 0.110107 6.50301 0.110107C2.97726 0.110107 0.078125 2.97388 0.078125 6.49341C0.078125 10.0046 2.96891 12.8767 6.50301 12.8767ZM6.50301 11.4988C3.74591 11.4988 1.46503 9.23267 1.46503 6.49341C1.46503 3.75415 3.74591 1.48804 6.50301 1.48804C9.26011 1.48804 11.541 3.75415 11.541 6.49341C11.541 9.23267 9.26011 11.4988 6.50301 11.4988Z"
                  fill={colors.textPrimary}
                  fillOpacity="0.6"
                />
              </Svg>
              <TextInput
                style={{flex: 1, fontSize: hp('2%'), color: colors.black, marginLeft: hp('1.5%'), height: 40}}
                placeholder="Search events or locations..."
                placeholderTextColor={colors.placeholderText}
                underlineColorAndroid="transparent"
                value={inputValue}
                onChangeText={onChangeText}
                returnKeyType="search"
                onSubmitEditing={() => {
                  setShowSuggestions(false);
                  setShowLocationResults(false);
                }}
              />
              {showSuggestions && (
                <View
                  style={{
                    position: 'absolute',
                    top: 45,
                    left: -13,
                    width: wp('100%'),
                    backgroundColor: colors.white,
                    borderWidth: 1,
                    borderColor: colors.separatorLine,
                    borderRadius: 8,
                    shadowColor: colors.black,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84,
                    elevation: 5,
                    zIndex: 999,
                    maxHeight: hp('40%'),
                  }}>
                  <ScrollView
                    style={{maxHeight: hp('40%')}}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    {locationResults.map((item: any, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => handleLocationSelect(item.place_id)}
                        style={{
                          padding: hp('1.5%'),
                          borderBottomWidth: 1,
                          borderBottomColor: colors.separatorLine,
                          backgroundColor: colors.white,
                          width: '100%',
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                          <Svg
                            width={hp('2.5%')}
                            height={hp('2.5%')}
                            viewBox="0 0 24 24"
                            fill="none"
                            style={{marginRight: hp('1%')}}>
                            <Path
                              d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
                              fill={colors.textSecondary}
                            />
                          </Svg>
                          <Text style={{fontSize: hp('2%'), color: colors.textSecondary, fontWeight: '500', flex: 1}}>
                            {item.description}
                          </Text>
                        </View>
                        <Svg width={hp('2%')} height={hp('2%')} viewBox="0 0 24 24" fill="none">
                          <Path
                            d="M9 18l6-6-6-6"
                            stroke={colors.textSecondary}
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </Svg>
                      </TouchableOpacity>
                    ))}
                    {eventsList &&
                      eventsList.length > 0 &&
                      eventsList.map((item: any, index: number) => (
                        <TouchableOpacity
                          key={index}
                          onPress={() => {
                            setShowSuggestions(false);
                            // setInputValue(item.name);
                            Keyboard.dismiss();
                            DeviceEventEmitter.emit('eventSelection', {event: item});
                            if (item.coords && item.coords.lat) {
                              DeviceEventEmitter.emit('homeLocationChange', {
                                lat: item.coords.lat,
                                long: item.coords.long,
                              });
                            }
                          }}
                          style={{
                            padding: hp('1.5%'),
                            borderBottomWidth: 1,
                            borderBottomColor: colors.separatorLine,
                            backgroundColor: colors.white,
                            width: '100%',
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                            <Svg
                              width={hp('2.5%')}
                              height={hp('2.5%')}
                              viewBox="0 0 24 24"
                              fill="none"
                              style={{marginRight: hp('1%')}}>
                              <Path
                                d="M19 4H5C3.89543 4 3 4.89543 3 6V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V6C21 4.89543 20.1046 4 19 4Z"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M16 2V6"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M8 2V6"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M3 10H21"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M8 14H8.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M12 14H12.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M16 14H16.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M8 18H8.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M12 18H12.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <Path
                                d="M16 18H16.01"
                                stroke={colors.textSecondary}
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </Svg>
                            <Text style={{fontSize: hp('2%'), color: colors.textSecondary, fontWeight: '500', flex: 1}}>
                              {item.name}
                            </Text>
                          </View>
                          <Svg width={hp('2%')} height={hp('2%')} viewBox="0 0 24 24" fill="none">
                            <Path
                              d="M9 18l6-6-6-6"
                              stroke={colors.textSecondary}
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </Svg>
                        </TouchableOpacity>
                      ))}
                  </ScrollView>
                </View>
              )}
            </View>
            <Pressable
              style={e => {
                return {
                  width: 40,
                  height: 40,
                  marginLeft: 5,
                  borderRadius: 8,
                  alignItems: 'center',
                  alignContent: 'center',
                  justifyContent: 'center',
                  backgroundColor: e.pressed ? colors.warning : colors.secondary,
                };
              }}
              onPress={() => {
                setIsTabBarDisabled(true);
                navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE);
              }}>
              <FloatingPlusIcon />
            </Pressable>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  const selectedEventTypeData = eventTypes.find(item => item.id === selectedEventType.id);
  // const selectedTimeframeData = timeframes.find(item => item.id === selectedTimeframe.id);

  // Filter handlers
  const handleTimeframePress = () => {
    setTempSelectedTimeframe(selectedTimeframe);
    setRadiusLocal(radius);
    setChildrenEventsLocal(isForKids);
    setIsTimeframeModalVisible(true);
  };

  const handleEventTypePress = () => {
    setTempSelectedEventType(selectedEventType);
    setIsEventTypeModalVisible(true);
  };

  const handleFilterPress = () => {
    // Open full-screen filter modal
    setIsFullScreenFilterVisible(true);
  };

  const handleQuickFilterSelect = (timeframe: any) => {
    setSelectedTimeframe(timeframe);
  };

  const handleFullScreenFilterApply = (timeframe: any, eventType: any, isForKids: boolean, radius: number) => {
    setSelectedTimeframe(timeframe);
    setSelectedEventType(eventType);
    setIsForKids(isForKids);
    setRadius(radius);
  };

  const applyTimeframeChanges = () => {
    setSelectedTimeframe(tempSelectedTimeframe);
    setRadius(radiusLocal);
    setIsForKids(isChildrenEventsLocal);
    setIsTimeframeModalVisible(false);
  };

  const applyEventTypeChanges = () => {
    setSelectedEventType(tempSelectedEventType);
    setIsEventTypeModalVisible(false);
  };

  const listComponent = useMemo(() => {
    const EventTypeComponent = selectedEventTypeData?.component(
      scrollHandler,
      filterEvents,
      selectedTimeframe,
      globalInputValue,
      isMapView,
      setIsMapView,
    );
    // const TimeframeComponent = selectedTimeframeData?.component(scrollHandler);
    // console.log(selectedTimeframe);
    return (
      <>
        {/* {TimeframeComponent} */}
        {EventTypeComponent}
      </>
    );
  }, [scrollHandler, selectedEventType, selectedTimeframe, globalInputValue, isMapView, setIsMapView]);

  const header = useMemo(() => {
    return (
      <ModernHomeHeader
        scrollAnimatedStyle={scrollAnimatedStyle}
        globalInputValue={globalInputValue}
        setGlobalInputValue={setGlobalInputValue}
        selectedTimeframe={selectedTimeframe}
        selectedEventType={selectedEventType}
        isMapView={isMapView}
        setIsMapView={setIsMapView}
        onTimeframePress={handleTimeframePress}
        onEventTypePress={handleEventTypePress}
        onFilterPress={handleFilterPress}
        onQuickFilterSelect={handleQuickFilterSelect}
        isForKids={isForKids}
        radius={radius}
        timeframes={timeframes}
        eventTypes={eventTypes}
      />
    );
  }, [
    scrollAnimatedStyle,
    globalInputValue,
    selectedEventType,
    selectedTimeframe,
    isMapView,
    isForKids,
    radius,
    timeframes,
    eventTypes,
    handleTimeframePress,
    handleEventTypePress,
    handleFilterPress,
    handleQuickFilterSelect,
  ]);

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        DeviceEventEmitter.emit('hideSuggestion');
        Keyboard.dismiss();
      }}>
      <View style={{flex: 1}}>
        <Animated.View
          entering={FadeInRight.duration(300)}
          style={{
            zIndex: 10000,
            position: 'absolute',
            bottom: hp('15%'),
            right: hp('2.5%'),
            backgroundColor: colors.background,
          }}
        />
        <View style={{flex: 1, backgroundColor: colors.background}}>
          {header}
          <Animated.View style={[{flex: 1, backgroundColor: colors.background, marginTop: hp('3.75%')}]}>
            {listComponent}
          </Animated.View>
        </View>
        {selectedEvent && <EventDetailsBottomSheet event={selectedEvent} />}

        {/* Full Screen Filter Modal */}
        <FullScreenFilterModal
          visible={isFullScreenFilterVisible}
          onClose={() => setIsFullScreenFilterVisible(false)}
          selectedTimeframe={selectedTimeframe}
          selectedEventType={selectedEventType}
          timeframes={timeframes}
          eventTypes={eventTypes}
          isForKids={isForKids}
          radius={radius}
          onApply={handleFullScreenFilterApply}
        />

        {/* Timeframe Filter Modal */}
        <Modal visible={isTimeframeModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsTimeframeModalVisible(false);
              applyTimeframeChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2.5%'),
                height: hp('71%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('1%')}}>
                {t('events.lookingforevents')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                  marginBottom: hp('0.5%'),
                }}>
                {timeframes.map(timeframe => (
                  <TouchableOpacity
                    key={timeframe.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('2%'),
                      borderRadius: 20,
                      backgroundColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedTimeframe.id === timeframe.id ? colors.warning : colors.black,
                      margin: hp('0.5%'),
                    }}
                    onPress={() => setTempSelectedTimeframe(timeframe)}>
                    <Text
                      style={{
                        color: tempSelectedTimeframe.id === timeframe.id ? colors.white : colors.black,
                        fontSize: hp('1.8%'),
                        fontWeight: '500',
                      }}>
                      {timeframe.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <TouchableOpacity
                onPress={applyTimeframeChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>

        {/* Event Type Filter Modal */}
        <Modal visible={isEventTypeModalVisible} transparent={true} animationType="fade">
          <TouchableOpacity
            onPress={() => {
              setIsEventTypeModalVisible(false);
              applyEventTypeChanges();
            }}
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              backgroundColor: colors.overlayBackground,
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={e => {
                e.isPropagationStopped;
              }}
              style={{
                width: '100%',
                backgroundColor: colors.white,
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                padding: hp('2%'),
                height: hp('30%'),
              }}>
              <Text style={{fontSize: hp('2.5%'), fontWeight: 'bold', marginBottom: hp('2%')}}>
                {t('events.eventtype')}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-between',
                  width: '100%',
                  alignItems: 'flex-start',
                }}>
                {eventTypes.map(eventType => (
                  <TouchableOpacity
                    key={eventType.id}
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: hp('1.5%'),
                      borderRadius: 20,
                      backgroundColor:
                        tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.white,
                      borderWidth: 1,
                      borderColor: tempSelectedEventType.id === eventType.id ? colors.eventInfluencer : colors.gray400,
                      margin: hp('0.5%'),
                      marginBottom: hp('1%'),
                    }}
                    onPress={() => setTempSelectedEventType(eventType)}>
                    <Text
                      style={{
                        color: tempSelectedEventType.id === eventType.id ? colors.white : colors.black,
                        fontSize: hp('1.8%'),
                        fontWeight: '500',
                      }}>
                      {eventType.title === t('events.type') ? t('events.all') : eventType.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <TouchableOpacity
                onPress={applyEventTypeChanges}
                style={{
                  marginTop: hp('1.5%'),
                  backgroundColor: colors.secondary,
                  paddingVertical: hp('1.25%'),
                  paddingHorizontal: hp('5%'),
                  borderRadius: 25,
                  width: '100%',
                  alignItems: 'center',
                  marginBottom: hp('1.5%'),
                }}>
                <Text style={{color: colors.white, fontSize: hp('2%'), fontWeight: 'bold'}}>{t('events.apply')}</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default HomeScreenTabs;
