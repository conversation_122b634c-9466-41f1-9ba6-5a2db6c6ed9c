buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        kotlinVersion = "1.9.10"
        playServicesLocationVersion = "21.0.1"
        ndkVersion = "25.1.8937393"
        gradleVersion = "7.4.2"
        reactNativeGradlePluginVersion = "0.72.3"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:$gradleVersion")
        classpath("com.facebook.react:react-native-gradle-plugin:$reactNativeGradlePluginVersion")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.7'
    }
}

allprojects {
    repositories {
        mavenLocal()
        mavenCentral()
        google()
    }

    // Enable buildConfig for all Android projects to fix compatibility issues
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                buildFeatures {
                    buildConfig true
                }
            }
        }
    }
}
