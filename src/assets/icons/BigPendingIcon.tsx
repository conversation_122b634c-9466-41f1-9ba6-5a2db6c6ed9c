import {Path, Rect, Svg} from 'react-native-svg';

const BigPendingIcon = () => (
  <Svg width="55" height="58" viewBox="0 0 55 58" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.3356 8.52823C27.5241 7.60803 31.8991 8.09572 35.7821 9.91565C39.5157 11.6656 42.5938 14.5532 44.5786 18.1579L38.3831 17.0654C37.7598 16.9555 37.1656 17.3717 37.0556 17.9949C36.9458 18.6181 37.3619 19.2124 37.9851 19.3223L47.0124 20.914C47.6357 21.0239 48.2299 20.6078 48.3399 19.9846L49.9317 10.9572C50.0415 10.334 49.6253 9.73967 49.0022 9.62976C48.3789 9.51988 47.7847 9.93602 47.6749 10.5592L46.5435 16.9754C44.3238 12.9826 40.901 9.7839 36.7547 7.84059C32.4147 5.80655 27.525 5.2615 22.8438 6.28993C18.1626 7.31839 13.9516 9.86287 10.864 13.5287C7.77635 17.1945 5.98472 21.7768 5.76701 26.5648C5.73825 27.1968 6.22743 27.7326 6.85961 27.7615C7.49177 27.7901 8.02756 27.3009 8.0563 26.6688C8.06122 26.5609 8.06702 26.4529 8.07371 26.3452C8.08921 26.285 8.09943 26.2224 8.10385 26.1585C8.10814 26.0964 8.10681 26.0347 8.1003 25.9747C8.42812 21.9432 10.0037 18.1073 12.6167 15.005C15.3793 11.7251 19.1471 9.44842 23.3356 8.52823ZM6.3513 32.5144C6.42958 32.8451 6.51566 33.1742 6.60952 33.5014C6.70339 33.8287 6.80482 34.1534 6.9137 34.4754C7.11644 35.0749 7.78586 35.3609 8.37381 35.1269C8.96178 34.8929 9.24528 34.2274 9.04625 33.6268C8.96325 33.3763 8.88526 33.1237 8.81239 32.8696C8.73951 32.6155 8.67186 32.3602 8.60951 32.1037C8.45993 31.4889 7.86682 31.0748 7.2442 31.188C6.62158 31.3012 6.20555 31.8986 6.3513 32.5144ZM47.3493 36.5241C47.6114 35.9482 47.319 35.2816 46.7303 35.0499C46.1413 34.8182 45.4793 35.1095 45.2134 35.6838C45.1025 35.9232 44.9866 36.1607 44.8658 36.3958C44.7452 36.6309 44.6197 36.8635 44.4897 37.0931C44.1781 37.6438 44.3272 38.3517 44.8589 38.6952C45.3903 39.0388 46.1024 38.8877 46.4175 38.3389C46.5866 38.0442 46.7491 37.7453 46.9047 37.4424C47.0601 37.1394 47.2083 36.8333 47.3493 36.5241ZM9.95077 40.3333C10.1511 40.6079 10.358 40.8781 10.5714 41.1432C10.7848 41.4086 11.0041 41.6687 11.2292 41.9233C11.6485 42.3972 12.3765 42.4016 12.8278 41.9579C13.2791 41.5142 13.2825 40.791 12.8662 40.3143C12.6926 40.1156 12.5229 39.9133 12.3572 39.7072C12.1915 39.5012 12.0302 39.292 11.8733 39.0798C11.4971 38.5708 10.7899 38.4191 10.2598 38.7647C9.72972 39.1103 9.57774 39.8223 9.95077 40.3333ZM42.303 43.4977C42.768 43.0684 42.7568 42.3406 42.3037 41.8988C41.8506 41.4569 41.1274 41.4689 40.6597 41.8951C40.4646 42.0729 40.2659 42.2469 40.0634 42.4169C39.861 42.587 39.6552 42.7526 39.4464 42.914C38.9457 43.3008 38.8089 44.0112 39.1657 44.5337C39.5225 45.0565 40.2375 45.1933 40.7408 44.8094C41.011 44.6034 41.2763 44.3907 41.5371 44.1719C41.7979 43.9528 42.0532 43.728 42.303 43.4977ZM16.2994 46.1464C16.5904 46.3217 16.8858 46.4904 17.1854 46.6524C17.4849 46.8142 17.7879 46.9688 18.0941 47.1162C18.6643 47.3907 19.337 47.1125 19.5811 46.5286C19.8252 45.9449 19.548 45.2767 18.9796 44.9987C18.7425 44.8827 18.5076 44.762 18.275 44.6362C18.0424 44.5106 17.8127 44.3802 17.5858 44.2454C17.0418 43.9221 16.331 44.0564 15.9763 44.5805C15.6216 45.1044 15.7574 45.8196 16.2994 46.1464ZM34.9484 47.9703C35.5433 47.7549 35.8153 47.0795 35.5687 46.4965C35.3224 45.9138 34.6509 45.6445 34.0546 45.8562C33.806 45.9445 33.5553 46.0277 33.3027 46.106C33.0502 46.1842 32.7965 46.2573 32.5414 46.3251C31.9298 46.4876 31.5283 47.0894 31.6548 47.7095C31.7811 48.3296 32.3872 48.733 32.9998 48.5742C33.3286 48.4889 33.6559 48.3959 33.9811 48.295C34.3065 48.1942 34.6289 48.0858 34.9484 47.9703ZM24.4049 49.0442C24.7411 49.093 25.0788 49.134 25.4176 49.1672C25.7565 49.2005 26.0957 49.2255 26.4351 49.2426C27.0671 49.2747 27.5795 48.7575 27.5781 48.1248C27.577 47.492 27.0625 46.9835 26.4307 46.9475C26.1672 46.9326 25.9038 46.9122 25.6408 46.8866C25.3777 46.8607 25.1153 46.8297 24.8538 46.7933C24.227 46.7062 23.6239 47.1052 23.4999 47.726C23.3761 48.3466 23.7786 48.9532 24.4049 49.0442ZM27.373 19.4792V27.5H35.3959C36.0289 27.5 36.5418 28.0131 36.5418 28.6458C36.5418 29.2788 36.0289 29.7917 35.3959 29.7917H25.0814V19.4792C25.0814 18.8464 25.5942 18.3334 26.2272 18.3334C26.8599 18.3334 27.373 18.8464 27.373 19.4792Z"
      fill="white"
    />
  </Svg>
);

export default BigPendingIcon;
