import {FC, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Text, TouchableOpacity, View} from 'react-native';
import Animated, {interpolate, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {SmallChevronIcon} from '~assets/icons';
import Check from '~assets/icons/Check';
import FilterIcon from '~assets/icons/FilterIcon';
import FilteredIcon from '~assets/icons/FilteredIcon';
import getStyles from './styles';
import {useHomeStore} from '~providers/home/<USER>';
import Modal from 'react-native-modal/dist/modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';

const ANIMATION_TIME = 500;

interface IProps {
  isChildrenEvents: boolean;
  handleRadiusPress: () => void;
  handleChildrenPress: () => void;
  isFiltered: boolean;
}

const HeaderDropdown: FC<IProps> = ({isChildrenEvents, handleRadiusPress, handleChildrenPress, isFiltered}) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const isDropdownOpen = useSharedValue(0);
  const {t} = useTranslation();
  const {radius} = useHomeStore();
  const {top} = useSafeAreaInsets();
  const [isVisible, setIsVisible] = useState(false);

  const toggleDropdown = () => {
    isDropdownOpen.value = withTiming(isDropdownOpen.value ? 0 : 1, {
      duration: ANIMATION_TIME,
    });
    setIsVisible(prevState => !prevState);
  };

  const filter = useMemo(() => {
    return isChildrenEvents == true || isFiltered == true ? <FilteredIcon /> : <FilterIcon />;
  }, [isChildrenEvents, radius]);

  console.log('isVisible', isVisible);

  return (
    <>
      <TouchableOpacity onPress={() => toggleDropdown()} style={styles.buttonContainer}>
        {filter}
        <Text style={styles.buttonText}>{t('generic.filters')}</Text>
        <View style={styles.chevronContainer}>
          <SmallChevronIcon />
        </View>
      </TouchableOpacity>
      <Modal
        isVisible={isVisible}
        onBackdropPress={toggleDropdown}
        style={styles.flex}
        animationIn="fadeIn"
        animationOut="fadeOut"
        backdropTransitionOutTiming={0}>
        <Animated.View
          style={[
            styles.listContainer,
            {
              top: top + 30,
            },
          ]}>
          <TouchableOpacity
            style={styles.dropdownItemContainer}
            onPress={() => {
              handleChildrenPress();
              toggleDropdown();
            }}>
            <Text style={styles.dropdownText}>{t('home.children_events')}</Text>
            {isChildrenEvents && <Check />}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.dropdownItemContainer}
            onPress={() => {
              toggleDropdown();
              handleRadiusPress();
            }}>
            <Text style={styles.dropdownText}>{t('home.radius')}</Text>
            <Text style={styles.dropdownRadius}>
              {radius} {t('home.km')}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </Modal>
    </>
  );
};

export default HeaderDropdown;
