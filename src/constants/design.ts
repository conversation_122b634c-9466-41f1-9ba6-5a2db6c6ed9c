import {Dimensions, PixelRatio} from 'react-native';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

// Responsive breakpoints
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
} as const;

// Spacing system (based on 4px grid)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
  // Legacy spacing (for backward compatibility)
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
  xxxxxl: 48,
  xxxxxxl: 64,
} as const;

// Typography system
export const typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 36,
    '6xl': 48,
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // Letter spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
} as const;

// Border radius system
export const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 6,
  lg: 8,
  xl: 12,
  '2xl': 16,
  '3xl': 20,
  '4xl': 24,
  full: 9999,
} as const;

// Shadow system
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
  },
  '2xl': {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 12},
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 24,
  },
} as const;

// Icon sizes
export const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 40,
  '3xl': 48,
  '4xl': 56,
  '5xl': 64,
} as const;

// Helper function for responsive values
const getResponsiveValue = <T>(values: {xs?: T; sm?: T; md?: T; lg?: T; xl?: T}, fallback: T): T => {
  if (SCREEN_WIDTH >= breakpoints.xl && values.xl !== undefined) {
    return values.xl;
  }
  if (SCREEN_WIDTH >= breakpoints.lg && values.lg !== undefined) {
    return values.lg;
  }
  if (SCREEN_WIDTH >= breakpoints.md && values.md !== undefined) {
    return values.md;
  }
  if (SCREEN_WIDTH >= breakpoints.sm && values.sm !== undefined) {
    return values.sm;
  }
  if (values.xs !== undefined) {
    return values.xs;
  }
  return fallback;
};

// Responsive utilities
export const responsive = {
  // Get responsive value based on screen width
  getValue: getResponsiveValue,

  // Screen dimensions
  screen: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    isSmall: SCREEN_WIDTH < breakpoints.sm,
    isMedium: SCREEN_WIDTH >= breakpoints.sm && SCREEN_WIDTH < breakpoints.lg,
    isLarge: SCREEN_WIDTH >= breakpoints.lg,
  },

  // Pixel ratio utilities
  pixelRatio: PixelRatio.get(),

  // Scale function for responsive sizing
  scale: (size: number): number => {
    const scale = SCREEN_WIDTH / 375; // Base on iPhone X width
    return Math.round(PixelRatio.roundToNearestPixel(size * scale));
  },

  // Responsive padding/margin
  spacing: {
    xs: getResponsiveValue({xs: spacing.xs, sm: spacing.sm, md: spacing.md}, spacing.md),
    sm: getResponsiveValue({xs: spacing.sm, sm: spacing.md, md: spacing.lg}, spacing.lg),
    md: getResponsiveValue({xs: spacing.md, sm: spacing.lg, md: spacing.xl}, spacing.xl),
    lg: getResponsiveValue({xs: spacing.lg, sm: spacing.xl, md: spacing.xxl}, spacing.xxl),
    xl: getResponsiveValue({xs: spacing.xl, sm: spacing.xxl, md: spacing.xxxl}, spacing.xxxl),
  },
};

// Animation durations
export const animations = {
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },

  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
} as const;

// Touch target sizes (accessibility)
export const touchTargets = {
  min: 44, // Minimum touch target size
  comfortable: 48,
  large: 56,
} as const;

export type SpacingKey = keyof typeof spacing;
export type TypographyKey = keyof typeof typography;
export type BorderRadiusKey = keyof typeof borderRadius;
export type ShadowKey = keyof typeof shadows;
export type IconSizeKey = keyof typeof iconSizes;
