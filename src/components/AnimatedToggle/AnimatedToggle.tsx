import {FC, useCallback, useState} from 'react';
import {Animated, Text, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface Item {
  id: number;
  text: string;
}

interface IProps {
  items: Item[];
  height?: number;
  borderRadius?: number;
  paddingHorizontal?: number;
  onSelect: (text: string) => void;
}

const AnimatedToggle: FC<IProps> = ({items, height = 36, borderRadius = 5, paddingHorizontal = 16, onSelect}) => {
  const {colors} = useTheme();
  const [translateValue] = useState(new Animated.Value(0));
  const {width} = useWindowDimensions();

  const onPress = useCallback(
    ({id, text}: {id: number; text: string}) => {
      Animated.timing(translateValue, {
        toValue: id * (width / 2 - 18),
        useNativeDriver: true,
        duration: 200,
      }).start(() => onSelect(text));
    },
    [translateValue, width, onSelect],
  );

  return (
    <View style={{width: '100%', height, paddingHorizontal}}>
      <View
        style={{
          flex: 1,
          paddingVertical: 2,
          paddingHorizontal: 2,
          flexDirection: 'row',
          backgroundColor: colors.gray400 + '1F',
          borderRadius,
        }}>
        <Animated.View
          style={[
            {
              position: 'absolute',
              backgroundColor: colors.white,
              top: 2,
              right: 0,
              bottom: 0,
              left: 2,
              width: '50%',
              height: height - 4,
              borderRadius,
              transform: [{translateX: translateValue}],
            },
          ]}
        />
        {items.map(item => (
          <TouchableOpacity
            key={item.id}
            onPress={() => onPress(item)}
            style={{
              flex: 1,

              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={{fontSize: 15, fontWeight: '500', color: colors.textPrimary}}>{item.text}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default AnimatedToggle;
