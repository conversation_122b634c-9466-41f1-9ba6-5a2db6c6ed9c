import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
import {useTheme} from '~contexts/ThemeContext';

const createDetailStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    header: {
      marginVertical: 6,
      fontSize: 30,
      color: colors.textPrimary,
    },
    description: {
      color: colors.textPrimary,
      fontSize: 15,
      lineHeight: 25,
    },
    readMoreTest: {
      color: colors.info,
    },
  });

export {createDetailStyles as detailStyles};
