import {useNavigation} from '@react-navigation/native';
import {FC} from 'react';
import {Pressable, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import getStyles from './styles';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';
import {ChevronLeftIcon, XMarkIcon} from '~assets/icons';

interface IProps {
  isFirst: boolean;
  headerText: string;
}

const EventsHeader: FC<IProps> = ({isFirst, headerText}) => {
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation();
  const {t} = useTranslation();
  const {colors} = useTheme();
  const styles = getStyles(colors);

  return (
    <View style={{marginTop: top, ...styles.container}}>
      <Pressable onPress={() => navigation.goBack()} style={styles.backSection}>
        {isFirst ? (
          <XMarkIcon color={colors.textPrimary} size={24} />
        ) : (
          <>
            <ChevronLeftIcon color={colors.textPrimary} size={24} />
            <Text style={styles.backText}>{t('generic.back')}</Text>
          </>
        )}
      </Pressable>
      <Text style={styles.labelText}>{headerText}</Text>
    </View>
  );
};

export default EventsHeader;
