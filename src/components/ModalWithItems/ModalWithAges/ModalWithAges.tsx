import {Platform, View} from 'react-native';
import ModalWithButtons from '../../ModalWithButtons';
import {useEffect, useState} from 'react';
import ModalItem from '../ModalItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  close: () => void;
  onPress: (value: string) => () => void;
  chosenAge: string;
}
const defaultValues = [
  {title: '0-3', isActive: false},
  {title: '3-6', isActive: false},
  {title: '6-12', isActive: false},
  {title: '12-16', isActive: false},
  {title: '16+', isActive: false},
];
const ModalWithAges = ({isVisible, close, onPress, chosenAge}: IProps) => {
  const {colors} = useTheme();
  const {bottom} = useSafeAreaInsets();
  const [items, setItems] = useState(defaultValues);

  const handleItemClick = (index: number) => {
    const updatedItems = items.map((item, i) => ({
      ...item,
      isActive: i === index,
    }));
    setItems(updatedItems);
  };

  useEffect(() => {
    if (chosenAge) {
      setItems(prevItems => [
        ...prevItems.map(prevItem => (prevItem.title === chosenAge ? {...prevItem, isActive: true} : prevItem)),
      ]);
      return;
    }
    setItems(defaultValues);
  }, [chosenAge, isVisible]);
  return (
    <ModalWithButtons
      onCloseModal={close}
      modalIsVisible={isVisible}
      onPress={items.find(item => item.isActive)?.title ? onPress(items.find(item => item.isActive)!.title) : () => {}}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingHorizontal: 46,
          paddingTop: 10,
          overflow: 'hidden',
          height: '100%',
          paddingBottom: Platform.OS === 'ios' ? bottom + 10 : 30,
          justifyContent: 'space-around',
        }}>
        {items.map((item, index) => (
          <ModalItem key={index} item={item} onPress={() => handleItemClick(index)} flex={0.15} />
        ))}
      </View>
    </ModalWithButtons>
  );
};

export default ModalWithAges;
