import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {User} from '~types/api/user';

export function useGetUserById(user_id: any) {
  return useQuery<Omit<User, 'subcategories' | 'groups'> | undefined, Error>(['userAccount', user_id], async () => {
    if (!user_id) {
      return undefined;
    }
    const token = await FirebaseAuth.getAuthToken();
    const userResponse = await fetch(`${Config.BASE_API_URL}users/${user_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!userResponse.ok) {
      if (userResponse.status === 404) {
        console.log(`User account not found for user ${user_id}`);
        return undefined;
      }
      console.error(`Failed to get user account: ${userResponse.status}`);
      throw new Error(`Failed to get user account: ${userResponse.status}`);
    }

    const userData = (await userResponse.json()) as Omit<User, 'subcategories' | 'groups'>;

    return userData;
  });
}
