import React, {useState, useCallback, useEffect, useRef} from 'react';
import {useQuery, useQueryClient} from 'react-query';
import {DeviceEventEmitter} from 'react-native';
import Config from 'react-native-config';
import {
  useSmartDebounce,
  searchCache,
  requestDeduplicator,
  useOptimisticSearch,
  usePredictivePrefetch,
} from '~utils/performance/searchOptimization';
import {queryKeys} from '~utils/performance/queryConfig';

interface UseOptimizedSearchProps {
  onLocationSelect?: (placeId: string) => void;
  onEventSelect?: (event: any) => void;
  debounceMs?: number;
  enablePrefetch?: boolean;
  enableOptimistic?: boolean;
}

export const useOptimizedSearch = ({
  onLocationSelect,
  onEventSelect,
  debounceMs = 300,
  enablePrefetch = true,
  enableOptimistic = true,
}: UseOptimizedSearchProps = {}) => {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isMapLoading, setIsMapLoading] = useState(false);
  const queryClient = useQueryClient();
  const abortControllerRef = useRef<AbortController>();

  const {suggestions, updateSuggestions} = useOptimisticSearch();
  const {schedulePrefetech} = usePredictivePrefetch(queryClient);

  // Optimized location search with caching
  const searchLocations = useCallback(async (text: string, signal?: AbortSignal) => {
    if (!text.trim()) {
      return [];
    }

    const cacheKey = `locations-${text.toLowerCase()}`;
    const cached = searchCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    const dedupeKey = `location-search-${text}`;
    return requestDeduplicator.deduplicate(dedupeKey, async () => {
      const apiKey = Config.GOOGLE_API_KEY;
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(text)}&key=${apiKey}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          signal,
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const results = data.status === 'OK' && data.predictions ? data.predictions.slice(0, 3) : [];

      // Cache results
      searchCache.set(cacheKey, results);
      return results;
    });
  }, []);

  // Event search query with React Query
  const {
    data: eventResults = [],
    isLoading: isEventLoading,
    error: eventError,
  } = useQuery(
    queryKeys.events.search(inputValue),
    async () => {
      if (!inputValue.trim()) {
        return [];
      }

      const cacheKey = `events-${inputValue.toLowerCase()}`;
      const cached = searchCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      try {
        // Import FirebaseAuth dynamically to avoid circular dependencies
        const FirebaseAuth = require('~services/FirebaseAuthService').default;
        const axios = require('axios').default;

        const token = await FirebaseAuth.getAuthToken();
        const config = {
          headers: {Authorization: token, Accept: 'application/json'},
        };

        // Search events using the same API as useOptimizedEvents
        const response = await axios.get(Config.BASE_API_URL + 'events/', {
          params: {
            q: inputValue.trim(),
            tab: 'discover', // Use discover tab for search
            order_by: 'start_date',
            order_dir: 'asc',
            distance_km: 10000000, // Large radius for search
            limit: 10, // Limit search results for suggestions
            offset: 0,
          },
          ...config,
        });

        const results = response.data?.items || [];
        searchCache.set(cacheKey, results);
        return results;
      } catch (error) {
        console.error('Event search error:', error);
        return [];
      }
    },
    {
      enabled: inputValue.length > 2,
      staleTime: 1000 * 60 * 2, // 2 minutes
      cacheTime: 1000 * 60 * 10, // 10 minutes
      keepPreviousData: true,
    },
  );

  // Debounced search function
  const debouncedSearch = useSmartDebounce(
    async (text: string) => {
      if (!text.trim()) {
        setShowSuggestions(false);
        updateSuggestions([]);
        setIsLoading(false);
        return;
      }

      const startTime = Date.now();

      setIsLoading(true);
      setShowSuggestions(true);

      try {
        // Cancel previous request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        // Search locations
        const locationResults = await searchLocations(text, abortControllerRef.current.signal);

        // Combine results: Show first 2 locations, then events
        const limitedLocationResults = locationResults.slice(0, 2);
        const combinedResults = [
          ...limitedLocationResults.map((item: any) => ({...item, type: 'location'})),
          ...eventResults.map((item: any) => ({...item, type: 'event'})),
        ];

        updateSuggestions(combinedResults);

        // Emit search results for backward compatibility
        DeviceEventEmitter.emit('onSearchResult', {events: eventResults});
      } catch (error: any) {
        if (error.name !== 'AbortError') {
          console.error('Search error:', error);
          updateSuggestions([]);
        }
      } finally {
        // Ensure minimum loading duration of 200ms for better UX
        const elapsed = Date.now() - startTime;
        const minDelay = Math.max(0, 200 - elapsed);

        setTimeout(() => {
          setIsLoading(false);
        }, minDelay);
      }
    },
    debounceMs,
    {
      leading: false,
      trailing: true,
      maxWait: debounceMs * 3,
    },
  );

  // Handle input change with optimistic updates
  const handleInputChange = useCallback(
    (text: string) => {
      setInputValue(text);

      if (text.length > 0) {
        // Set loading immediately when user starts typing

        setIsLoading(true);

        if (enableOptimistic) {
          // Show optimistic results immediately
          const optimisticResults = searchCache.get(`locations-${text.toLowerCase()}`) || [];
          if (optimisticResults.length > 0) {
            updateSuggestions(optimisticResults, true);
            setShowSuggestions(true);
          }
        }

        debouncedSearch(text);
      } else {
        setIsLoading(false);
        setShowSuggestions(false);
        updateSuggestions([]);
      }
    },
    [debouncedSearch, enableOptimistic, updateSuggestions],
  );

  // Handle location selection with prefetching
  const handleLocationSelect = useCallback(
    async (placeId: string) => {
      setShowSuggestions(false);
      onLocationSelect?.(placeId);

      if (enablePrefetch) {
        // Prefetch nearby events for selected location
        schedulePrefetech(async () => {
          // This would prefetch events near the selected location
          // Implementation depends on your location-to-coordinates conversion
        });
      }
    },
    [onLocationSelect, enablePrefetch, schedulePrefetech],
  );

  // Handle event selection
  const handleEventSelect = useCallback(
    (event: any) => {
      setShowSuggestions(false);
      onEventSelect?.(event);

      if (enablePrefetch) {
        // Prefetch event details
        schedulePrefetech(async () => {
          if (event.event_id) {
            await queryClient.prefetchQuery({
              queryKey: queryKeys.events.detail(event.event_id),
              queryFn: async () => {
                // Fetch event details
                const response = await fetch(`${Config.BASE_API_URL}events/${event.event_id}`);
                return response.json();
              },
            });
          }
        });
      }
    },
    [onEventSelect, enablePrefetch, schedulePrefetech, queryClient],
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Listen for hide suggestion events
  useEffect(() => {
    const hideSuggestion = () => setShowSuggestions(false);
    DeviceEventEmitter.addListener('hideSuggestion', hideSuggestion);
    return () => DeviceEventEmitter.removeAllListeners('hideSuggestion');
  }, []);

  // Listen for map loading events
  useEffect(() => {
    const handleMapLoadingStart = () => setIsMapLoading(true);
    const handleMapLoadingEnd = () => setIsMapLoading(false);

    DeviceEventEmitter.addListener('mapLoadingStart', handleMapLoadingStart);
    DeviceEventEmitter.addListener('mapLoadingEnd', handleMapLoadingEnd);

    return () => {
      DeviceEventEmitter.removeAllListeners('mapLoadingStart');
      DeviceEventEmitter.removeAllListeners('mapLoadingEnd');
    };
  }, []);

  return {
    inputValue,
    setInputValue: handleInputChange,
    showSuggestions,
    setShowSuggestions,
    suggestions,
    isLoading: isLoading || isEventLoading || isMapLoading,
    handleLocationSelect,
    handleEventSelect,

    // Additional utilities
    clearSearch: () => {
      setInputValue('');
      setShowSuggestions(false);
      updateSuggestions([]);
    },

    // Performance metrics
    cacheSize: searchCache.size(),
    hasError: !!eventError,
  };
};
