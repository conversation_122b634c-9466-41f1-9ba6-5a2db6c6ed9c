import React from 'react';
import {View, ViewStyle, ImageSourcePropType} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useTheme} from '~contexts/ThemeContext';

interface CircularImageProps {
  source: ImageSourcePropType;
  size?: number;
  style?: ViewStyle;
  showShadow?: boolean;
  borderWidth?: number;
  borderColor?: string;
}

const CircularImage: React.FC<CircularImageProps> = ({
  source,
  size = 120,
  style,
  showShadow = true,
  borderWidth = 0,
  borderColor,
}) => {
  const {colors} = useTheme();
  const radius = size / 2;

  const containerStyle: ViewStyle = {
    width: size,
    height: size,
    borderRadius: radius,
    overflow: 'hidden',
    backgroundColor: colors.gray100,
    borderWidth,
    borderColor: borderColor || colors.border,
    ...(showShadow && {
      elevation: 3,
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
    }),
    ...style,
  };

  return (
    <View style={containerStyle}>
      <FastImage
        source={source}
        style={{
          width: '100%',
          height: '100%',
        }}
        resizeMode="cover"
      />
    </View>
  );
};

export default CircularImage;
