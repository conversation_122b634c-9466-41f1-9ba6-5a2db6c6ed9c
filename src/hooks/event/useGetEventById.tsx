import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Event} from '~types/api/event';

export const useGetEventById = (event_id?: number) => {
  const {t} = useTranslation();

  return useQuery<Event, Error>(
    ['eventById', event_id],
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const baseURL = token
        ? `${Config.BASE_API_URL}events/${event_id}`
        : `${Config.BASE_API_URL}public-events/${event_id}`;
      const response = await fetch(baseURL, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (response.status === 404) {
        Alert.alert(t('events.event_has_been_deleted'));
      }
      if (!response.ok) {
        throw new Error('Failed to get the event by id');
      }

      const data = await response.json();

      return data as Event;
    },
    {cacheTime: 0, enabled: !!event_id},
  );
};
