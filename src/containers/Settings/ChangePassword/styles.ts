import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.white,
    },
    mainWrapper: {flex: 1, paddingHorizontal: 16, paddingBottom: 30, marginTop: 80},
    headerText: {marginTop: 12, lineHeight: 41, fontSize: 34, color: colors.textPrimary, fontWeight: '700'},
    requirementsText: {marginTop: 16, fontSize: 17, lineHeight: 22, fontWeight: '400', color: colors.textSecondary},
    wrapper: {flex: 1, justifyContent: 'space-between', marginTop: 16},
  });

export default getStyles;
