import {FC} from 'react';
import {View} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  dotsNumber: number;
  selectedDotNumber: number;
}

const ProgressDots = ({dotsNumber, selectedDotNumber}: IProps) => {
  const {colors} = useTheme();
  const array = [...new Array(dotsNumber)];

  return (
    <View style={{width: '100%', flexDirection: 'row', justifyContent: 'center'}}>
      {array.map((_, index) => (
        <View
          key={`dot-${index}`}
          style={{
            backgroundColor: selectedDotNumber === index + 1 ? colors.primary : colors.border,
            height: 8,
            width: 8,
            borderRadius: 100,
            marginLeft: index === 0 ? 0 : 8,
          }}
        />
      ))}
    </View>
  );
};

export default ProgressDots;
