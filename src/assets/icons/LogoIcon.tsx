import {Path, Svg} from 'react-native-svg';

const LogoIcon = ({color}: {color: string}) => (
  <Svg width="25" height="25" viewBox="0 0 27 32" fill="none">
    <Path
      d="M26.0623 9.81431C25.8097 8.79516 25.4316 7.80831 24.9359 6.8745C24.4383 5.94184 23.8227 5.07132 23.1029 4.28257C22.3761 3.47842 21.5411 2.76997 20.6197 2.1757C18.7146 0.946766 16.5095 0.210635 14.2189 0.0389152C11.9284 -0.132804 9.63041 0.265735 7.54851 1.19577C6.53892 1.66493 5.59321 2.25072 4.73254 2.94005C3.88017 3.62511 3.12154 4.41036 2.47464 5.2772C1.82469 6.14895 1.28503 7.09137 0.866978 8.08472C0.448464 9.0788 0.179544 10.1247 0.0682655 11.1911C0.0426657 11.4018 0.0221865 11.6027 0.0068266 11.7987C-0.00853325 11.9947 0.0068266 12.2005 0.0068266 12.4111V29.0211C0.000797072 29.5072 0.146014 29.984 0.424 30.3908C0.701986 30.7977 1.10017 31.1162 1.56789 31.3059C2.03561 31.4955 2.55171 31.5477 3.05052 31.4558C3.54933 31.3639 4.0083 31.1321 4.36902 30.7899C4.61205 30.5622 4.80305 30.2888 4.92996 29.9868C5.05687 29.6849 5.11691 29.3611 5.1063 29.0358V22.5976C6.49037 23.5874 8.0521 24.3264 9.71425 24.778C11.3858 25.2323 13.1359 25.3589 14.8598 25.1503C16.578 24.9416 18.2372 24.414 19.7442 23.5971C21.2618 22.7834 22.5895 21.6812 23.6456 20.3584C24.488 19.2906 25.1513 18.1037 25.6117 16.8405C26.0765 15.5788 26.3406 14.2575 26.3951 12.9207C26.4257 11.8758 26.3138 10.8315 26.0623 9.81431V9.81431ZM10.1904 4.10128C10.0675 4.15028 9.93953 4.20907 9.81665 4.26787C9.90881 4.22377 10.001 4.17477 10.0982 4.13558L10.1904 4.10128ZM25.4991 12.5924C25.4665 12.7316 25.3853 12.8559 25.269 12.945C25.1526 13.034 25.0081 13.0825 24.8591 13.0824H22.5141C22.5167 13.1248 22.5167 13.1674 22.5141 13.2098C22.4549 14.3143 22.1678 15.3968 21.6693 16.3946C21.1752 17.3908 20.5068 18.2988 19.693 19.0796C19.2853 19.4301 18.8429 19.7419 18.3721 20.0106C17.9164 20.2849 17.4403 20.5397 16.9385 20.7798C16.3977 21.0097 15.8354 21.1902 15.2592 21.3188L13.9382 24.0234C13.8918 24.1377 13.8112 24.2364 13.7065 24.307C13.6017 24.3776 13.4775 24.417 13.3494 24.4203C13.3019 24.4275 13.2535 24.4275 13.2061 24.4203C13.0607 24.3909 12.9303 24.3144 12.837 24.2037C12.7437 24.093 12.6932 23.9549 12.6941 23.8127V21.5393C10.8636 21.4379 9.10388 20.8277 7.63057 19.7833C6.15725 18.7389 5.03442 17.3058 4.39974 15.6596C4.26926 15.3091 4.16322 14.9506 4.08231 14.5866L1.25097 13.3274C1.11438 13.2691 1.00294 13.1676 0.935421 13.0399C0.867904 12.9123 0.848439 12.7662 0.880305 12.6264C0.91217 12.4866 0.99342 12.3615 1.11036 12.2724C1.22731 12.1832 1.3728 12.1353 1.52233 12.1368H3.88263C3.90983 11.7385 3.96456 11.3424 4.04647 10.951C4.36286 9.3819 5.1024 7.91917 6.19173 6.70791C7.00295 5.76914 8.03448 5.0271 9.20226 4.54225C9.30978 4.48346 9.43266 4.43446 9.54529 4.38056C10.0527 4.17206 10.5788 4.00806 11.1171 3.89059L12.4329 1.18597C12.4863 1.07861 12.5705 0.987975 12.6757 0.924647C12.7808 0.861319 12.9026 0.827901 13.0269 0.82829C13.0726 0.821069 13.1193 0.821069 13.1651 0.82829C13.311 0.856771 13.4419 0.933021 13.5353 1.04391C13.6288 1.1548 13.6789 1.2934 13.6771 1.43585V3.67501C14.3456 3.71535 15.0079 3.82209 15.6534 3.99349C16.6956 4.26746 17.6797 4.7135 18.5615 5.3115C19.4556 5.91237 20.2292 6.66259 20.845 7.52616C21.547 8.45594 22.0435 9.51294 22.3042 10.6326L25.1355 11.8967C25.2685 11.9573 25.3765 12.0586 25.4424 12.1847C25.5083 12.3108 25.5282 12.4543 25.4991 12.5924Z"
      fill={color}
    />
    <Path
      d="M21.3414 13.9887L19.6416 14.7285C19.2905 15.7033 18.7035 16.5847 17.9285 17.3009C17.1534 18.017 16.2123 18.5476 15.1821 18.8492C14.9138 18.9291 14.6401 18.9913 14.3629 19.0353C14.0246 19.0916 13.6822 19.1227 13.3389 19.1284V23.7733L15.0182 20.3436C16.6092 19.9983 18.0582 19.211 19.1831 18.0804C20.3081 16.9497 21.0589 15.5263 21.3414 13.9887Z"
      fill={color}
    />
    <Path
      d="M5.04461 11.2253L6.74443 10.4854C7.1954 9.22326 8.03994 8.12381 9.16418 7.3353C10.2884 6.5468 11.6383 6.10717 13.0317 6.07568V1.44058L11.3575 4.87036C9.76842 5.2173 8.3217 6.0054 7.19868 7.13589C6.07567 8.26637 5.32634 9.68893 5.04461 11.2253Z"
      fill={color}
    />
    <Path
      d="M21.2595 10.8529C20.8975 9.33539 20.0758 7.95378 18.8974 6.88095C17.7189 5.80811 16.236 5.0917 14.6343 4.82137L15.4125 6.44806C16.7314 6.87962 17.8802 7.68783 18.7042 8.7637C19.5282 9.83958 19.9876 11.1314 20.0204 12.4649H24.8742L21.2595 10.8529Z"
      fill={color}
    />
    <Path
      d="M11.762 20.4073L10.9889 18.7757C10.2176 18.5254 9.5009 18.1417 8.87434 17.6438C8.11516 17.0473 7.50006 16.3004 7.07139 15.4545C6.64272 14.6087 6.41065 13.684 6.39117 12.7442H1.52209L5.10606 14.3464C5.46671 15.8724 6.29116 17.2622 7.47557 18.3407C8.65999 19.4193 10.1514 20.1383 11.762 20.4073Z"
      fill={color}
    />
    <Path
      d="M15.8835 10.0199C15.8191 9.91025 15.7255 9.81889 15.6122 9.75519C15.4988 9.69149 15.3698 9.65772 15.2384 9.65735H10.8711C10.7394 9.65669 10.61 9.69003 10.4965 9.75384C10.3829 9.81764 10.2895 9.90957 10.2259 10.0199L9.04324 12.0435C8.96536 12.1738 8.93328 12.3246 8.95171 12.4737C8.97015 12.6228 9.03813 12.7622 9.14564 12.8715L12.5094 16.3552C12.5794 16.4266 12.6638 16.4834 12.7576 16.5223C12.8514 16.5612 12.9525 16.5812 13.0547 16.5812C13.1569 16.5812 13.258 16.5612 13.3518 16.5223C13.4456 16.4834 13.5301 16.4266 13.6 16.3552L16.9638 12.8715C17.0713 12.7622 17.1393 12.6228 17.1577 12.4737C17.1762 12.3246 17.1441 12.1738 17.0662 12.0435L15.8835 10.0199ZM13.0522 14.8265L10.5997 12.2885L11.3063 11.0734H14.8032L15.5097 12.2885L13.0522 14.8265Z"
      fill={color}
    />
  </Svg>
);

export default LogoIcon;
