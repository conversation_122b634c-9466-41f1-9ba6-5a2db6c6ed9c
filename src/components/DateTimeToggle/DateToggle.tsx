import {Text, View} from 'react-native';
import {DateIcon} from '../../assets/icons';
import {Switch} from 'react-native-switch';
import {FC, useCallback, useEffect, useState} from 'react';
import {widthPercentageToDP as wp, heightPercentageToDP as hp} from 'react-native-responsive-screen';
import createStyles from './styles';
import DateTimeModal from '../DateTimeModal';
import moment from 'moment-timezone';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  label: string;
  date: moment.Moment;
  onChangeDate: (date: string) => void;
  errorText?: string;
  minAvailableDate?: Date;
  timeFrame?: number;
}

export const DATE_FORMAT = 'DD/MM/YYYY';

const DateToggle: FC<IProps> = ({
  date,
  timeFrame,
  label,
  onChangeDate,
  errorText,
  minAvailableDate = moment().toDate(),
}) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const [isActive, setIsActive] = useState(false);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [changeableDate, setChangeableDate] = useState<moment.Moment>(date);

  useEffect(() => {
    setChangeableDate(date);
    if (date && timeFrame == 0) {
      setIsActive(true);
    } else {
      setIsActive(false);
    }
  }, [date, timeFrame]);

  const onChange = () => {
    if (isActive) {
      onChangeDate('');
    } else {
      setIsDateModalOpen(true);
      setChangeableDate(moment(minAvailableDate));
    }
    setIsActive(prevState => !prevState);
  };

  const handleChooseDate = useCallback((value?: Date) => {
    if (value) {
      setChangeableDate(moment(value));
      return;
    }
  }, []);

  const handleChange = (val: moment.Moment) => {
    onChangeDate(val.format(DATE_FORMAT));
  };

  return (
    <>
      <View style={styles.container}>
        <Switch
          value={isActive}
          onValueChange={onChange}
          backgroundActive={colors.statusGreen}
          backgroundInactive={colors.gray400 + '29'}
          renderActiveText={false}
          renderInActiveText={false}
          circleBorderWidth={0}
          circleSize={hp('2.5%')}
          switchWidthMultiplier={2.2}
          barHeight={hp('3%')}
        />
      </View>
      <DateTimeModal
        isVisible={isDateModalOpen}
        close={() => setIsDateModalOpen(false)}
        onPress={() => {
          if (changeableDate) {
            handleChange(changeableDate);
          }
          setIsDateModalOpen(false);
        }}
        date={changeableDate?.toDate()}
        handleChooseMaxDate={handleChooseDate}
        minAvailableDate={minAvailableDate}
        withoutMaxAvailableDate
        mode="date"
      />
    </>
  );
};

export default DateToggle;
