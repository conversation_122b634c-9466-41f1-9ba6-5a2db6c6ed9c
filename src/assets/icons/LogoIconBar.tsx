import React from 'react';
import {Image} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const PyxiLogo = require('~assets/images/PyxiLogo.png'); // Змініть шлях до зображення

const LogoIconBar = ({color}: {color: string}) => {
  const {isDarkMode} = useTheme();

  return (
    <Image
      source={PyxiLogo}
      style={{
        width: 105,
        height: 25,
        tintColor: color,
      }}
      resizeMode="contain"
    />
  );
};

export default LogoIconBar;
