<resources>

    <!-- Dark theme application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:textColor">#F9FAFB</item>

        <!-- Status Bar Customization for Dark Theme -->
        <item name="android:statusBarColor">#0F0F0F</item>        <!-- Dark background matching app theme -->
        <item name="android:windowLightStatusBar">false</item>        <!-- Makes icons white for dark backgrounds -->

        <!-- Full screen support -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="SplashScreenTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:statusBarColor">#0F0F0F</item>        <!-- Dark background matching app theme -->

        <!-- Full screen support for splash -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
</resources>
