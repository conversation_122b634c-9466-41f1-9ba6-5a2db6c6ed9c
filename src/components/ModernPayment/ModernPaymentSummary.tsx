import React from 'react';
import {View, Text, ViewStyle} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import Animated, {FadeInUp} from 'react-native-reanimated';
import {useTranslation} from 'react-i18next';

interface PaymentSummaryItem {
  label: string;
  amount: number;
  isTotal?: boolean;
  isDiscount?: boolean;
}

interface ModernPaymentSummaryProps {
  items: PaymentSummaryItem[];
  currency?: string;
  style?: ViewStyle;
  animationDelay?: number;
}

const ModernPaymentSummary: React.FC<ModernPaymentSummaryProps> = ({
  items,
  currency = '$',
  style,
  animationDelay = 0,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const formatAmount = (amount: number, isDiscount = false) => {
    const formattedAmount = `${currency}${Math.abs(amount).toFixed(2)}`;
    return isDiscount ? `-${formattedAmount}` : formattedAmount;
  };

  const containerStyle: ViewStyle = {
    backgroundColor: colors.cardBackground,
    borderRadius: borderRadius['2xl'],
    padding: spacing.xl,
    borderWidth: 1,
    borderColor: colors.border + '40',
    ...style,
  };

  return (
    <Animated.View entering={FadeInUp.delay(animationDelay).duration(400)} style={containerStyle}>
      <Text
        style={{
          fontSize: typography.fontSize.lg,
          fontWeight: typography.fontWeight.bold,
          color: colors.textPrimary,
          marginBottom: spacing.md,
        }}>
        {t('payment.summary') || 'Payment Summary'}
      </Text>

      {items.map((item, index) => (
        <View key={index}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingVertical: spacing.sm,
            }}>
            <Text
              style={{
                fontSize: item.isTotal ? typography.fontSize.lg : typography.fontSize.base,
                fontWeight: item.isTotal ? typography.fontWeight.bold : typography.fontWeight.normal,
                color: item.isTotal ? colors.textPrimary : colors.textSecondary,
                flex: 1,
              }}>
              {item.label}
            </Text>

            <Text
              style={{
                fontSize: item.isTotal ? typography.fontSize.lg : typography.fontSize.base,
                fontWeight: item.isTotal ? typography.fontWeight.bold : typography.fontWeight.medium,
                color: item.isTotal ? colors.textPrimary : item.isDiscount ? colors.success : colors.textSecondary,
              }}>
              {formatAmount(item.amount, item.isDiscount)}
            </Text>
          </View>

          {/* Add separator line except for last item */}
          {index < items.length - 1 && !item.isTotal && (
            <View
              style={{
                height: 1,
                backgroundColor: colors.border,
                marginVertical: spacing.xs,
              }}
            />
          )}

          {/* Add thicker line before total */}
          {item.isTotal && index > 0 && (
            <View
              style={{
                height: 2,
                backgroundColor: colors.border,
                marginVertical: spacing.sm,
              }}
            />
          )}
        </View>
      ))}
    </Animated.View>
  );
};

export default ModernPaymentSummary;
