import {Alert, Linking} from 'react-native';

const openSetting = () => {
  Linking.openSettings().catch(() => {
    Alert.alert('Unable to open settings');
  });
};

const handleGoToSettingsImage = () => {
  return Alert.alert('Settings', 'If you want to use your photos, you can enable the images services in the settings', [
    {
      text: 'Go to Settings',
      onPress: openSetting,
    },
    {
      text: "Don't Use Photos",
      onPress: () => {},
    },
  ]);
};

export const contactUs = async () => {
  try {
    await Linking.openURL('mailto:<EMAIL>');
  } catch (e) {
    console.log(e);
  }
};
export default handleGoToSettingsImage;
