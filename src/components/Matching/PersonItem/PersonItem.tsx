import {StyleSheet, TouchableOpacity, View, Image} from 'react-native';
import FastImage from 'react-native-fast-image';
import {User} from '~types/api/user';
import getStyles from './styles';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import Animated, {FadeInLeft, FadeInUp} from 'react-native-reanimated';
import {Event} from '~types/api/event';
import Button from '~components/Button';
import {t} from 'i18next';
import auth from '@react-native-firebase/auth';
import FirebaseChatsService from '~services/FirebaseChats';
import {ChatType} from '~types/chat';
import firestore from '@react-native-firebase/firestore';
import {useTheme} from '~contexts/ThemeContext';

export const PersonCard = ({
  person,
  i,
  event,
  userAccount,
}: {
  person: User;
  i: number;
  event: Event;
  userAccount: User;
}) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const navigation = useNavigation<NavigationProps>();

  const onHostChatClick = async (host: User) => {
    if (host && event) {
      const chatId = await FirebaseChatsService.createPrivateChat({
        user_id1: auth().currentUser!.uid,
        user_id2: host?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: `${host.first_name} ${host.last_name || ''}`,
        user_image: userAccount?.photo + '',
        event: event,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
    }
  };
  return (
    <Animated.View style={styles.container} entering={FadeInUp.duration(300).delay(150 * i + 300)}>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate(SCREENS.PERSONAL_INFO, {
            user: {
              tag: `${person.uid}-tag`,
              user_id: person.uid,
              source: person.photo,
              description: person.description,
              name: person?.last_name ? `${person.first_name} ${person?.last_name}` : person.first_name,
              coords: person.coords,
            },
          });
        }}
        style={styles.wrapper}>
        <View style={styles.viewWrapper}>
          <View style={styles.image}>
            <FastImage
              style={[StyleSheet.absoluteFillObject, styles.radius]}
              source={{uri: person.photo, priority: 'high'}}
              resizeMode="cover"
            />
            <Image style={styles.image} source={{uri: person.photo}} resizeMode="cover" />
          </View>

          <View style={styles.flex3}>
            <Animated.Text
              style={styles.nameText}
              numberOfLines={2}
              entering={FadeInLeft.duration(300).delay(150 * i + 450)}>
              {person.first_name.trim()} {person.last_name.trim()}
            </Animated.Text>

            <View style={styles.fullWidth}>
              <Animated.Text
                style={styles.descriptionText}
                numberOfLines={1}
                entering={FadeInLeft.duration(300).delay(150 * i + 450)}>
                {person.description}
              </Animated.Text>
            </View>
          </View>

          <Button
            onPress={() => onHostChatClick(person)}
            label={t('chat.chat')}
            containerStyle={{
              backgroundColor: colors.statusPurple,
              borderRadius: 50,
            }}
            textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
          />
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default PersonCard;
