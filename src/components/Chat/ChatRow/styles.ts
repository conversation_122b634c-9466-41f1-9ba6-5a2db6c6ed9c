import {StyleSheet} from 'react-native';

const getStyles = (colors: any) =>
  StyleSheet.create({
    closedText: {
      color: colors.error,
      marginTop: 10,
      fontSize: 14,
      fontWeight: 'bold',
    },
    container: {
      flexDirection: 'row',
      padding: 10,
      borderBottomWidth: 1,
      borderBottomColor: colors.gray400,
    },
    rightContentContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    middleContentContainer: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: 10,
    },
    image: {height: 50, width: 50, borderRadius: 50},
    topText: {
      fontWeight: 'bold',
      fontSize: 16,
      color: colors.black,
    },
    middleText: {
      color: colors.textSecondary,
    },
    bottomText: {
      color: colors.textSecondary,
    },
    flexWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateText: {
      color: colors.textSecondary,
    },
    quantityUnreadMessagesContainer: {
      backgroundColor: colors.error,
      borderRadius: 12,
      paddingHorizontal: 7,
      paddingVertical: 2,
    },
    quantityUnreadMessagesText: {
      color: colors.white,
    },
  });

export default getStyles;
