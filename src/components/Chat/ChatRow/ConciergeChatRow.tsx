import {useNavigation} from '@react-navigation/native';
import {useMemo} from 'react';
import {getLastMessage} from '~Utils/chat';
import {formatDateTime} from '~Utils/Time';
import firestore from '@react-native-firebase/firestore';
import {Text, TouchableOpacity, View} from 'react-native';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {ChatType, ConciergeChatTypeWithKey} from '~types/chat';
import auth from '@react-native-firebase/auth';
import getStyles from './styles';
import {LogoIcon} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';

export default function ConciergeChatRow({chat}: {chat: ConciergeChatTypeWithKey}) {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const uid = auth().currentUser!.uid;

  const navigation = useNavigation();
  const {setIsTabBarDisabled} = useTabBar();

  const unreadMessagesCount = useMemo(() => {
    return chat?.messages.filter(cur => cur?.readUserIds && !cur.readUserIds.includes(uid)).length;
  }, [chat?.messages, uid]);

  const lastMessage = useMemo(() => getLastMessage(chat.messages), [chat.messages]);

  const topText = 'Ask Pyxi';

  const middleText = lastMessage?.sender;

  const bottomText = lastMessage?.message;

  // Check if last message is from current user and has been read
  const isLastMessageFromCurrentUser = lastMessage?.senderId === uid;
  const isLastMessageRead = lastMessage?.messageStatus === 'read';
  const shouldShowReadIndicator = isLastMessageFromCurrentUser && isLastMessageRead;

  const selectChat = () => {
    const chatRef = firestore().collection('chats').doc(chat.key);

    chatRef
      .get()
      .then(doc => {
        if (doc.exists) {
          const chatData = doc.data() as ChatType;
          const updatedMessages = [];

          for (const message of chatData.history) {
            if (!message.readUserIds) {
              updatedMessages.push(message);
              continue;
            }

            const updatedMessage = {...message};

            if (!updatedMessage.readUserIds.includes(uid)) {
              updatedMessage.readUserIds.push(uid);
            }

            updatedMessages.push(updatedMessage);
          }

          chatRef.update({
            history: updatedMessages,
          });
        }
      })
      .catch(e => console.log(e));
    setIsTabBarDisabled(true);
    navigation.navigate(SCREENS.CONCIERGE_CHAT, {chat: chat});
  };

  return (
    <TouchableOpacity onPress={selectChat} style={styles.container}>
      <View style={styles.rightContentContainer}>
        <View style={[styles.image, {backgroundColor: colors.black, alignItems: 'center', justifyContent: 'center'}]}>
          <LogoIcon color={colors.white} />
        </View>
      </View>
      <View style={styles.middleContentContainer}>
        <Text style={styles.topText} numberOfLines={1}>
          {topText}
        </Text>

        {!!middleText && (
          <Text style={styles.middleText} numberOfLines={1}>
            {middleText}
          </Text>
        )}
        <Text style={styles.bottomText} numberOfLines={1}>
          {bottomText}
        </Text>
      </View>

      <View style={styles.rightContentContainer}>
        <View style={styles.flexWrapper}>
          {lastMessage?.timestamp && <Text style={styles.dateText}>{formatDateTime(lastMessage.timestamp)}</Text>}
        </View>
        <View style={styles.flexWrapper}>
          {/* {unreadMessagesCount ? (
            <View style={styles.quantityUnreadMessagesContainer}>
              <Text style={styles.quantityUnreadMessagesText}>{unreadMessagesCount}</Text>
            </View>
          ) : null} */}
        </View>
        <View style={styles.flexWrapper} />
      </View>
    </TouchableOpacity>
  );
}
