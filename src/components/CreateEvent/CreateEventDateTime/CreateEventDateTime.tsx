import moment from 'moment-timezone';
import {FC} from 'react';
import {View} from 'react-native';
import {DateTimeToggle, DATE_FORMAT, TIME_FORMAT} from '~/components/DateTimeToggle';
import getStyles from './styles';
import {FormikErrors} from 'formik';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  onChangeStartDate: (date: string) => void;
  onChangeStartTime: (time: string) => void;
  onChangeEndDate: (date: string) => void;
  onChangeEndTime: (time: string) => void;
  formikErrors: FormikErrors<{
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
  }>;
}

const CreateEventDateTime: FC<IProps> = ({
  startDate,
  startTime,
  onChangeStartDate,
  onChangeStartTime,
  onChangeEndDate,
  onChangeEndTime,
  formikErrors,
  endTime,
  endDate,
}) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);

  return (
    <View style={styles.dateContainer}>
      <View style={styles.timelineContainer}>
        <View style={styles.dot} />
        <View style={styles.line} />
        <View style={styles.dotOutline} />
      </View>
      <View style={styles.dateTimeContainer}>
        <DateTimeToggle
          date={startDate ? moment(startDate, DATE_FORMAT) : moment()}
          time={startTime ? moment(startTime, TIME_FORMAT) : moment()}
          onChangeDate={onChangeStartDate}
          onChangeTime={onChangeStartTime}
          isDate={!!startDate}
          isTime={!!startTime}
          label={'Start'}
          errorText={
            formikErrors.startTime || (formikErrors.startDate && formikErrors.startTime + ' ' + formikErrors.startDate)
          }
        />
        <DateTimeToggle
          isDate={!!endDate}
          isTime={!!endTime}
          errorText={
            formikErrors.endTime || (formikErrors.endDate && formikErrors.endTime + ' ' + formikErrors.endDate)
          }
          time={endTime ? moment(endTime, TIME_FORMAT) : moment()}
          date={endDate ? moment(endDate, DATE_FORMAT) : moment()}
          label={'End'}
          onChangeDate={onChangeEndDate}
          onChangeTime={onChangeEndTime}
        />
      </View>
    </View>
  );
};

export default CreateEventDateTime;
