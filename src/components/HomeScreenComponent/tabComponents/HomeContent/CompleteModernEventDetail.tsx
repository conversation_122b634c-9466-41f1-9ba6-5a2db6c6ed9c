import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
  TextInput,
  Pressable,
  Linking,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  FadeInDown,
  FadeInUp,
  SlideInRight,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment-timezone';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import {BottomSheetModalProvider, BottomSheetModal} from '@gorhom/bottom-sheet';
import {Dialog} from 'react-native-simple-dialogs';
import Hyperlink from 'react-native-hyperlink';
import {BlurView} from '@react-native-community/blur';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import firestore from '@react-native-firebase/firestore';
import Share from 'react-native-share';
import auth from '@react-native-firebase/auth';

import {Event} from '~types/api/event';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius} from '~constants';
import {haptics} from '~utils/haptics';
import {useTranslation} from 'react-i18next';
import LikeButton from '~components/LikeButton';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetEventAttendees} from '~hooks/event/useGetEventAttendees';
import {useGetEventUserStatus} from '~hooks/event/useGetEventUserStatus';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useGetEventMatchingStatus} from '~hooks/event/useGetEventMatchingStatus';
import {useGetCommentByEvent} from '~hooks/event/useGetCommentByEvent';
import {useCreateComment} from '~hooks/event/useCreateComment';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import {useUpdateUserPostCode} from '~hooks/user/useUpdateUserPostcode';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {SUBSCRIPTION_STATUS, USER_EVENT_STATUS, User} from '~types/api/user';
import {Business} from '~types/api/business';
import {openLocationInMaps} from '~Utils/openLocationInMaps';
import ModernSpinner from '~components/ModernSpinner';
import Button from '~components/Button';
import CommentSheet from '~components/CommentSheet';
import Calendar from '~components/Calendar';
import QRCodeGenerator from '~components/QR-codes/QRCodeGenerator';
import ScannerQRCode from '~components/QR-codes/ScannerQRCode';
import AnimatedLoadingModal from '~components/Matching/AnimatedLoadingModal';
import IssueModal from '~types/components/issueModal';
import {getMapStyle} from '~constants/locationMap';
import {getMessageOfRecurrence} from '~Utils/event';
import FirebaseChatsService from '~services/FirebaseChats';
import {ChatType} from '~types/chat';
import {BigCheckIcon, BigPendingIcon, CheckIcon} from '~assets/icons';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import axios from 'axios';
import {t} from 'i18next';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const HEADER_HEIGHT = SCREEN_HEIGHT * 0.5;
const TRUNCATE_DESCRIPTION_NUMBER_OF_LINES = 6;
const DATE_FORMAT = 'ddd MMM DD, YYYY';

interface CompleteModernEventDetailProps {
  event: Event;
  onClose: () => void;
}

const CompleteModernEventDetail: React.FC<CompleteModernEventDetailProps> = ({event, onClose}) => {
  const {colors, isDarkMode} = useTheme();
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const scrollY = useSharedValue(0);

  // State management
  const [descriptionNumberOfLines, setDescriptionNumberOfLines] = useState(TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  const [lengthMore, setLengthMore] = useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [spinner, setIsSpinner] = useState(false);
  const [isAnimationVisible, setAnimationVisisble] = useState(false);
  const [eventCountry, setEventCountry] = useState('');
  const [pincodeDialogVisible, setPinCodeDialog] = useState(false);
  const [postCode, setPostCode] = useState('');

  // Refs
  const calendarRef = useRef<{open: () => void; close: () => void}>();
  const matchingOptionsSheetRef = useRef<BottomSheetModal>(null);
  const commentSheetRef = useRef<BottomSheetModal>(null);
  const mapRef = useRef<MapView>(null);

  // API Hooks - exactly like old implementation
  const {data: pyxiHost} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');
  const {data: eventAttendees, refetch: refetchAttendees} = useGetEventAttendees({
    event_id: event.event_id,
    subscription_status: SUBSCRIPTION_STATUS.ACCEPTED,
  });
  const {data: matchingStatus, refetch: refetchMatchinEventSatus} = useGetEventMatchingStatus(event.event_id + '');
  const {
    data: commentList,
    refetch: refetchCommentEvent,
    isLoading: isCommentEventLoading,
  } = useGetCommentByEvent(event.event_id);
  const {mutateAsync: createComment} = useCreateComment();
  const {
    data: userStatus,
    isLoading: userStatusIsLoading,
    isFetching: userStatusIsFetching,
    refetch,
  } = useGetEventUserStatus({event_id: event.event_id, user_id: auth()!.currentUser?.uid || ''});
  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus();
  const {data: userAccount, refetch: refetchUser} = useGetUserAccount(auth().currentUser?.uid);
  const {data: businessAccount, refetch: refetchBusiness} = useGetBusinessAccount(auth().currentUser?.uid || '');
  const {data: hostUser} = useGetUserAccount(event?.host_id || '');
  const {data: hostBusiness} = useGetBusinessAccount(event?.host_id || '');
  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {mutateAsync: deleteEventUserStatus} = useDeleteUserEventStatus();
  const {mutateAsync: updateUserMutation} = useUpdateUserPostCode();
  const {data: categoryData} = useGetEventSubcategories(event.event_id);
  const {setSubCategory} = useEventCreationStore();

  // Matching hooks
  const {openMatchingLoadingModal, setCurrentMatchingEvent, setDomain, setRefresh, setMatchingType, matchingType} =
    useMatchingLoadingModalAnimation();

  // Computed values
  const isUserIsAttendee = eventAttendees?.some(attendee => attendee.user?.uid === userAccount?.uid) || false;
  const user = !(userAccount as unknown as {detail: string})?.detail ? userAccount : businessAccount;
  const host = !(hostUser as unknown as {detail: string})?.detail ? hostUser : hostBusiness;
  const isUserEvent = event?.host_id === auth().currentUser?.uid;
  const isEventPassed = moment(event?.end_date).isBefore(moment());

  const publicDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
  const userDomain = user?.email?.split('@')[1];
  const isPublicDomain = publicDomains.includes(userDomain || '');

  // Groups query for matching
  const {data: groups} = useQuery<any[]>('joined-groups', async () => {
    const token = await FirebaseAuth.getAuthToken();
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    const response = await axios.get(Config.BASE_API_URL + 'community/joined', config);
    return response.data;
  });

  const styles = createStyles(colors);

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [1, 0], Extrapolate.CLAMP);
    const scale = interpolate(scrollY.value, [0, HEADER_HEIGHT], [1, 1.1], Extrapolate.CLAMP);
    return {
      opacity,
      transform: [{scale}],
    };
  });

  const overlayAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [0, 1], Extrapolate.CLAMP);
    return {
      opacity,
    };
  });

  // Event handlers
  const handleSharePress = async () => {
    if (!event?.event_id) {
      return;
    }
    const userName = (user as User)?.last_name
      ? (user as User)?.first_name + ' ' + (user as User)?.last_name
      : (user as User)?.first_name || (user as Business)?.name;
    const redirectLink = `https://partner.pyxi.ai/event/detail/${event.event_id}`;
    const url = redirectLink;
    await Share.open({
      message: t('invite_message', {userName, itemName: event.name, url}),
    });
  };

  const handleOpenMaps = () => {
    if (!event?.coords?.lat || !event?.coords?.long) {
      return;
    }
    openLocationInMaps({lat: event.coords.lat, long: event.coords.long, pointLabel: event?.address_name});
  };

  // Join/Leave Event Logic - exactly like old implementation
  const onNotificationAboutSubscriptionOn = async (item: any, host: any, responseData: any) => {
    setTimeout(async () => {
      if (responseData.status === USER_EVENT_STATUS.PENDING) {
        Notifier.showNotification({
          title: 'Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
        return;
      } else if (responseData.status === 'waiting') {
        Notifier.showNotification({
          title: 'You are in waitlist. Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
      } else {
        Notifier.showNotification({
          title: 'All Set! You are Ready for the event.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'success',
          },
        });
        refetchAttendees();
        if (item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
          setAnimationVisisble(true);
        }
      }
    }, 2000);
  };

  const onNotificationAboutSubscriptionOff = async (item: any, host: any) => {
    setTimeout(async () => {
      Notifier.showNotification({
        title: 'We notified the organiser you are no longer going to the event.',
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      try {
        const eventOwnerDoc = await firestore().collection('users').doc(host.uid).get();
        const eventOwnerData = eventOwnerDoc.data();
        if (eventOwnerData && eventOwnerData.deviceFcmToken) {
          await FirebaseChatsService.sendPushNotification(
            eventOwnerData.deviceFcmToken,
            'New Event Notification',
            `User ${userAccount?.first_name} are no longer going to the event ${item?.name}`,
            {
              clickAction: 'OPEN_NOTIFICATIONS',
              eventId: item.event_id,
              item: JSON.stringify(item),
            },
          );
        }
      } catch (error) {
        console.error('Error fetching event owner data:', error);
      }
    }, 2000);
  };

  const joinEvent = async (event_id?: number, isLeave?: boolean) => {
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: true});
      return;
    }
    if (event?.event_group_id && !event_id) {
      calendarRef.current?.open();
      return;
    }
    if (event && !event?.payment_url) {
      if (userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
        navigation.navigate(SCREENS.BUY_TICKET, {eventId: event.event_id});
        return;
      } else {
        navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus.order_id + ''});
        return;
      }
    }

    if (event?.payment_url && userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
      setModalIsVisible(true);
    }

    if (!event) {
      return;
    }
    if (event_id) {
      if (!isLeave) {
        const data = await addEventUserStatus({
          event_id: event_id.toString(),
        });
        if (!event?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(event, host)
            : onNotificationAboutSubscriptionOn(event, host, data);
        }

        if (event.event_type === 'business' || event.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event_id,
          event_image: event.image_url,
        });
      }
    } else {
      if (!userStatus) {
        const data = await addEventUserStatus({
          event_id: event.event_id.toString(),
        });
        if (!event?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(event, host)
            : onNotificationAboutSubscriptionOn(event, host, data);
        }

        if (event.event_type === 'business' || event.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event.event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event.event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event.event_id,
          event_image: event.image_url,
        });
      }
    }
    if (event_id) {
      if (event_id !== event.event_id) {
        navigation.replace(SCREENS.HOME_EVENT, {eventId: event_id});
      } else {
        await refetch();
      }
    } else {
      await refetch();
    }
  };

  const onJoinEventClick = () => {
    if (!userStatus && eventCountry === 'Greece') {
      if (userAccount?.postal_code) {
        joinEvent();
      } else {
        setPinCodeDialog(true);
      }
    } else {
      joinEvent();
    }
  };

  // Add to Calendar functionality - exactly like old implementation
  const addEventToCalendar = async (title: any, startDate: any, endDate: any, description: any, location: any) => {
    const now = new Date();
    const startDateEvent = new Date(startDate);
    const effectiveStartDate = startDateEvent < now ? now : startDateEvent;

    try {
      const RNCalendarEvents = require('react-native-calendar-events');
      const authStatus = await RNCalendarEvents.requestPermissions();
      if (authStatus === 'authorized') {
        const eventId = await RNCalendarEvents.saveEvent(title, {
          startDate: effectiveStartDate.toISOString(),
          endDate: new Date(endDate).toISOString(),
          description: description,
          location: location,
        });

        if (Platform.OS === 'ios') {
          const AddCalendarEvent = require('react-native-add-calendar-event');
          AddCalendarEvent.presentEventViewingDialog({eventId: eventId})
            .then((event: any) => {
              Alert.alert('Calendar', 'Your event has been saved.');
            })
            .catch((error: any) => {
              console.error('Error presenting event viewing dialog', error);
              Alert.alert('Error', 'Failed to open the event viewer: ' + error.message);
            });
        } else {
          Alert.alert('Unsupported Feature', 'This feature is only available on iOS.');
        }
      } else {
        console.error('Calendar permission is not authorized');
        return;
      }
    } catch (error) {
      console.error('Failed to add event to calendar:', error);
    }
  };

  const addCalendarEvent = async (eventConfig: any) => {
    try {
      const AddCalendarEvent = require('react-native-add-calendar-event');
      const eventInfo = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
      setTimeout(() => {
        if (eventInfo?.action !== 'CANCELED') {
          Alert.alert('Calendar', 'Your event has been saved.');
        } else {
          Alert.alert('Calendar', 'Your event has been cancelled.');
        }
      }, 2000);
    } catch (error) {
      Alert.alert('Error', 'Failed to add event to calendar');
      console.error('Add event error:', error);
    }
  };

  const addToCalendar = () => {
    if (Platform.OS == 'ios') {
      addEventToCalendar(event?.name, event?.start_date, event?.end_date, event?.description, event?.address_name);
    } else {
      const eventConfig = {
        title: event?.name,
        startDate: new Date(event?.start_date || '').toISOString(),
        endDate: new Date(event?.end_date || '').toISOString(),
        location: event?.address_name,
        notes: event?.description,
      };

      addCalendarEvent(eventConfig);
    }
  };

  // Host Chat functionality - exactly like old implementation
  const onHostChatClick = async (host: Business) => {
    setModalVisible(true);
  };

  const onIssueTypeClick = async (type: string) => {
    const hHost = type == 't' ? pyxiHost : host;
    if (hHost && event) {
      const chatId = await FirebaseChatsService.createOrganisationChat({
        user_id1: auth().currentUser!.uid,
        user_id2: hHost?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: (hHost as Business)?.name
          ? (hHost as Business)?.name
          : (hHost as unknown as User)?.last_name
            ? `${(hHost as unknown as User)?.first_name} ${(hHost as unknown as User)?.last_name || ''}`
            : (hHost as unknown as User)?.first_name || '',
        user_image: userAccount?.photo + '',
        event: event,
        isTechnical: type == 't' ? true : false,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
    }
  };

  // Find People to Go With handler - THE MOST PROMINENT FEATURE
  const handleMatchingPress = () => {
    if (false && userStatus?.status != USER_EVENT_STATUS.ACCEPTED) {
      Alert.alert('Note!', 'Please join the event before searching for someone to go with.');
      return;
    }
    if (matchingStatus?.matches_available && matchingStatus?.type === 'colleague') {
      Alert.alert(
        'Note!',
        "If you find someone to go with, your current colleague matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
        [
          {
            text: 'No',
            onPress: () => {
              // If user cancels, close the dialog and do nothing
            },
            style: 'cancel',
          },
          {
            text: 'Yes',
            onPress: () => {
              setCurrentMatchingEvent(event?.event_id || null);
              setRefresh(true);
              openMatchingLoadingModal();
            },
          },
        ],
      );
    } else {
      // Check if user has groups before showing bottom sheet
      if (groups && groups.length > 0) {
        matchingOptionsSheetRef.current?.present();
      } else {
        // If no groups, go directly to matching with 'anyone' type
        setCurrentMatchingEvent(event?.event_id || null);
        setDomain(userDomain || null);
        setMatchingType('anyone');
        setRefresh(false);
        openMatchingLoadingModal();
      }
    }
  };

  return (
    <BottomSheetModalProvider>
      <View style={styles.container}>
        {/* Header Image */}
        <Animated.View style={[styles.headerContainer, headerAnimatedStyle]}>
          <FastImage source={{uri: event.image_url}} style={styles.headerImage} resizeMode="cover" />
          <View style={styles.headerOverlay} />

          {/* Header Actions */}
          <View style={[styles.headerActions, {top: top + 10}]}>
            <TouchableOpacity onPress={onClose} style={styles.actionButton} activeOpacity={0.8}>
              <Ionicons name="close" size={24} color={colors.white} />
            </TouchableOpacity>

            <View style={styles.rightActions}>
              <TouchableOpacity onPress={handleSharePress} style={styles.actionButton} activeOpacity={0.8}>
                <Ionicons name="share-outline" size={24} color={colors.white} />
              </TouchableOpacity>
              <View style={styles.actionButton}>
                <LikeButton liked={event.user_liked} eventId={event.event_id} />
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Content */}
        <ScrollView style={styles.scrollView}>
          <View style={styles.contentCard}>
            <Text style={styles.eventTitle}>{event.name}</Text>
            <Text style={styles.eventDescription}>{event.description}</Text>

            {/* JOIN/LEAVE EVENT BUTTON */}
            {event?.host_id !== auth().currentUser?.uid && !userStatusIsLoading && !userStatusIsFetching ? (
              <View style={styles.joinSection}>
                {!isEventPassed || userStatus?.status ? (
                  <Button
                    disabled={
                      userStatus?.status === USER_EVENT_STATUS.PENDING ||
                      event?.is_cancelled ||
                      moment(event?.end_date).isBefore(moment())
                    }
                    icon={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED ? (
                        <BigCheckIcon />
                      ) : userStatus?.status === USER_EVENT_STATUS.PENDING ? (
                        <BigPendingIcon />
                      ) : undefined
                    }
                    label={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                        ? 'Accepted'
                        : userStatus?.status === USER_EVENT_STATUS.PENDING
                          ? 'Pending'
                          : userStatus?.status === 'waiting'
                            ? 'Waitlisted'
                            : event?.is_paid || event?.payment_url
                              ? 'Buy tickets'
                              : 'Join'
                    }
                    containerStyle={{
                      backgroundColor: event?.is_cancelled
                        ? colors.statusGray
                        : userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                          ? colors.statusGreen
                          : userStatus?.status === USER_EVENT_STATUS.PENDING
                            ? colors.primary
                            : userStatus?.status === 'waiting'
                              ? colors.primary
                              : colors.statusPurple,
                      marginTop: 20,
                      borderRadius: 20,
                    }}
                    textStyle={{
                      fontSize: userStatus?.status === 'waiting' ? 12 : 16,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => onJoinEventClick()}
                  />
                ) : (
                  <Button
                    label={'Buy tickets'}
                    containerStyle={{
                      backgroundColor: colors.gray400,
                      marginTop: 20,
                      borderRadius: 20,
                    }}
                    textStyle={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => Alert.alert('The event has ended')}
                  />
                )}
              </View>
            ) : (
              (userStatusIsLoading || userStatusIsFetching) && (
                <View style={styles.loaderJoinBtnView}>
                  <ModernSpinner size={24} variant="circular" />
                </View>
              )
            )}

            {/* FIND PEOPLE TO GO WITH - MOST PROMINENT FEATURE */}
            {!isUserEvent && (
              <View style={styles.findPeopleSection}>
                <Button
                  label={
                    matchingStatus?.matches_available
                      ? matchingStatus?.type === 'all'
                        ? t('events.see_my_match')
                        : t('home.find_someone')
                      : t('home.find_someone')
                  }
                  containerStyle={{
                    backgroundColor: matchingStatus?.matches_available ? colors.statusGreen : colors.statusPurple,
                    marginTop: 20,
                  }}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={handleMatchingPress}
                />

                {/* Find My Colleagues Button for business events */}
                {(host as Business) && (host as Business)!.user_pooling! && (
                  <Button
                    label={
                      matchingStatus?.matches_available
                        ? matchingStatus?.type === 'colleague'
                          ? 'See My Colleagues'
                          : 'Find My Colleagues'
                        : 'Find My Colleagues'
                    }
                    containerStyle={{
                      backgroundColor: colors.primary,
                      marginTop: 10,
                    }}
                    textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                    onPress={() => {
                      if (matchingStatus?.matches_available && matchingStatus?.type === 'all') {
                        Alert.alert(
                          'Note!',
                          "If you find my colleagues, your current matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
                          [
                            {
                              text: 'No',
                              onPress: () => {},
                              style: 'cancel',
                            },
                            {
                              text: 'Yes',
                              onPress: () => {
                                setCurrentMatchingEvent(event?.event_id || null);
                                setRefresh(true);
                                openMatchingLoadingModal();
                              },
                            },
                          ],
                        );
                      } else {
                        matchingOptionsSheetRef.current?.present();
                      }
                    }}
                  />
                )}
              </View>
            )}

            {/* ADD TO CALENDAR BUTTON */}
            <Button
              label="Add to my calendar"
              containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
              textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
              onPress={() => addToCalendar()}
            />

            {/* HOST INFORMATION SECTION */}
            {!!host && (
              <View style={styles.hostSection}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>{t('events.host')}</Text>
                  {isUserEvent && (
                    <Button
                      onPress={() =>
                        navigation.navigate(SCREENS.PENDING_ATTENDEES, {
                          eventId: event!.event_id,
                          eventName: event!.name,
                        })
                      }
                      label={t('events.requiring_confirmation')}
                      containerStyle={{
                        backgroundColor: colors.statusPurple,
                        borderRadius: 50,
                      }}
                      textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                    />
                  )}
                </View>
                <TouchableOpacity
                  style={styles.hostContainer}
                  onPress={() => {
                    navigation.navigate(SCREENS.PERSONAL_INFO, {
                      user: {
                        tag: host?.uid + 'image',
                        source: host?.photo || '',
                        description: host?.description || '',
                        name: (host as Business)?.name
                          ? (host as Business)?.name
                          : (host as User)?.last_name
                            ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                            : (host as User)?.first_name || '',
                        user_id: host?.uid || '',
                        eventName: event?.name,
                      },
                    });
                  }}>
                  <View style={styles.hostInfo}>
                    <View style={styles.hostPhoto}>
                      <FastImage
                        style={StyleSheet.absoluteFillObject}
                        resizeMode={'cover'}
                        source={{uri: host?.photo, priority: 'normal'}}
                      />
                    </View>

                    <Text style={styles.hostName}>
                      {(host as Business)?.name
                        ? (host as Business)?.name
                        : (host as User)?.last_name
                          ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                          : (host as User)?.first_name || ''}
                    </Text>
                    {!isUserEvent && (
                      <Button
                        onPress={() => onHostChatClick(host as Business)}
                        label={t('chat.chat')}
                        containerStyle={{
                          backgroundColor: colors.statusPurple,
                          borderRadius: 50,
                        }}
                        textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            )}

            {/* ATTENDEES LIST SECTION */}
            <View style={styles.attendeesSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>{t('events.attendees')}</Text>
              </View>
              <View style={styles.attendeesContainer}>
                {event?.host_id != user?.uid && (
                  <View style={styles.attendeeRowStyle}>
                    {eventAttendees && eventAttendees.length > 0 && (
                      <>
                        {eventAttendees.slice(0, 2).map((attendee, index) => (
                          <FastImage
                            key={attendee.user_id}
                            style={[styles.profileImg, {left: index > 0 ? -15 : 0}]}
                            source={{uri: attendee.user?.photo}}
                          />
                        ))}
                        <Text style={styles.attendeesCount}>+ {Math.max(0, (eventAttendees.length || 0) - 1)}</Text>
                      </>
                    )}
                  </View>
                )}
                {event?.host_id === user?.uid && (
                  <>
                    {eventAttendees ? (
                      eventAttendees.map?.((attendee, index) => (
                        <View key={index}>
                          <TouchableOpacity
                            style={styles.attendeeContainer}
                            disabled={!attendee.user}
                            onPress={() => {
                              navigation.navigate(SCREENS.PERSONAL_INFO, {
                                user: {
                                  tag: index + 'image',
                                  source: attendee.user.photo,
                                  description: attendee.user.description,
                                  name: `${attendee.user.first_name} ${attendee.user.last_name || ''}`,
                                  user_id: attendee.user.uid,
                                  eventName: event?.name,
                                },
                              });
                            }}>
                            <View style={styles.attendeeLeftContainer}>
                              {attendee?.user && (
                                <View style={styles.attendeePhoto}>
                                  <FastImage
                                    style={StyleSheet.absoluteFillObject}
                                    resizeMode={'cover'}
                                    source={{uri: attendee?.user?.photo, priority: 'normal'}}
                                  />
                                </View>
                              )}

                              {attendee?.user ? (
                                <Text
                                  style={
                                    styles.attendeeName
                                  }>{`${attendee?.user?.first_name} ${attendee?.user?.last_name}`}</Text>
                              ) : (
                                <Text style={[styles.attendeeName, {marginLeft: 0}]}>
                                  {`${attendee?.name}`} <Text style={styles.attendeeSub}>{'(external)'}</Text>
                                </Text>
                              )}
                            </View>
                            {isUserEvent && (
                              <Button
                                label={t('generic.remove')}
                                onPress={async () => {
                                  await deleteEventUserStatus({
                                    event_id: event.event_id,
                                    user_id: attendee.user?.uid,
                                    email: attendee.email,
                                  });
                                }}
                                containerStyle={styles.buttonRejectContainer}
                                textStyle={styles.buttonText}
                              />
                            )}
                          </TouchableOpacity>
                          <View style={styles.divider} />
                        </View>
                      ))
                    ) : (
                      <SkeletonPlaceholder>
                        <SkeletonPlaceholder.Item width={'100%'}>
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                          <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                        </SkeletonPlaceholder.Item>
                      </SkeletonPlaceholder>
                    )}
                  </>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </BottomSheetModalProvider>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerContainer: {
      height: HEADER_HEIGHT,
      position: 'relative',
    },
    headerImage: {
      width: '100%',
      height: '100%',
    },
    headerOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    headerActions: {
      position: 'absolute',
      left: spacing.md,
      right: spacing.md,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    rightActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
    },
    actionButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      alignItems: 'center',
      justifyContent: 'center',
    },
    scrollView: {
      flex: 1,
    },
    contentCard: {
      backgroundColor: colors.surface,
      borderTopLeftRadius: borderRadius.xl,
      borderTopRightRadius: borderRadius.xl,
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.xl,
      marginTop: -borderRadius.xl,
      minHeight: SCREEN_HEIGHT - HEADER_HEIGHT + 100,
    },
    eventTitle: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text,
      marginBottom: spacing.md,
    },
    eventDescription: {
      fontSize: typography.fontSize.md,
      color: colors.textSecondary,
      lineHeight: 24,
    },
    joinSection: {
      marginTop: spacing.lg,
    },
    loaderJoinBtnView: {
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.lg,
      height: 50,
    },
    findPeopleSection: {
      marginTop: spacing.lg,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    hostSection: {
      marginTop: spacing.lg,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text,
    },
    hostContainer: {
      marginTop: spacing.md,
    },
    hostInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
    },
    hostPhoto: {
      width: 50,
      height: 50,
      borderRadius: 25,
      overflow: 'hidden',
    },
    hostName: {
      flex: 1,
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.text,
    },
    attendeesSection: {
      marginTop: spacing.lg,
      paddingTop: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    attendeesContainer: {
      marginTop: spacing.md,
    },
    attendeeRowStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    profileImg: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: colors.white,
    },
    attendeesCount: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginLeft: spacing.sm,
    },
    attendeeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: spacing.md,
    },
    attendeeLeftContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
    },
    attendeePhoto: {
      width: 45,
      height: 45,
      borderRadius: 22.5,
      overflow: 'hidden',
    },
    attendeeName: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.text,
    },
    attendeeSub: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
    },
    buttonRejectContainer: {
      backgroundColor: colors.error,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
    },
    buttonText: {
      fontSize: typography.fontSize.sm,
      color: colors.white,
      fontWeight: typography.fontWeight.medium,
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: spacing.sm,
    },
  });

export default CompleteModernEventDetail;
