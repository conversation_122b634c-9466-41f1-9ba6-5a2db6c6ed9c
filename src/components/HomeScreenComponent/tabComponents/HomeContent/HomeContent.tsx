import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  DeviceEventEmitter,
  Image,
  ImageBackground,
  Keyboard,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import Animated from 'react-native-reanimated';
import {useIsFocused, useNavigation} from '@react-navigation/native';

import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {useHomeStore} from '~providers/home/<USER>';
import {Event} from '~types/api/event';
import {EVENT_AGE_GROUP, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {responsive, spacing} from '~constants';
import ModernEventsList from '../components/ModernEventsList';
import {Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import BottomSheet from '@gorhom/bottom-sheet';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import {useMapEventPrefetch} from '~hooks/performance/useMapEventPrefetch';
import LikeButton from '~components/LikeButton';
import {useTranslation} from 'react-i18next';
import auth from '@react-native-firebase/auth';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import MapView from 'react-native-map-clustering';
import FastImage from 'react-native-fast-image';
import EventDetailsBottomSheet from './EventDetailsBottomSheet';
import AnimatedPin from './AnimatedPin';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {useMapsContext} from '~providers/maps/zustand';
import {useTheme} from '~contexts/ThemeContext';
import {getMapStyle} from '~constants/locationMap';

const haversine = require('haversine');

interface HomeContentInterface {
  scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any;
  selectedTimeframe: {id: number; title: string};
  globalInputValue: string;
  isFree?: boolean;
  isNeighbourhood?: boolean;
  isMapView: boolean;
  setIsMapView: (value: boolean) => void;
}

const HomeContent: React.FC<HomeContentInterface> = ({
  scrollHandler,
  filterEvents,
  selectedTimeframe,
  globalInputValue,
  isFree,
  isNeighbourhood,
  isMapView,
  setIsMapView,
}) => {
  const {colors, isDarkMode} = useTheme();
  const {radius, isForKids} = useHomeStore();
  const navigation = useNavigation<NavigationProps>();

  const styles = useMemo(
    () =>
      StyleSheet.create({
        pinContainer: {
          alignItems: 'center',
          overflow: 'visible',
        },
        pinCircle: {
          width: 50, // Increased from 40 to 50
          height: 50, // Increased from 40 to 50
          borderRadius: 25, // Increased from 20 to 25
          borderWidth: 2,
          borderColor: colors.black,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.white,
        },
        pinImage: {
          width: 46, // Increased from 36 to 46
          height: 46, // Increased from 36 to 46
          borderRadius: 23, // Increased from 18 to 23
        },
        pinTriangle: {
          width: 0,
          height: 0,
          borderLeftWidth: 10, // Increased from 8 to 10
          borderRightWidth: 10, // Increased from 8 to 10
          borderTopWidth: 15, // Increased from 12 to 15
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderTopColor: colors.black,
          marginTop: -3,
        },

        iconContainer: {
          zIndex: 100,
          backgroundColor: colors.overlayBackground,
          width: 35,
          height: 35,
          borderRadius: 35,
          position: 'absolute',
          top: 5,
          right: 10,
          flexDirection: 'row',
          gap: 10,
        },
        imageEvent: {
          width: 300,
          height: 100,
          borderRadius: 10,
        },
        dateRangeContainer: {
          zIndex: 1,
          position: 'absolute',
          top: 10,
          left: 10,
          backgroundColor: colors.overlayBackground,
          borderRadius: 10,
          padding: 6,
        },
        eventTitle: {
          fontWeight: 'bold',
          color: colors.white,
          fontSize: 12,
          position: 'absolute',
          bottom: 20,
          left: 10,
          zIndex: 100,
        },
        eventCard: {
          height: 100,
          width: 300,
          alignSelf: 'center',
          borderRadius: 10,
          zIndex: 1909000,
        },
        calloutWrapper: {
          alignItems: 'center',
        },
        calloutContainer: {
          width: 200,
          backgroundColor: colors.white + 'B3',
          borderRadius: 8,
          paddingHorizontal: 8,
          paddingVertical: 3,
        },
        calloutTitle: {
          fontWeight: 'bold',
          fontSize: 11,
        },
        calloutDescription: {
          fontSize: 11,
          color: 'orange',
          position: 'absolute',
          bottom: 7,
          left: 10,
          zIndex: 100,
        },
        arrow: {
          width: 0,
          height: 0,
          borderLeftWidth: 10,
          borderRightWidth: 10,
          borderTopWidth: 10,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          alignSelf: 'center',
          borderTopColor: colors.white + '4D',
          marginTop: -1,
        },
      }),
    [colors],
  );
  const {t} = useTranslation();
  const {data: businessAccountData} = useGetBusinessAccount(auth().currentUser!.uid);
  const {data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch, remove} = useAllEvents({
    distance_km: radius,
    tab: TABS.DISCOVER,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
    filter_type: selectedTimeframe.title === t('home.upcoming_events') ? undefined : selectedTimeframe.title,
    q: globalInputValue,
    isNeighbourhood: isNeighbourhood,
  });
  const {top} = useSafeAreaInsets();
  const {width, height} = useSafeAreaFrame();
  const isFocused = useIsFocused();
  const sheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['1%', '100%'], []);
  const [currentPosition, setCurrentPosition] = useState(0);
  const eventsList = getListFromPages<Event>(data);
  const [refreshing, setRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState({latitude: 51.5072, longitude: 0.1276});
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const mapRef = useRef<MapView>(null);
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const {getCurrentPosition} = useGetCurrentPosition();
  const {currentPositionState, setCurrentPositionState} = useMapsContext();
  const [prevRegion, setPrevRegion] = useState<Region | null>(null);
  const lastRegionRef = useRef<Region | null>(null); // Store the last region to compare

  // Helper function to check if two regions are effectively the same
  const areRegionsEqual = (region1: Region | null, region2: Region | null): boolean => {
    if (!region1 || !region2) {
      return false;
    }
    const latDiff = Math.abs(region1.latitude - region2.latitude);
    const lonDiff = Math.abs(region1.longitude - region2.longitude);
    return latDiff < 0.0001 && lonDiff < 0.0001; // Adjust threshold as needed
  };

  // Update map only when currentPositionState changes significantly
  useEffect(() => {
    if (currentPositionState && currentPositionState.latitude && mapRef.current) {
      const newRegion: Region = {
        latitude: currentPositionState.latitude,
        longitude: currentPositionState.longitude,
        latitudeDelta: 0.015,
        longitudeDelta: 0.015,
      };

      // Only animate if the new region is different from the last known region
      if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
        mapRef.current.animateToRegion(newRegion);
        lastRegionRef.current = newRegion; // Update last known region
      }
    }
  }, [currentPositionState]);

  /*  useEffect(() => {
    if (isNeighbourhood && eventsList && eventsList.length > 0 && mapRef.current) {
      const event = eventsList[eventsList.length - 1]; // Get the last event
      const newRegion: Region = {
        latitude: event.coords.lat,
        longitude: event.coords.long,
        latitudeDelta: 0.015,
        longitudeDelta: 0.015,
      };

      // Only animate if the new region is different from the last known region
      if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
        mapRef.current.animateToRegion(newRegion);
        lastRegionRef.current = newRegion; // Update last known region
      }
    }
  }, [eventsList, isNeighbourhood]) */

  useEffect(() => {
    if (eventsList && globalInputValue) {
      DeviceEventEmitter.emit('onSearchResult', {events: eventsList});
    }
  }, [eventsList, globalInputValue]);

  useEffect(() => {
    remove();
    refetch();
    DeviceEventEmitter.addListener('eventSelection', data => {
      setSelectedEvent(data.event);
    });
  }, [globalInputValue, remove, refetch]);

  useEffect(() => {
    getCurrentPosition();
  }, []);

  useEffect(() => {
    const eventListener = DeviceEventEmitter.addListener('homeLocationChange', data => {
      if (sheetRef.current) {
        sheetRef.current.snapToIndex(0);
      }
      if (data.lat && data.long && mapRef.current) {
        const newRegion: Region = {
          latitude: data.lat,
          longitude: data.long,
          latitudeDelta: 0.015,
          longitudeDelta: 0.015,
        };
        if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
          mapRef.current.animateToRegion(newRegion);
          lastRegionRef.current = newRegion;
        }
      }
    });

    return () => {
      eventListener.remove();
    };
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setSelectedEvent(null);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {});

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    remove();
    refetch();
  }, [refetch, remove, radius, isForKids, selectedTimeframe, isNeighbourhood]);

  // Control BottomSheet position when map/list view changes
  useEffect(() => {
    if (sheetRef.current) {
      const targetIndex = isMapView ? 0 : 1;
      if (currentPosition !== targetIndex) {
        sheetRef.current.snapToIndex(targetIndex);
      }
    }
  }, [isMapView, currentPosition]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleSheetChange = useCallback((index: number) => {
    console.log('handleSheetChange', index);
    setSelectedEvent(null);
    setCurrentPosition(index);
    // Don't automatically change map view based on sheet position
    // Only the switch button should control this
  }, []);

  const filteredEvents = useMemo(() => {
    let events = eventsList;

    // Apply time-based filtering first
    if (events && filterEvents) {
      events = filterEvents(events, selectedTimeframe);
    }

    // Then apply free events filter if needed
    if (isFree && events) {
      events = events.filter(event => event.is_paid == false);
    }

    // Apply client-side distance filtering as a safety net (except for neighbourhood events which may have different logic)
    if (events && currentPositionState && !isNeighbourhood) {
      const beforeDistanceFilter = events.length;
      events = events.filter(event => {
        const distance = haversine(
          {latitude: currentPositionState.latitude, longitude: currentPositionState.longitude},
          {latitude: event.coords.lat, longitude: event.coords.long},
          {unit: 'km'},
        );
        return distance <= radius;
      });

      if (beforeDistanceFilter !== events.length) {
        console.log(
          `🔍 Client-side distance filter: ${beforeDistanceFilter} → ${events.length} events (removed ${beforeDistanceFilter - events.length} events beyond ${radius}km)`,
        );
      }
    }

    // Debug logging for filtered events
    console.log(
      `🗂️ HomeContent filtered events: ${events?.length || 0} events (isNeighbourhood: ${isNeighbourhood}, radius: ${radius}km)`,
    );

    // Log first few events with their coordinates for debugging
    if (events && events.length > 0 && currentPositionState) {
      const eventsWithDistance = events.slice(0, 3).map(event => {
        const distance = haversine(
          {latitude: currentPositionState.latitude, longitude: currentPositionState.longitude},
          {latitude: event.coords.lat, longitude: event.coords.long},
          {unit: 'km'},
        );
        return {
          id: event.event_id,
          name: event.name,
          coords: event.coords,
          calculatedDistance: Math.round(distance * 10) / 10, // Round to 1 decimal place
        };
      });
      console.log('📍 First 3 events with calculated distances:', eventsWithDistance);

      // Check if any events are beyond the radius (this should not happen after client-side filtering)
      const eventsOutsideRadius = eventsWithDistance.filter(event => event.calculatedDistance > radius);
      if (eventsOutsideRadius.length > 0) {
        console.warn(`⚠️ Found ${eventsOutsideRadius.length} events outside ${radius}km radius:`, eventsOutsideRadius);
      }
    }

    return events;
  }, [eventsList, isFree, filterEvents, selectedTimeframe, isNeighbourhood, radius, currentPositionState]);

  // Map event prefetching - prefetch when 10 or fewer events
  const {prefetchEvent, isPrefetched, getPrefetchStats} = useMapEventPrefetch({
    events: filteredEvents || [],
    enabled: isMapView && (filteredEvents?.length || 0) <= 10,
    maxEventsToPrefetch: 10,
    prefetchDelay: 1000,
    prefetchStrategy: 'delayed',
  });

  const onEventClick = () => {
    if (selectedEvent) {
      navigation.navigate(SCREENS.HOME_EVENT, {
        tag: `${selectedEvent.event_id}=tag`,
        eventId: selectedEvent.event_id,
        item: selectedEvent,
        fromMapPin: true, // Add flag to indicate this came from a map pin
      });
    }
  };

  const handleLocationChange = useCallback(
    async (location: Region | null) => {
      if (!location) {
        return;
      }

      try {
        // Emit loading start event for search bar
        DeviceEventEmitter.emit('mapLoadingStart');

        if (businessAccountData?.uid) {
          const payload = {uid: auth().currentUser!.uid, coords: {lat: location.latitude, long: location.longitude}};
          await updateBusinessMutation(payload);
          return;
        }

        await updateUserMutation({
          coords_real: {lat: location.latitude, long: location.longitude},
        });
        refetch();
      } catch (error) {
        console.log(error);
      }
    },
    [businessAccountData, updateBusinessMutation, updateUserMutation, refetch],
  );

  const handleRegionChangeComplete = useCallback(
    (newRegion: Region) => {
      // Store the exact region from onRegionChangeComplete
      lastRegionRef.current = newRegion;
      Keyboard.dismiss();
      DeviceEventEmitter.emit('hideSuggestion');

      // Update state only if the region has changed significantly
      if (!areRegionsEqual(newRegion, prevRegion)) {
        setCurrentPositionState({
          latitude: newRegion.latitude,
          longitude: newRegion.longitude,
          address: '',
        });

        const distance = prevRegion
          ? haversine(
              {latitude: prevRegion.latitude, longitude: prevRegion.longitude},
              {latitude: newRegion.latitude, longitude: newRegion.longitude},
              {unit: 'km'},
            )
          : Infinity;

        if (distance >= 2 || !prevRegion) {
          handleLocationChange(newRegion);
          setPrevRegion(newRegion);
        }
      }
    },
    [prevRegion, handleLocationChange, setCurrentPositionState],
  );

  useEffect(() => {
    DeviceEventEmitter.emit('eventSelection', {event: selectedEvent});
  }, [selectedEvent]);

  // Emit loading end event when loading states change
  useEffect(() => {
    if (!isLoading && !isFetching) {
      DeviceEventEmitter.emit('mapLoadingEnd');
    }
  }, [isLoading, isFetching]);

  return (
    <View style={{flex: 1}}>
      <View style={{flex: 1, marginTop: Platform.OS === 'ios' ? 75 + top : 85 + top}}>
        <View style={{flex: 1}}>
          <MapView
            ref={mapRef}
            style={{flex: 1}}
            provider={PROVIDER_GOOGLE}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={false}
            toolbarEnabled={false}
            showsTraffic={false}
            showsBuildings={false}
            showsIndoors={false}
            clusterColor={colors.eventInfluencer}
            customMapStyle={getMapStyle(isDarkMode)}
            initialRegion={{
              latitude: currentPositionState?.latitude ?? currentLocation.latitude,
              longitude: currentPositionState?.longitude ?? currentLocation.longitude,
              latitudeDelta: 0.015,
              longitudeDelta: 0.015,
            }}
            onPress={() => {
              Keyboard.dismiss();
              DeviceEventEmitter.emit('hideSuggestion');
            }}
            onRegionChangeComplete={handleRegionChangeComplete}>
            {filteredEvents?.map((event, index) => (
              <Marker
                key={`${event.event_id}-${index}`} // Better key for React optimization
                coordinate={{
                  latitude: event.coords.lat,
                  longitude: event.coords.long,
                }}
                onPress={() => {
                  Keyboard.dismiss();
                  setSelectedEvent(event);

                  // Trigger immediate prefetch for this event if not already prefetched
                  if (!isPrefetched(event.event_id)) {
                    prefetchEvent(event.event_id);
                  }
                }}
                tracksViewChanges={false} // Optimize performance
                style={{
                  // Increase touch target area for better UX
                  padding: 10,
                }}>
                {event.event_type === 'influencer' ? (
                  <View style={styles.pinContainer}>
                    <View style={styles.pinCircle}>
                      <FastImage source={{uri: event.host_photo}} style={styles.pinImage} />
                    </View>
                    <View style={styles.pinTriangle} />
                  </View>
                ) : (
                  <AnimatedPin isSelected={selectedEvent?.event_id === event.event_id} eventType={event.event_type} />
                )}
              </Marker>
            ))}
          </MapView>
        </View>
        <BottomSheet
          ref={sheetRef}
          snapPoints={snapPoints}
          index={isMapView ? 0 : 1}
          enablePanDownToClose={false}
          enableDynamicSizing={false}
          handleComponent={() => null}
          onChange={handleSheetChange}
          handleStyle={{backgroundColor: colors.background, borderRadius: 0}}
          backgroundStyle={{backgroundColor: colors.background, borderRadius: 0}}
          activeOffsetY={[]}
          failOffsetX={[-999, 999]}
          enableContentPanningGesture={false}
          enableHandlePanningGesture={false}>
          <ModernEventsList
            data={filteredEvents}
            isLoading={isLoading}
            isFetching={isFetching}
            isFetchingNextPage={isFetchingNextPage}
            refreshing={refreshing}
            onRefresh={() => refetch()}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            isMyEvents={false}
            emptyStateTitle="No events found"
            emptyStateSubtitle="Try adjusting your search or location to find events near you!"
            estimatedItemSize={280}
            contentContainerStyle={{
              paddingTop: spacing.xs,
              paddingBottom: responsive.getValue({xs: 80, sm: 100, md: 120}, 100),
            }}
          />
        </BottomSheet>
      </View>
    </View>
  );
};

export default HomeContent;
