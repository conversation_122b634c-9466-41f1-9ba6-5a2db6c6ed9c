import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {IChild} from '~types/api/user';

export function useGetUserChildren() {
  return useQuery<IChild[], Error>(
    'userChildren', // Query key
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}user/children`, {
        method: 'GET',
        headers: {
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user children');
      }

      const data = await response.json();

      return data as IChild[];
    },
  );
}
