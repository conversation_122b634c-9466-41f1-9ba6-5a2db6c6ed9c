import Config from 'react-native-config';
import {useMutation} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {CreatePaymentIntentPayload, PaymentIntentResponse} from '~types/api/event';

export function useCreatePaymentIntent() {
  return useMutation<PaymentIntentResponse, Error, CreatePaymentIntentPayload>(
    async (payloadBody: CreatePaymentIntentPayload) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${payloadBody.eventId}/create-payment-intent`, {
        method: 'POST',
        body: JSON.stringify(payloadBody),
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });
      console.log(response, token, payloadBody, 'responseresponseresponse');

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const data = await response.json();

      return data;
    },
  );
}
