import {Text, TouchableOpacity, View} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  item: {
    title: string;
    isActive: boolean;
  };
  onPress: () => void;
  flex?: number;
}

const ModalItem = ({item, onPress, flex = 0.2}: IProps) => {
  const {colors} = useTheme();
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        borderRadius: 5,
        flex: flex,
        width: '100%',
        borderWidth: 1,
        borderColor: item.isActive ? colors.primary : colors.border,
        flexDirection: 'row',
        paddingHorizontal: 16,
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      <Text style={{color: colors.textPrimary, fontWeight: '500', fontSize: 15}}>{item.title}</Text>
      <View
        style={{
          height: 17,
          width: 17,
          borderRadius: 50,
          borderWidth: 1,
          borderColor: item.isActive ? colors.primary : colors.border,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        {item.isActive && <View style={{height: 8.5, width: 8.5, borderRadius: 50, backgroundColor: colors.primary}} />}
      </View>
    </TouchableOpacity>
  );
};

export default ModalItem;
