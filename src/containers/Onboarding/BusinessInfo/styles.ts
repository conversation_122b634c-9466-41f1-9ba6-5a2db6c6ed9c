import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    contentContainer: {
      flexGrow: 1,
    },
    flex: {
      flex: 1,
    },
    container: {
      flex: 1,
      marginTop: 35,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 30,
    },
    formikWrapper: {flex: 1, width: '100%'},
    bottomButtonWrapper: {
      width: '100%',
      flex: 1,
      justifyContent: 'flex-end',
      // marginBottom: bottom + 20,
    },
    bottomButton: {
      width: '100%',
      height: 40,
      borderRadius: 6,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomButtonText: {fontSize: 15, fontWeight: '500', color: colors.white},
  });

export default getStyles;
