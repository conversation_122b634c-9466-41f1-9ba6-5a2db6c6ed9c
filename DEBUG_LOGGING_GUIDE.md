# Debug Logging System for App Performance Analysis

This guide explains the comprehensive debug logging system implemented to help identify performance bottlenecks during app startup and loading.

## 🎯 Overview

The debug logging system provides detailed timing information and state tracking for:
- App initialization and startup
- API calls and authentication
- Navigation and loading states
- Image preloading and other async operations

## 🔧 Configuration

### Babel Configuration
The system uses `babel-plugin-transform-remove-console` to automatically strip console logs in production builds while keeping them in debug mode.

**babel.config.js:**
```javascript
env: {
  production: {
    plugins: [
      [
        'transform-remove-console',
        {
          exclude: ['error', 'warn'], // Keep error and warn logs in production
        },
      ],
    ],
  },
}
```

### Debug Logger Utility
Located at `src/utils/debugLogger.ts`, this utility provides categorized logging methods:

- `appStartup()` - App initialization events
- `navigation()` - Navigation and routing events  
- `api()` - API calls and network requests
- `auth()` - Authentication events
- `timing()` - Performance timing measurements
- `loading()` - Loading state changes
- `success()` - Successful operations
- `error()` - Error events (always logged)
- `warn()` - Warning events (always logged)

## 📊 Performance Tracking

### Key Metrics Being Tracked

1. **App Startup Time**
   - OneSignal initialization
   - Image preloading
   - Splash screen timing
   - Total app startup duration

2. **API Call Performance**
   - Firebase auth token retrieval
   - User type API call
   - User account API call  
   - Business account API call
   - Individual request timing

3. **Navigation Loading States**
   - Authentication state changes
   - Loading state transitions
   - Screen navigation timing

### Sample Debug Output

```
🚀 [APP_STARTUP] App component started rendering
🔔 [APP_STARTUP] Initializing OneSignal
✅ [APP_STARTUP] OneSignal initialization completed
🖼️ [APP_STARTUP] Starting image preloading
⏱️ [TIMING] Image preloading initiated: 45ms
🧭 [NAVIGATION] Navigation component started rendering
🔍 [NAVIGATION] Starting API calls for user data
🔍 [API] Starting getUserType API call
🔑 [API] getUserType: Getting auth token
⏱️ [TIMING] getUserType: Auth token obtained: 12ms
🌐 [API] getUserType: Making API request
⏱️ [TIMING] getUserType: Request completed: 234ms
⏱️ [TIMING] getUserType: Total time: 246ms
```

## 🚀 Usage

### Import and Use
```typescript
import { appStartup, api, timing, navigation } from '~utils/debugLogger';

// Log app startup events
appStartup('Component initialized');

// Log API calls with timing
const startTime = Date.now();
// ... perform operation
timing('Operation completed', startTime);

// Log with additional data
navigation('Loading state changed', { isLoading: true, userType: 'personal' });
```

### Performance Measurement Helpers
```typescript
// Synchronous operation timing
const result = measurePerformance('Heavy computation', () => {
  return performHeavyComputation();
});

// Asynchronous operation timing  
const data = await measureAsyncPerformance('API call', async () => {
  return await fetchUserData();
});
```

## 🔍 Analyzing Performance Issues

### Common Bottlenecks to Look For

1. **Slow API Calls**
   - Look for timing logs > 500ms
   - Check auth token retrieval times
   - Monitor network request durations

2. **Authentication Delays**
   - Firebase auth state changes
   - Token retrieval performance
   - Multiple auth checks

3. **Loading State Issues**
   - Unnecessary loading states
   - Concurrent API calls
   - State update delays

4. **Image/Asset Loading**
   - Image preloading times
   - Asset resolution delays

### Debug Log Categories

- 🚀 `[APP_STARTUP]` - App initialization
- 🧭 `[NAVIGATION]` - Navigation events
- 🔍 `[API]` - API calls
- 🔐 `[AUTH]` - Authentication
- ⏱️ `[TIMING]` - Performance measurements
- ⏳ `[LOADING]` - Loading states
- ✅ `[SUCCESS]` - Successful operations
- ❌ `[ERROR]` - Errors
- ⚠️ `[WARNING]` - Warnings

## 🛠️ Development vs Production

- **Development**: All debug logs are shown
- **Production**: Only error and warn logs are kept
- **Automatic**: Babel plugin handles stripping based on build environment

## 📱 Testing Performance

1. **Enable Debug Mode**: Ensure you're running in development mode
2. **Monitor Console**: Watch for timing logs and bottlenecks
3. **Analyze Patterns**: Look for consistently slow operations
4. **Profile Network**: Check API call durations
5. **Track Loading States**: Monitor unnecessary loading delays

## 🎯 Next Steps

Based on the debug logs, you can:
1. Identify the slowest operations
2. Optimize API calls and caching
3. Improve loading state management
4. Reduce unnecessary re-renders
5. Optimize asset loading strategies

The debug logging system provides comprehensive visibility into app performance, making it easier to identify and resolve loading time issues.
