import {Path, Rect, Svg} from 'react-native-svg';

const SettingsHelpIcon = () => (
  <Svg width="30" height="30" viewBox="0 0 30 30" fill="none">
    <Rect width="30" height="30" rx="7" fill="#FF3B30" />
    <Path
      d="M14.9912 23.7422C19.9746 23.7422 24.0879 19.6289 24.0879 14.6543C24.0879 9.67969 19.9658 5.56641 14.9824 5.56641C10.0078 5.56641 5.90332 9.67969 5.90332 14.6543C5.90332 19.6289 10.0166 23.7422 14.9912 23.7422ZM14.7803 16.4648C14.209 16.4648 13.8926 16.2012 13.8926 15.6738V15.5771C13.8926 14.751 14.3672 14.2852 15.0176 13.8281C15.791 13.292 16.1689 13.002 16.1689 12.4395C16.1689 11.833 15.7031 11.4287 14.9824 11.4287C14.4551 11.4287 14.0684 11.6924 13.7783 12.1406C13.4971 12.457 13.3652 12.7295 12.8379 12.7295C12.4072 12.7295 12.0557 12.4482 12.0557 12.0088C12.0557 11.833 12.0908 11.6748 12.1523 11.5166C12.4424 10.6553 13.4971 9.95215 15.0703 9.95215C16.7051 9.95215 18.085 10.8223 18.085 12.3516C18.085 13.4062 17.5049 13.9336 16.5908 14.5225C16.0107 14.9004 15.6768 15.2168 15.6504 15.6914C15.6504 15.7178 15.6416 15.7617 15.6416 15.7969C15.6152 16.1748 15.29 16.4648 14.7803 16.4648ZM14.7715 19.2773C14.1738 19.2773 13.6816 18.8467 13.6816 18.2666C13.6816 17.6865 14.165 17.2559 14.7715 17.2559C15.3691 17.2559 15.8525 17.6865 15.8525 18.2666C15.8525 18.8555 15.3604 19.2773 14.7715 19.2773Z"
      fill="white"
    />
  </Svg>
);

export default SettingsHelpIcon;
