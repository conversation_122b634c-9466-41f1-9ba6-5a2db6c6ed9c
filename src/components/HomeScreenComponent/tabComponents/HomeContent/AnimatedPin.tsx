import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';
import ModernEventPin from '~assets/icons/ModernEventPin';
import {haptics} from '~utils/haptics';

interface AnimatedPinProps {
  isSelected: boolean;
  eventType: string;
}

const AnimatedPin: React.FC<AnimatedPinProps> = ({isSelected, eventType}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    if (isSelected) {
      // Provide haptic feedback when pin is selected
      haptics.mapPinSelect();

      // Start pulsing animation with improved smoothness
      pulseAnim.current = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2, // Reduced from 1.3 to 1.2 for smoother animation
            duration: 800, // Increased duration for smoother animation
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1, // Scale back down
            duration: 800, // Increased duration for smoother animation
            useNativeDriver: true,
          }),
        ]),
      );
      pulseAnim.current.start();
    } else {
      // Stop pulsing and return to default scale
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300, // Increased duration for smoother deselection
        useNativeDriver: true,
      }).start();
    }

    // Cleanup on unmount or when isSelected changes
    return () => {
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }
    };
  }, [isSelected, scaleAnim]);

  // Map event types to our modern pin types
  const getEventPinType = (type: string): 'business' | 'influencer' | 'community' | 'pyxi_select' => {
    switch (type) {
      case 'business':
        return 'business';
      case 'influencer':
        return 'influencer';
      case 'community':
        return 'community';
      case 'pyxi_select':
        return 'pyxi_select';
      default:
        return 'community';
    }
  };

  return (
    <Animated.View
      style={[
        styles.iconContainer,
        {
          transform: [{scale: scaleAnim}],
          // Increased size to accommodate 1.2x scaling (50 * 1.2 = 60, plus padding)
          // Added extra padding for smooth animation
          height: 100,
          width: 100,
          // Allow content to overflow the container bounds
          overflow: 'visible',
        },
      ]}>
      <ModernEventPin
        eventType={getEventPinType(eventType)}
        size={50} // Increased from 40 to 50
        isSelected={false} // Pass false to prevent double scaling
        disableInternalScaling={true} // New prop to disable internal scaling
      />
    </Animated.View>
  );
};

export default AnimatedPin;

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // Ensure content can overflow for smooth animations
    overflow: 'visible',
    // Add z-index to ensure pin appears above other elements during animation
    zIndex: 1000,
  },
});
