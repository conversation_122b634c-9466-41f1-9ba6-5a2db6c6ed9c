import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: 12,
      overflow: 'hidden',
    },
    title: {
      textAlign: 'center',
      marginTop: 2,
    },
    label: {
      marginRight: 10,
    },
    buttonContainer: {
      width: 75,
      height: 40,
      borderRadius: 8,
      backgroundColor: colors.gray400,
      marginRight: 15,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonLabel: {},
    selectedButton: {
      borderColor: colors.error,
      borderWidth: 1,
    },
    datePickerContainer: {
      marginTop: 30,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    doneButton: {
      width: '100%',
      backgroundColor: colors.error,
      borderRadius: 10,
      marginTop: 8,
      alignSelf: 'center',
      alignItems: 'center',
    },
    doneButtonLabel: {
      marginVertical: 10,
      color: colors.white,
    },
  });

export default getStyles;
