import {StyleSheet} from 'react-native';

const getStyles = (colors: any) =>
  StyleSheet.create({
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 10,
      marginTop: 12,
      paddingBottom: 8,
      paddingTop: 4,
    },
    timelineContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      marginStart: 10,
      marginTop: 5,
    },
    dotOutline: {
      width: 10,
      height: 10,
      borderRadius: 5,
      borderWidth: 2,
      borderColor: colors.border,
    },
    dot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: colors.border,
    },
    line: {
      width: 2,
      height: 30,
      borderStyle: 'dashed',
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: 'transparent',
    },
    dateTimeContainer: {
      flex: 1,
    },
    addEndDateContainer: {
      alignSelf: 'flex-end',
      marginTop: 4,
    },
    addEndDateText: {
      lineHeight: 17,
      fontSize: 14,
      fontWeight: '500',
      color: colors.statusPurple,
    },
    title: {
      fontSize: 17,
      fontWeight: '400',
      marginLeft: 10,
    },
    optionList: {
      position: 'absolute',
      top: 70,
      zIndex: 100,
      right: 0,
      left: 0,
      backgroundColor: colors.white,
    },
    errorText: {
      fontSize: 12,
      color: colors.error,
      marginTop: 4,
    },
    durationInput: {
      height: 40,
      borderBottomWidth: 1,
      borderBottomColor: colors.gray400,
      borderRadius: 8,
      paddingHorizontal: 10,
      fontSize: 16,
      backgroundColor: colors.white,
      width: '100%',
    },
    dropdownItem: {
      padding: 10,
      backgroundColor: colors.gray100,
      borderBottomWidth: 1,
      borderBottomColor: colors.gray100,
    },
    autoCompleteContainer: {
      position: 'relative',
      flex: 1,
      marginStart: 10,
      zIndex: 100,
    },
    autoCompleteMainContainer: {
      marginTop: 8,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.white,
      padding: 10,
      zIndex: 100,
      borderRadius: 10,
    },
  });

export default getStyles;
