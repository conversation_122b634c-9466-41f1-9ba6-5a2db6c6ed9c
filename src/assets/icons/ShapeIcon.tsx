import {Path, Svg} from 'react-native-svg';

const ShapeIcon = ({isMessageFromCurrentUser = false}: {isMessageFromCurrentUser?: boolean}) => (
  <Svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    style={{transform: [{scaleX: isMessageFromCurrentUser ? 1 : -1}]}}>
    <Path
      d="M11.5 10.5C12.0014 13.5086 14.8333 16.3333 16.5 17C10.1 17 6 14.8333 5 13.5L0 15L0.5 0H11V2V4V4.5C11 5.5 11 7.5 11.5 10.5Z"
      fill={isMessageFromCurrentUser ? '#2E2EAB' : '#E9E9EB'}
    />
  </Svg>
);

export default ShapeIcon;
