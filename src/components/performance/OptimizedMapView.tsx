import React, {useCallback, useMemo, useRef, useState, useEffect} from 'react';
import {View, Dimensions, Text, TouchableOpacity} from 'react-native';
import MapView, {Marker, Region, MapViewProps} from 'react-native-maps';
import {deferHeavyOperation, processInChunks} from '~utils/performance/advancedOptimizations';
import {performanceMonitor} from '~utils/performance/performanceMonitor';

interface OptimizedMapViewProps extends Omit<MapViewProps, 'children'> {
  markers?: Array<{
    id: string;
    coordinate: {latitude: number; longitude: number};
    title?: string;
    description?: string;
    data?: any;
  }>;
  onMarkerPress?: (marker: any) => void;

  // Performance optimizations
  enableClustering?: boolean;
  clusterRadius?: number;
  maxZoomLevel?: number;
  enableMarkerCaching?: boolean;
  enableViewportCulling?: boolean;
  markerBatchSize?: number;

  // Advanced features
  enableHeatmap?: boolean;
  heatmapData?: Array<{latitude: number; longitude: number; weight?: number}>;
  enableTrafficLayer?: boolean;
  enableSatelliteToggle?: boolean;
}

interface ClusterMarker {
  id: string;
  coordinate: {latitude: number; longitude: number};
  count: number;
  markers: any[];
}

const {width, height} = Dimensions.get('window');
const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.0922;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const OptimizedMapView: React.FC<OptimizedMapViewProps> = ({
  markers = [],
  onMarkerPress,
  enableClustering = true,
  clusterRadius = 50,
  maxZoomLevel = 15,
  enableMarkerCaching = true,
  enableViewportCulling = true,
  markerBatchSize = 100,
  enableHeatmap = false,
  heatmapData = [],
  enableTrafficLayer = false,
  enableSatelliteToggle = false,
  ...mapProps
}) => {
  const mapRef = useRef<MapView>(null);
  const [region, setRegion] = useState<Region>({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: LATITUDE_DELTA,
    longitudeDelta: LONGITUDE_DELTA,
  });
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const [visibleMarkers, setVisibleMarkers] = useState<any[]>([]);
  const [clusters, setClusters] = useState<ClusterMarker[]>([]);

  // Marker cache for performance
  const markerCache = useRef<Map<string, React.ReactElement>>(new Map());
  const lastRegion = useRef<Region>(region);

  // Calculate zoom level from region
  const getZoomLevel = useCallback((region: Region): number => {
    const angle = region.longitudeDelta;
    return Math.round(Math.log(360 / angle) / Math.LN2);
  }, []);

  // Viewport culling - only show markers in visible area
  const getMarkersInViewport = useCallback(
    (markers: any[], region: Region) => {
      if (!enableViewportCulling) {
        return markers;
      }

      const padding = 0.1; // 10% padding around viewport
      const minLat = region.latitude - region.latitudeDelta * (0.5 + padding);
      const maxLat = region.latitude + region.latitudeDelta * (0.5 + padding);
      const minLng = region.longitude - region.longitudeDelta * (0.5 + padding);
      const maxLng = region.longitude + region.longitudeDelta * (0.5 + padding);

      return markers.filter(marker => {
        const {latitude, longitude} = marker.coordinate;
        return latitude >= minLat && latitude <= maxLat && longitude >= minLng && longitude <= maxLng;
      });
    },
    [enableViewportCulling],
  );

  // Clustering algorithm
  const clusterMarkers = useCallback(
    async (markers: any[], region: Region) => {
      if (!enableClustering || getZoomLevel(region) > maxZoomLevel) {
        return {clusters: [], individualMarkers: markers};
      }

      const clusters: ClusterMarker[] = [];
      const processed = new Set<string>();
      const individualMarkers: any[] = [];

      await processInChunks(
        markers,
        async chunk => {
          for (const marker of chunk) {
            if (processed.has(marker.id)) {
              continue;
            }

            const nearby = markers.filter(other => {
              if (processed.has(other.id) || other.id === marker.id) {
                return false;
              }

              const distance = getDistance(marker.coordinate, other.coordinate);
              return distance < clusterRadius;
            });

            if (nearby.length > 0) {
              // Create cluster
              const allMarkers = [marker, ...nearby];
              const centerCoordinate = getCenterCoordinate(allMarkers.map(m => m.coordinate));

              clusters.push({
                id: `cluster-${marker.id}`,
                coordinate: centerCoordinate,
                count: allMarkers.length,
                markers: allMarkers,
              });

              // Mark as processed
              allMarkers.forEach(m => processed.add(m.id));
            } else {
              individualMarkers.push(marker);
              processed.add(marker.id);
            }
          }
          return chunk;
        },
        markerBatchSize,
        5,
      );

      return {clusters, individualMarkers};
    },
    [enableClustering, maxZoomLevel, clusterRadius, markerBatchSize],
  );

  // Update visible markers when region or markers change
  useEffect(() => {
    const updateVisibleMarkers = async () => {
      const startTime = Date.now();

      try {
        // Get markers in viewport
        const viewportMarkers = getMarkersInViewport(markers, region);

        // Apply clustering
        const {clusters: newClusters, individualMarkers} = await clusterMarkers(viewportMarkers, region);

        setClusters(newClusters);
        setVisibleMarkers(individualMarkers);

        const processingTime = Date.now() - startTime;
        if (processingTime > 100) {
          console.warn(`Map processing took ${processingTime}ms`);
        }

        performanceMonitor.recordRequest();
      } catch (error) {
        console.error('Error updating visible markers:', error);
        performanceMonitor.recordError();
      }
    };

    // Debounce updates to avoid excessive processing
    const timeoutId = setTimeout(() => {
      deferHeavyOperation(updateVisibleMarkers, 'normal');
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [markers, region, getMarkersInViewport, clusterMarkers]);

  // Cached marker rendering
  const renderMarker = useCallback(
    (marker: any) => {
      if (enableMarkerCaching && markerCache.current.has(marker.id)) {
        return markerCache.current.get(marker.id)!;
      }

      const markerElement = (
        <Marker
          key={marker.id}
          coordinate={marker.coordinate}
          title={marker.title}
          description={marker.description}
          onPress={() => onMarkerPress?.(marker)}
        />
      );

      if (enableMarkerCaching) {
        markerCache.current.set(marker.id, markerElement);
      }

      return markerElement;
    },
    [enableMarkerCaching, onMarkerPress],
  );

  // Render cluster marker
  const renderCluster = useCallback(
    (cluster: ClusterMarker) => (
      <Marker
        key={cluster.id}
        coordinate={cluster.coordinate}
        onPress={() => {
          // Zoom into cluster
          const region = getClusterRegion(cluster.markers);
          mapRef.current?.animateToRegion(region, 500);
        }}>
        <View
          style={{
            backgroundColor: '#007AFF',
            borderRadius: 20,
            width: 40,
            height: 40,
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 2,
            borderColor: 'white',
          }}>
          <Text style={{color: 'white', fontWeight: 'bold'}}>{cluster.count}</Text>
        </View>
      </Marker>
    ),
    [],
  );

  // Handle region change
  const onRegionChangeComplete = useCallback((newRegion: Region) => {
    // Only update if significant change
    const threshold = 0.001;
    const latDiff = Math.abs(newRegion.latitude - lastRegion.current.latitude);
    const lngDiff = Math.abs(newRegion.longitude - lastRegion.current.longitude);
    const deltaChange = Math.abs(newRegion.latitudeDelta - lastRegion.current.latitudeDelta);

    if (latDiff > threshold || lngDiff > threshold || deltaChange > threshold) {
      setRegion(newRegion);
      lastRegion.current = newRegion;
    }
  }, []);

  // Memoized markers to prevent unnecessary re-renders
  const memoizedMarkers = useMemo(() => {
    return [...clusters.map(renderCluster), ...visibleMarkers.map(renderMarker)];
  }, [clusters, visibleMarkers, renderCluster, renderMarker]);

  return (
    <View style={{flex: 1}}>
      <MapView
        ref={mapRef}
        style={{flex: 1}}
        mapType={mapType}
        showsTraffic={enableTrafficLayer}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={false}
        toolbarEnabled={false}
        onRegionChangeComplete={onRegionChangeComplete}
        {...mapProps}>
        {memoizedMarkers}
      </MapView>

      {enableSatelliteToggle && (
        <View
          style={{
            position: 'absolute',
            top: 50,
            right: 20,
            backgroundColor: 'white',
            borderRadius: 8,
            padding: 8,
          }}>
          <TouchableOpacity onPress={() => setMapType(mapType === 'standard' ? 'satellite' : 'standard')}>
            <Text>{mapType === 'standard' ? 'Satellite' : 'Standard'}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

// Helper functions
function getDistance(
  coord1: {latitude: number; longitude: number},
  coord2: {latitude: number; longitude: number},
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (coord1.latitude * Math.PI) / 180;
  const φ2 = (coord2.latitude * Math.PI) / 180;
  const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180;
  const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

function getCenterCoordinate(coordinates: Array<{latitude: number; longitude: number}>): {
  latitude: number;
  longitude: number;
} {
  const totalLat = coordinates.reduce((sum, coord) => sum + coord.latitude, 0);
  const totalLng = coordinates.reduce((sum, coord) => sum + coord.longitude, 0);

  return {
    latitude: totalLat / coordinates.length,
    longitude: totalLng / coordinates.length,
  };
}

function getClusterRegion(markers: any[]): Region {
  const coordinates = markers.map(m => m.coordinate);
  const minLat = Math.min(...coordinates.map(c => c.latitude));
  const maxLat = Math.max(...coordinates.map(c => c.latitude));
  const minLng = Math.min(...coordinates.map(c => c.longitude));
  const maxLng = Math.max(...coordinates.map(c => c.longitude));

  const centerLat = (minLat + maxLat) / 2;
  const centerLng = (minLng + maxLng) / 2;
  const latDelta = (maxLat - minLat) * 1.2; // Add 20% padding
  const lngDelta = (maxLng - minLng) * 1.2;

  return {
    latitude: centerLat,
    longitude: centerLng,
    latitudeDelta: Math.max(latDelta, 0.01),
    longitudeDelta: Math.max(lngDelta, 0.01),
  };
}

export default OptimizedMapView;
