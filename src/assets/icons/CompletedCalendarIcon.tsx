import {Path, Svg} from 'react-native-svg';

const CompletedCalendarIcon = ({color = 'black'}: {color?: string}) => (
  <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
    <Path
      d="M18 4.84116V7.14801V15.4982C18 16.8953 16.8462 18 15.4615 18H2.53846C1.12088 18 0 16.8628 0 15.4982V7.14801V4.84116C0 3.44404 1.15385 2.33935 2.53846 2.33935H3.3956V3.31408V4.77617C3.3956 5.78339 4.21978 6.59567 5.24176 6.59567C6.26374 6.59567 7.08791 5.78339 7.08791 4.77617V3.31408V2.33935H11.1099V3.31408V4.77617C11.1099 5.78339 11.9341 6.59567 12.956 6.59567C13.978 6.59567 14.8022 5.78339 14.8022 4.77617V3.31408V2.33935H15.4945C16.8462 2.33935 18 3.44404 18 4.84116ZM17.2418 6.92058H0.758241V15.4982C0.758241 16.4729 1.54945 17.2527 2.53846 17.2527H15.4615C16.4505 17.2527 17.2418 16.4729 17.2418 15.4982V6.92058ZM5.24176 5.88087C5.86813 5.88087 6.36264 5.3935 6.36264 4.77617V3.31408V2.33935V1.10469C6.36264 0.487365 5.86813 0 5.24176 0C4.61538 0 4.12088 0.487365 4.12088 1.10469V2.33935V3.31408V4.77617C4.12088 5.3935 4.61538 5.88087 5.24176 5.88087ZM12.956 5.88087C13.5824 5.88087 14.0769 5.3935 14.0769 4.77617V3.31408V2.33935V1.10469C14.0769 0.487365 13.5824 0 12.956 0C12.3297 0 11.8352 0.487365 11.8352 1.10469V2.33935V3.31408V4.77617C11.8022 5.3935 12.3297 5.88087 12.956 5.88087ZM13.1209 9.25993C12.7912 8.93502 12.2308 8.93502 11.9011 9.25993L7.87912 13.1913L6.0989 11.4368C5.76923 11.1119 5.20879 11.1119 4.87912 11.4368C4.54945 11.7617 4.54945 12.3141 4.87912 12.639L7.25275 14.9783C7.41758 15.1408 7.64835 15.2383 7.84615 15.2383C8.04396 15.2383 8.27472 15.1408 8.43956 14.9783L13.0549 10.4296C13.4505 10.1047 13.4505 9.58484 13.1209 9.25993Z"
      fill={color}
    />
  </Svg>
);

export default CompletedCalendarIcon;
