import {useIsFocused, useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Platform, ScrollView, StatusBar, Text, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {PyxiLabel} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernCard from '~components/ModernCard/ModernCard';
import SocialButtons from '~components/SocialButtons';
import {SCREENS} from '~constants';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';
import {useLanguage} from '~containers/Core/navigation/SettingsStack/zustand';
import {useTheme} from '~contexts/ThemeContext';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import ModernSpinner from '~components/ModernSpinner';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useUserStore} from '~providers/userStore/zustand';
import auth from '@react-native-firebase/auth';

const validationSchema = yup.object().shape({
  email: yup
    .string()
    .email('Enter a valid email')
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter a valid email')
    .required('Email is required'),
  password: yup.string().required('Password is required'),
});

const LoginView = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {width, height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const isAndroid = Platform.OS === 'android';

  const [isLoading, setIsLoading] = useState(false);
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);
  const [isPostLoginLoading, setIsPostLoginLoading] = useState(false);
  const isFocused = useIsFocused();
  const {language} = useLanguage();
  const {setUser} = useUserStore();

  // Get current user ID
  const userId = auth().currentUser?.uid || '';
  const isAuthenticated = !!auth().currentUser?.emailVerified;

  // Data fetching hooks
  const {data: userType, isLoading: userTypeIsLoading} = useGetUserType(userId);
  const {data: userData, isLoading: userAccountIsLoading} = useGetUserAccount(userId);
  const {data: businessData, isLoading: businessIsLoading} = useGetBusinessAccount(userId);

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  // Handle authentication state changes and navigation
  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged(user => {
      if (user && user.emailVerified) {
        console.log('🔄 [DEBUG] User authenticated, starting post-login loading');
        setIsPostLoginLoading(true);
      } else {
        setIsPostLoginLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  // Handle navigation after data is loaded
  useEffect(() => {
    if (!isPostLoginLoading) return;

    // Check if all necessary data has been loaded
    const isStillLoading =
      userTypeIsLoading ||
      !userType ||
      (userType === 'business' && (businessIsLoading || !businessData)) ||
      (userType === 'personal' && (userAccountIsLoading || !userData));

    if (!isStillLoading) {
      console.log('🔄 [DEBUG] All data loaded, making navigation decision:', {
        userType,
        userData,
        businessData,
        hasOnboardingAnswers:
          userType === 'business'
            ? (businessData?.onboarding_answers?.length || 0) > 0
            : (userData?.onboarding_answers?.length || 0) > 0,
        isRegistrationFinished:
          userType === 'business' ? businessData?.is_registration_finished : userData?.is_registration_finished,
      });

      // Set user data in store
      if (userType === 'personal' && userData) {
        setUser(userData);
      }

      // Make navigation decision
      const currentUserData = userType === 'business' ? businessData : userData;
      const hasOnboardingAnswers = (currentUserData?.onboarding_answers?.length || 0) > 0;
      const isRegistrationFinished = currentUserData?.is_registration_finished;

      setTimeout(() => {
        setIsPostLoginLoading(false);

        if (!hasOnboardingAnswers) {
          console.log('📱 [DEBUG] No onboarding answers, navigating to questions');
          navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        } else if (!isRegistrationFinished) {
          console.log('📱 [DEBUG] Registration not finished, navigating to onboarding');
          if (!currentUserData?.photo) {
            navigation.navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
          } else {
            navigation.navigate(SCREENS.ONBOARDING_GROUP);
          }
        } else {
          console.log(
            '📱 [DEBUG] User fully set up, should navigate to home - but navigation is limited from login screen',
          );
          // Since we can't navigate to APP_ROOT from login screen, we'll let the main navigation handle this
          // The main navigation will see that the user is fully set up and navigate appropriately
        }
      }, 300);
    }
  }, [
    isPostLoginLoading,
    userTypeIsLoading,
    userAccountIsLoading,
    businessIsLoading,
    userType,
    userData,
    businessData,
    navigation,
    setUser,
  ]);

  useEffect(() => {
    logScreenView('Login', 'LoginView');
  }, []);

  const paddingBottom = isAndroid ? bottom + spacing.lg : bottom;

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      minHeight: height - (StatusBar.currentHeight || 0),
    },
    heroSection: {
      height: height * 0.28, // Further reduced to ensure register button is visible
      position: 'relative' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      overflow: 'hidden' as const,
    },
    heroGradient: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    heroContent: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 2,
      paddingHorizontal: spacing.lg,
    },
    decorativeCircle1: {
      position: 'absolute' as const,
      top: -50,
      right: -50,
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: colors.textInverse + '15',
    },
    decorativeCircle2: {
      position: 'absolute' as const,
      bottom: -30,
      left: -30,
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.textInverse + '10',
    },
    decorativeCircle3: {
      position: 'absolute' as const,
      top: 120,
      left: -20,
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.textInverse + '20',
    },
    floatingElements: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    floatingDot: {
      position: 'absolute' as const,
      width: 8,
      height: 8,
      borderRadius: 4,
      top: 40,
      right: 60,
    },
    languageButton: {
      position: 'absolute' as const,
      top: spacing.md,
      right: spacing.md,
      backgroundColor: colors.textInverse + '10',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.full,
      borderWidth: 1,
      borderColor: colors.textInverse + '20',
      zIndex: 3,
      minWidth: 44,
      height: 32,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      backdropFilter: 'blur(10px)',
    },
    languageText: {
      color: colors.textInverse,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      letterSpacing: 0.3,
      opacity: 0.9,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: spacing.lg,
      transform: [{scale: 1.1}], // Slightly bigger but not too much
    },
    welcomeText: {
      fontSize: typography.fontSize['3xl'],
      fontWeight: typography.fontWeight.black,
      color: colors.textInverse,
      textAlign: 'center' as const,
      marginTop: spacing.sm,
      letterSpacing: -0.5,
      textShadowColor: colors.black + '20',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
      lineHeight: typography.fontSize['3xl'] * 1.2,
    },
    subtitleText: {
      fontSize: typography.fontSize.lg,
      color: colors.textInverse,
      textAlign: 'center' as const,
      opacity: 0.95,
      marginTop: spacing.xs,
      fontWeight: typography.fontWeight.semibold,
      letterSpacing: 0.2,
      textShadowColor: colors.black + '15',
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 2,
    },
    subSubtitleText: {
      fontSize: typography.fontSize.sm,
      color: colors.textInverse,
      textAlign: 'center' as const,
      opacity: 0.85,
      marginTop: spacing.xs,
      fontWeight: typography.fontWeight.medium,
      letterSpacing: 0.1,
    },
    taglineText: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textInverse,
      textAlign: 'center' as const,
      marginTop: spacing.md,
      opacity: 0.95,
      letterSpacing: 1,
    },
    formSection: {
      paddingHorizontal: 0,
      paddingTop: spacing.lg,
    },
    formCard: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius['2xl'],
      padding: spacing.lg,
      ...shadows.lg,
      marginBottom: spacing.md,
    },
    formTitle: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      marginBottom: spacing.sm,
      letterSpacing: -0.3,
    },
    formSubtitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      textAlign: 'center' as const,
      marginBottom: spacing.lg,
      opacity: 0.85,
      letterSpacing: 0.1,
      lineHeight: typography.fontSize.base * 1.4,
    },
    inputContainer: {
      marginBottom: spacing.sm,
    },
    forgotPasswordContainer: {
      alignItems: 'flex-end' as const,
      marginBottom: spacing.md,
    },
    forgotPasswordText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.primary,
    },
    dividerContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginVertical: spacing.md,
    },
    divider: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginHorizontal: spacing.md,
      fontWeight: typography.fontWeight.medium,
    },
    bottomSection: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.lg,
      paddingBottom: Math.max(paddingBottom, spacing.lg),
      alignItems: 'center' as const,
      marginTop: spacing.md,
    },
    signupContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    signupText: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      fontWeight: typography.fontWeight.normal,
    },
    signupButton: {
      marginLeft: spacing.xs,
    },
    signupButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.primary,
    },
    loadingOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.background + 'E6', // Semi-transparent background
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 1000,
    },
    loadingContainer: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...shadows.lg,
      minWidth: 160,
    },
    loadingText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textPrimary,
      marginTop: spacing.md,
      textAlign: 'center' as const,
    },
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {/* Hero Section */}
        <FadeIn duration={300}>
          <View style={styles.heroSection}>
            {/* Modern Gradient Background */}
            <LinearGradient
              colors={[
                colors.primary, // Your brand orange
                '#FFB347', // Peach
                '#FFD700', // Gold
                '#FF7F50', // Coral
                colors.primaryLight, // Lighter brand orange
              ]}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}
              style={styles.heroGradient}>
              {/* Decorative Elements */}
              <View style={styles.decorativeCircle1} />
              <View style={styles.decorativeCircle2} />
              <View style={styles.decorativeCircle3} />
            </LinearGradient>

            <TouchableOpacity style={styles.languageButton} onPress={() => setIsLanguagesModalVisible(true)}>
              <Text style={styles.languageText}>{language.toUpperCase()}</Text>
            </TouchableOpacity>

            <View style={styles.heroContent}>
              <FadeIn delay={100} duration={400}>
                <View style={styles.logoContainer}>
                  <PyxiLabel />
                </View>
              </FadeIn>

              <FadeIn delay={150} duration={400}>
                <Text style={styles.taglineText}>{t('signin.discover_connect_experience')}</Text>
              </FadeIn>

              {/* Modern Floating Elements */}
              <View style={styles.floatingElements}>
                <View style={[styles.floatingDot, {backgroundColor: colors.textInverse + '30'}]} />
                <View style={[styles.floatingDot, {backgroundColor: colors.textInverse + '20', top: 60, right: 40}]} />
                <View
                  style={[styles.floatingDot, {backgroundColor: colors.textInverse + '25', bottom: 80, left: 30}]}
                />
              </View>
            </View>
          </View>
        </FadeIn>

        {/* Form Section */}
        <View style={styles.formSection}>
          <FadeIn delay={50} duration={300}>
            <ModernCard variant="default" padding="xl" style={{marginBottom: spacing.lg, ...shadows.none}}>
              <Text style={styles.formTitle}>{t('signin.sign_in_title')}</Text>
              <Text style={styles.formSubtitle}>{t('signin.continue_event_journey')}</Text>

              <Formik
                initialValues={{email: '', password: ''}}
                onSubmit={async (values, actions) => {
                  setIsLoading(true);
                  await FirebaseAuth.signInEmailPassword(values.email, values.password, setIsLoading);
                  setIsLoading(false);
                }}
                validationSchema={validationSchema}>
                {formikProps => (
                  <>
                    <View style={styles.inputContainer}>
                      <ModernTextInput
                        label={t('signin.email')}
                        placeholder="Enter your email"
                        value={formikProps.values.email}
                        onChangeText={value => {
                          formikProps.handleChange('email')(value.trim());
                        }}
                        errorText={formikProps.touched.email ? formikProps.errors.email : undefined}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <View style={styles.inputContainer}>
                      <ModernTextInput
                        label={t('signin.password')}
                        placeholder="Enter your password"
                        value={formikProps.values.password}
                        onChangeText={value => {
                          formikProps.handleChange('password')(value);
                        }}
                        errorText={formikProps.touched.password ? formikProps.errors.password : undefined}
                        isPassword
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <View style={styles.forgotPasswordContainer}>
                      <TouchableOpacity
                        onPress={() => {
                          navigation.navigate(SCREENS.FORGOT_PASSWORD);
                        }}>
                        <Text style={styles.forgotPasswordText}>{t('signin.forgot_pwd_header')}</Text>
                      </TouchableOpacity>
                    </View>

                    <ModernButton
                      onPress={() => formikProps.handleSubmit()}
                      title={t('signin.login')}
                      variant="primary"
                      size="lg"
                      fullWidth
                      loading={isLoading}
                      hapticFeedback
                    />

                    <View style={styles.dividerContainer}>
                      <View style={styles.divider} />
                      <Text style={styles.dividerText}>or continue with</Text>
                      <View style={styles.divider} />
                    </View>

                    <SocialButtons />
                  </>
                )}
              </Formik>
            </ModernCard>
          </FadeIn>
        </View>

        {/* Bottom Section */}
        <FadeIn delay={100} duration={300}>
          <View style={styles.bottomSection}>
            <View style={styles.signupContainer}>
              <Text style={styles.signupText}>{t('signin.no_acc_question')}</Text>
              <TouchableOpacity
                style={styles.signupButton}
                onPress={() => {
                  navigation.navigate(SCREENS.SIGN_UP);
                }}>
                <Text style={styles.signupButtonText}>{t('signin.register')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </FadeIn>
      </ScrollView>

      <LanguagesModal isVisible={isLanguagesModalVisible} onClose={() => setIsLanguagesModalVisible(false)} />

      {/* Post-login loading overlay */}
      {isPostLoginLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ModernSpinner size={40} variant="gradient" />
            <Text style={styles.loadingText}>{t('loading.fetching_data')}</Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default LoginView;
