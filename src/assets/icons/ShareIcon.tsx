import {Clip<PERSON><PERSON>, Defs, G, Path, Rect, Svg} from 'react-native-svg';

const ShareIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <G clipPath="url(#clip0_5_8)">
      <Path
        d="M12.7661 17.0389L13.5101 12.2883C11.3246 12.5008 8.89871 13.028 6.85151 14.441C4.56095 16.0214 2.70815 18.7432 2.13551 23.4385C2.09375 23.787 1.76975 24.0366 1.41263 23.9953C1.13759 23.9634 0.922546 23.7673 0.858226 23.5196C0.335986 21.9899 0.0681458 20.5446 0.0119858 19.1879C-0.144974 15.4632 1.27871 12.4224 3.38063 10.1055C5.46191 7.81063 8.21231 6.2288 10.74 5.39895C11.7086 5.07996 12.6499 4.87027 13.5129 4.77222L12.7718 0.745874C12.7085 0.401549 12.9432 0.0712977 13.2955 0.00984461C13.4909 -0.0239311 13.6814 0.0314235 13.8235 0.145886L23.7643 8.1817C24.0408 8.40546 24.0797 8.80702 23.8507 9.07675L23.7768 9.15134L13.8379 17.6136C13.5672 17.8444 13.1553 17.8162 12.9192 17.5516C12.8571 17.4822 12.8112 17.4003 12.7847 17.3118C12.7583 17.2232 12.7519 17.1301 12.7661 17.0389Z"
        fill="#FF9500"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_5_8">
        <Rect width="24" height="24" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);

export default ShareIcon;
