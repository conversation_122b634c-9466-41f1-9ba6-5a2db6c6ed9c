import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Linking, Platform, ScrollView, Text, View, useWindowDimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import ModernButton from '~components/ModernButton';
import {ModernOnboardingHeader, ModernOnboardingCard} from '~components/ModernOnboarding';
import CircularImage from '~components/CircularImage';
import {SCREENS} from '~constants';
import {spacing, borderRadius} from '~constants/design';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useHandleLogOut} from '../hooks/useHandleLogOut';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {FadeInDown} from 'react-native-reanimated';

const AccountTypeView = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const {width} = useWindowDimensions();
  const {bottom} = useSafeAreaInsets();
  const [accountType, setAccountType] = useState('personal');
  const {handleLogOut} = useHandleLogOut();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Account Type', 'AccountType');
  }, []);

  const submit = () => {
    setAccountType(accountType);

    if (accountType === 'personal') {
      setIsLoading(true);
      navigation.navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
    } else {
      Alert.alert(
        'Business Registration',
        'You will be redirected to our website to complete your business account registration.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Continue',
            onPress: () => {
              Linking.openURL('https://www.pyxi.ai/register');
            },
          },
        ],
      );
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('onboarding.choose_account')}
        subtitle={t('onboarding.account_type_subtitle') || 'Select the type of account that best fits your needs'}
        step={1}
        totalSteps={5}
        onBackPress={handleLogOut}
        showSkip={false}
      />

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingHorizontal: spacing.lg,
          paddingBottom: spacing['6xl'],
        }}
        showsVerticalScrollIndicator={false}>
        {/* Personal Account Card */}
        <ModernOnboardingCard
          title={t('onboarding.personal')}
          description={t('onboarding.personal_desc')}
          selected={accountType === 'personal'}
          onPress={() => setAccountType('personal')}
          animationDelay={100}>
          <Animated.View
            entering={FadeInDown.delay(200).duration(400)}
            style={{
              alignItems: 'center',
              marginBottom: spacing.md,
            }}>
            <CircularImage source={require('~assets/images/personalImage.jpg')} size={120} showShadow={true} />
          </Animated.View>
        </ModernOnboardingCard>

        {/* Business Account Card */}
        <ModernOnboardingCard
          title={t('onboarding.business')}
          description={t('onboarding.business_desc')}
          selected={accountType === 'business'}
          onPress={() => {
            setAccountType('business');
            Alert.alert(
              t('onboarding.business_registration') || 'Business Registration',
              t('onboarding.business_redirect_message') ||
                'You will be redirected to our website to complete your business account registration.',
              [
                {
                  text: t('generic.cancel'),
                  style: 'cancel',
                },
                {
                  text: t('generic.continue'),
                  onPress: () => {
                    Linking.openURL('https://www.pyxi.ai/register');
                  },
                },
              ],
            );
          }}
          animationDelay={200}>
          <Animated.View
            entering={FadeInDown.delay(300).duration(400)}
            style={{
              alignItems: 'center',
              marginBottom: spacing.md,
            }}>
            <CircularImage source={require('~assets/images/businessImage.jpg')} size={120} showShadow={true} />
          </Animated.View>
        </ModernOnboardingCard>
      </ScrollView>

      {/* Bottom Action Button */}
      <Animated.View
        entering={FadeInDown.delay(400).duration(400)}
        style={{
          paddingHorizontal: spacing.lg,
          paddingBottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.xl,
          paddingTop: spacing.md,
          backgroundColor: colors.background,
        }}>
        <ModernButton
          title={t('generic.continue')}
          onPress={submit}
          loading={isLoading}
          disabled={!accountType}
          variant="primary"
          size="lg"
          fullWidth
        />
      </Animated.View>
    </View>
  );
};

export default AccountTypeView;
