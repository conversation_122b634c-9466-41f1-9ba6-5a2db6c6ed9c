'use strict';
Object.defineProperty(exports, '__esModule', {value: true});
exports.ACTIVE_CHAT_REQUEST_ACTIONS =
  exports.WEBSOCKET_MESSAGE_ACTION =
  exports.WEBSOCKET_TYPE =
  exports.USER_CHAT_STATUS =
  exports.UPDATE_CHAT_STATUS =
  exports.CONCIERGE_CHAT_TYPE_ENUM =
  exports.CHAT_TYPE_ENUM =
  exports.CHAT_MESSAGE_TYPE_ENUM =
  exports.BUTTON_STATUS_ENUM =
    void 0;
var BUTTON_STATUS_ENUM;
(function (BUTTON_STATUS_ENUM) {
  BUTTON_STATUS_ENUM.LOADING = 'loading';
  BUTTON_STATUS_ENUM.APPROVED = 'approved';
  BUTTON_STATUS_ENUM.REJECTED = 'rejected';
  BUTTON_STATUS_ENUM.SENT = 'sent';
  BUTTON_STATUS_ENUM.EMPTY = 'empty';
})((BUTTON_STATUS_ENUM = exports.BUTTON_STATUS_ENUM || (exports.BUTTON_STATUS_ENUM = {})));
var CHAT_MESSAGE_TYPE_ENUM;
(function (CHAT_MESSAGE_TYPE_ENUM) {
  CHAT_MESSAGE_TYPE_ENUM.INFO = 'info';
  CHAT_MESSAGE_TYPE_ENUM.MESSAGE = 'message';
  CHAT_MESSAGE_TYPE_ENUM.ISSUE_CONFIRMATION = 'issue_confirmation';
  CHAT_MESSAGE_TYPE_ENUM.EVENT = 'event';
})((CHAT_MESSAGE_TYPE_ENUM = exports.CHAT_MESSAGE_TYPE_ENUM || (exports.CHAT_MESSAGE_TYPE_ENUM = {})));
var CHAT_TYPE_ENUM;
(function (CHAT_TYPE_ENUM) {
  CHAT_TYPE_ENUM.PRIVATE = 'private';
  CHAT_TYPE_ENUM.GROUP = 'group';
  CHAT_TYPE_ENUM.ORGANISATION = 'organisation';
})((CHAT_TYPE_ENUM = exports.CHAT_TYPE_ENUM || (exports.CHAT_TYPE_ENUM = {})));
var CONCIERGE_CHAT_TYPE_ENUM;
(function (CONCIERGE_CHAT_TYPE_ENUM) {
  CONCIERGE_CHAT_TYPE_ENUM.ONE_ON_ONE = 'one-on-one';
  CONCIERGE_CHAT_TYPE_ENUM.BROADCAST = 'broadcast';
})((CONCIERGE_CHAT_TYPE_ENUM = exports.CONCIERGE_CHAT_TYPE_ENUM || (exports.CONCIERGE_CHAT_TYPE_ENUM = {})));
var UPDATE_CHAT_STATUS;
(function (UPDATE_CHAT_STATUS) {
  UPDATE_CHAT_STATUS.ACCEPTED = 'accepted';
  UPDATE_CHAT_STATUS.PENDING = 'pending';
  UPDATE_CHAT_STATUS.DECLINED = 'declined';
})((UPDATE_CHAT_STATUS = exports.UPDATE_CHAT_STATUS || (exports.UPDATE_CHAT_STATUS = {})));
var USER_CHAT_STATUS;
(function (USER_CHAT_STATUS) {
  USER_CHAT_STATUS.ACCEPTED = 'accepted';
  USER_CHAT_STATUS.PENDING = 'pending';
  USER_CHAT_STATUS.REJECTED = 'rejected';
})((USER_CHAT_STATUS = exports.USER_CHAT_STATUS || (exports.USER_CHAT_STATUS = {})));
var WEBSOCKET_TYPE;
(function (WEBSOCKET_TYPE) {
  WEBSOCKET_TYPE.CHAT = 'chat';
  WEBSOCKET_TYPE.MESSAGE = 'message';
  WEBSOCKET_TYPE.ERROR = 'error';
})((WEBSOCKET_TYPE = exports.WEBSOCKET_TYPE || (exports.WEBSOCKET_TYPE = {})));
var WEBSOCKET_MESSAGE_ACTION;
(function (WEBSOCKET_MESSAGE_ACTION) {
  WEBSOCKET_MESSAGE_ACTION.CREATE = 'create';
  WEBSOCKET_MESSAGE_ACTION.DELETE = 'delete';
})((WEBSOCKET_MESSAGE_ACTION = exports.WEBSOCKET_MESSAGE_ACTION || (exports.WEBSOCKET_MESSAGE_ACTION = {})));
var ACTIVE_CHAT_REQUEST_ACTIONS;
(function (ACTIVE_CHAT_REQUEST_ACTIONS) {
  ACTIVE_CHAT_REQUEST_ACTIONS.JOIN = 'join';
  ACTIVE_CHAT_REQUEST_ACTIONS.LEAVE = 'leave';
})((ACTIVE_CHAT_REQUEST_ACTIONS = exports.ACTIVE_CHAT_REQUEST_ACTIONS || (exports.ACTIVE_CHAT_REQUEST_ACTIONS = {})));
//# sourceMappingURL=index.js.map
