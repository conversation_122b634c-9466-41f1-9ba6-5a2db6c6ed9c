import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Business} from '~types/api/business';

export function useGetBusinessAccountById(user_id: any) {
  return useQuery<Business | undefined, Error>(['businessAccount', user_id], async () => {
    if (!user_id) {
      return undefined;
    }
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}businesses/${user_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`Business account not found for user ${user_id}`);
        return undefined;
      }
      console.error(`Failed to get business account: ${response.status}`);
      throw new Error(`Failed to get business account: ${response.status}`);
    }

    const data = await response.json();

    return data as Business;
  });
}
