import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    flex: {
      flex: 1,
    },
    modal: {
      flex: 1,
      justifyContent: 'flex-end',
      margin: 0,
    },
    modalContent: {
      backgroundColor: colors.white,
      borderTopLeftRadius: 14,
      borderTopRightRadius: 14,
    },
    slider: {
      alignSelf: 'center',
      width: 36,
      height: 5,
      backgroundColor: colors.gray400,
      borderRadius: 4,
      marginBottom: 8,
    },
    safeAreaContainer: {
      flex: 1,
      justifyContent: 'flex-end',
    },
  });

export default getStyles;
