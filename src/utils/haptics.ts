import {Platform} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

// Haptic feedback options
const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

export enum HapticFeedbackType {
  // Light feedback for subtle interactions
  LIGHT = 'impactLight',

  // Medium feedback for standard interactions
  MEDIUM = 'impactMedium',

  // Heavy feedback for important interactions
  HEAVY = 'impactHeavy',

  // Success feedback for positive actions
  SUCCESS = 'notificationSuccess',

  // Warning feedback for cautionary actions
  WARNING = 'notificationWarning',

  // Error feedback for negative actions
  ERROR = 'notificationError',

  // Selection feedback for picker/selector changes
  SELECTION = 'selection',

  // Rigid feedback for firm interactions
  RIGID = 'rigid',

  // Soft feedback for gentle interactions
  SOFT = 'soft',
}

class HapticManager {
  private isEnabled: boolean = true;

  /**
   * Enable or disable haptic feedback globally
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Check if haptic feedback is enabled
   */
  isHapticEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Trigger haptic feedback
   */
  trigger(type: HapticFeedbackType): void {
    if (!this.isEnabled) {
      return;
    }

    try {
      ReactNativeHapticFeedback.trigger(type, hapticOptions);
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Light haptic feedback for subtle interactions
   * Use for: hover states, minor UI feedback
   */
  light(): void {
    this.trigger(HapticFeedbackType.LIGHT);
  }

  /**
   * Medium haptic feedback for standard interactions
   * Use for: button presses, tab switches, toggle switches
   */
  medium(): void {
    this.trigger(HapticFeedbackType.MEDIUM);
  }

  /**
   * Heavy haptic feedback for important interactions
   * Use for: important confirmations, major state changes
   */
  heavy(): void {
    this.trigger(HapticFeedbackType.HEAVY);
  }

  /**
   * Success haptic feedback for positive actions
   * Use for: successful form submissions, completed actions
   */
  success(): void {
    this.trigger(HapticFeedbackType.SUCCESS);
  }

  /**
   * Warning haptic feedback for cautionary actions
   * Use for: validation warnings, cautionary alerts
   */
  warning(): void {
    this.trigger(HapticFeedbackType.WARNING);
  }

  /**
   * Error haptic feedback for negative actions
   * Use for: form errors, failed actions, destructive actions
   */
  error(): void {
    this.trigger(HapticFeedbackType.ERROR);
  }

  /**
   * Selection haptic feedback for picker/selector changes
   * Use for: scrolling through options, picker selections
   */
  selection(): void {
    this.trigger(HapticFeedbackType.SELECTION);
  }

  /**
   * Rigid haptic feedback for firm interactions
   * Use for: reaching limits, boundaries, or constraints
   */
  rigid(): void {
    if (Platform.OS === 'ios') {
      this.trigger(HapticFeedbackType.RIGID);
    } else {
      // Fallback to heavy for Android
      this.heavy();
    }
  }

  /**
   * Soft haptic feedback for gentle interactions
   * Use for: gentle confirmations, soft notifications
   */
  soft(): void {
    if (Platform.OS === 'ios') {
      this.trigger(HapticFeedbackType.SOFT);
    } else {
      // Fallback to light for Android
      this.light();
    }
  }

  /**
   * Context-aware haptic feedback for common UI interactions
   */
  buttonPress(): void {
    this.medium();
  }

  tabSwitch(): void {
    this.light();
  }

  toggleSwitch(): void {
    this.medium();
  }

  pullToRefresh(): void {
    this.light();
  }

  longPress(): void {
    this.heavy();
  }

  swipeAction(): void {
    this.medium();
  }

  modalPresent(): void {
    this.light();
  }

  modalDismiss(): void {
    this.light();
  }

  likeAction(): void {
    this.medium();
  }

  joinEvent(): void {
    this.success();
  }

  leaveEvent(): void {
    this.warning();
  }

  deleteAction(): void {
    this.error();
  }

  shareAction(): void {
    this.light();
  }

  searchResult(): void {
    this.light();
  }

  mapPinSelect(): void {
    this.medium();
  }

  filterApply(): void {
    this.light();
  }

  formValidationError(): void {
    this.error();
  }

  formSubmitSuccess(): void {
    this.success();
  }

  navigationBack(): void {
    this.light();
  }

  photoCapture(): void {
    this.heavy();
  }

  messageReceived(): void {
    this.light();
  }

  messageSent(): void {
    this.medium();
  }

  notificationReceived(): void {
    this.medium();
  }

  eventReminder(): void {
    this.warning();
  }

  locationFound(): void {
    this.success();
  }

  connectionLost(): void {
    this.error();
  }

  connectionRestored(): void {
    this.success();
  }
}

// Export singleton instance
export const haptics = new HapticManager();

// Export individual functions for convenience
export const {
  light,
  medium,
  heavy,
  success,
  warning,
  error,
  selection,
  rigid,
  soft,
  buttonPress,
  tabSwitch,
  toggleSwitch,
  pullToRefresh,
  longPress,
  swipeAction,
  modalPresent,
  modalDismiss,
  likeAction,
  joinEvent,
  leaveEvent,
  deleteAction,
  shareAction,
  searchResult,
  mapPinSelect,
  filterApply,
  formValidationError,
  formSubmitSuccess,
  navigationBack,
  photoCapture,
  messageReceived,
  messageSent,
  notificationReceived,
  eventReminder,
  locationFound,
  connectionLost,
  connectionRestored,
} = haptics;

export default haptics;
