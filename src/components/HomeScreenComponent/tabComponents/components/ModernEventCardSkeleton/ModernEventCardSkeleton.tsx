import React from 'react';
import {View, StyleSheet} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, responsive, shadows} from '~constants';
import ModernSkeleton from '~components/ModernSkeleton/ModernSkeleton';

interface ModernEventCardSkeletonProps {
  index?: number;
}

const ModernEventCardSkeleton: React.FC<ModernEventCardSkeletonProps> = ({index = 0}) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        {/* Image Section Skeleton */}
        <View style={styles.imageContainer}>
          <ModernSkeleton width="100%" height={220} borderRadius="lg" style={styles.imageSkeleton} />

          {/* Status Badge Skeleton */}
          <View style={styles.statusBadgeContainer}>
            <ModernSkeleton width={100} height={20} borderRadius="sm" />
          </View>

          {/* Title Overlay Skeleton */}
          <View style={styles.titleOverlayContainer}>
            <ModernSkeleton width="80%" height={32} borderRadius="sm" style={styles.titleOverlaySkeleton} />
            <ModernSkeleton width="60%" height={32} borderRadius="sm" style={styles.titleOverlaySkeleton} />
          </View>

          {/* Action Button Skeleton */}
          <View style={styles.actionButtonContainer}>
            <ModernSkeleton width={40} height={40} borderRadius="full" />
          </View>
        </View>

        {/* Content Section Skeleton */}
        <View style={styles.contentContainer}>
          {/* Title Skeleton */}
          <ModernSkeleton width="75%" height={20} borderRadius="sm" style={styles.titleSkeleton} />

          {/* Date and Location Skeleton */}
          <ModernSkeleton width="90%" height={16} borderRadius="sm" style={styles.dateSkeleton} />

          {/* Price Skeleton */}
          <ModernSkeleton width="40%" height={16} borderRadius="sm" style={styles.priceSkeleton} />
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginBottom: spacing.sm,
    },
    card: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.xl,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border + '30',
    },
    imageContainer: {
      height: responsive.getValue({xs: 200, sm: 220, md: 240}, 220),
      position: 'relative',
    },
    imageSkeleton: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    statusBadgeContainer: {
      position: 'absolute',
      top: spacing.md,
      right: spacing.md,
    },
    titleOverlayContainer: {
      position: 'absolute',
      bottom: spacing.xl,
      left: spacing.md,
      right: spacing.md,
      gap: spacing.xs,
    },
    titleOverlaySkeleton: {
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
    actionButtonContainer: {
      position: 'absolute',
      bottom: spacing.md,
      right: spacing.md,
    },
    contentContainer: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      gap: spacing.xs,
    },
    titleSkeleton: {
      marginBottom: spacing.xs,
    },
    dateSkeleton: {
      marginBottom: spacing.xs,
    },
    priceSkeleton: {
      marginTop: spacing.xs,
    },
  });

export default ModernEventCardSkeleton;
