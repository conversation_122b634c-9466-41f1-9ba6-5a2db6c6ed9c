import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      backgroundColor: colors.white,
    },

    subcategoriesListsContainer: {
      flex: 1,
      width: '100%',
    },
    buttonContainer: {
      width: '100%',
      paddingHorizontal: 25,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingTop: 16,
      justifyContent: 'flex-end',
      backgroundColor: colors.white,
    },
    buttonContainerStyles: {
      width: '100%',
      height: 40,
      borderRadius: 6,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 24,
    },
    buttonTextStyles: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.white,
    },
  });

export default createStyles;
