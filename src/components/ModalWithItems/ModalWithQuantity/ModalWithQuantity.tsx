import {Platform, View} from 'react-native';
import ModalWithButtons from '../../ModalWithButtons';
import {useEffect, useState} from 'react';
import ModalItem from './ModalItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  close: () => void;
  onPress: (value: number) => () => void;
  chosenQuantity: number;
}

const ModalWithQuantity = ({isVisible, close, onPress, chosenQuantity}: IProps) => {
  const {colors} = useTheme();
  const {bottom} = useSafeAreaInsets();
  const [items, setItems] = useState([
    {quantity: 0, isActive: false},
    {quantity: 1, isActive: false},
    {quantity: 2, isActive: false},
    {quantity: 3, isActive: false},
    {quantity: 4, isActive: false},
    {quantity: 5, isActive: false},
    {quantity: 6, isActive: false},
    {quantity: 7, isActive: false},
    {quantity: 8, isActive: false},
  ]);

  const isAndroid = Platform.OS === 'android';

  const handleItemClick = (index: number) => {
    const updatedItems = items.map((item, i) => ({
      ...item,
      isActive: i === index,
    }));
    setItems(updatedItems);
  };

  useEffect(() => {
    if (chosenQuantity) {
      setItems(prevItems => [
        ...prevItems.map(prevItem => (prevItem.quantity === chosenQuantity ? {...prevItem, isActive: true} : prevItem)),
      ]);
    }
  }, [chosenQuantity]);
  return (
    <ModalWithButtons
      onCloseModal={close}
      modalIsVisible={isVisible}
      onPress={
        items.find(item => item.isActive)?.quantity.toString()
          ? onPress(items.find(item => item.isActive)!.quantity)
          : () => {}
      }
      backgroundColor={colors.white}>
      <View
        style={{
          paddingHorizontal: 46,
          paddingTop: 10,
          overflow: 'hidden',
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
          justifyContent: 'space-between',
          flexDirection: 'row',
          flexWrap: 'wrap',
        }}>
        {items.map((item, index) => (
          <ModalItem key={index} item={item} onPress={() => handleItemClick(index)} />
        ))}
      </View>
    </ModalWithButtons>
  );
};

export default ModalWithQuantity;
