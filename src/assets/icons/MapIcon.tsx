import React from 'react';
import Svg, {Path, Circle} from 'react-native-svg';

interface MapIconProps {
  color?: string;
  size?: number;
}

const MapIcon: React.FC<MapIconProps> = ({color = '#202020', size = 18}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    {/* Map base */}
    <Path
      d="M3 20L9 17L15 20L21 17V4L15 7L9 4L3 7V20Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />

    {/* Map fold lines */}
    <Path d="M9 4V17" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M15 7V20" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />

    {/* Location pin */}
    <Circle cx="12" cy="10" r="2" stroke={color} strokeWidth="2" fill={color} />
  </Svg>
);

export default MapIcon;
