import {duration} from 'moment-timezone';
import {useEffect, useRef} from 'react';
import {
  Easing,
  Extrapolate,
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  cancelAnimation,
} from 'react-native-reanimated';

const useHomeScreenAnimation = () => {
  const lastContentOffset = useSharedValue(0);
  const isScrolling = useSharedValue(false);
  const translateY = useSharedValue(0);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      try {
        cancelAnimation(lastContentOffset);
        cancelAnimation(isScrolling);
        cancelAnimation(translateY);
      } catch (error) {
        if (__DEV__) {
          console.warn('HomeScreenAnimation cleanup warning:', error);
        }
      }
    };
  }, []);

  const scrollAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: withTiming(translateY.value, {
            duration: 300,
            easing: Easing.inOut(Easing.ease),
          }),
        },
      ],
    };
  });

  const addEventButtonStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(interpolate(translateY.value, [0, -200], [110, 0], Extrapolate.CLAMP), {
        duration: 300,
        easing: Easing.inOut(Easing.ease),
      }),
    };
  });

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      if (lastContentOffset.value > event.contentOffset.y && isScrolling.value) {
        translateY.value = 0;
      } else if (lastContentOffset.value < event.contentOffset.y && event.contentOffset.y > 150) {
        translateY.value = -200;
      }
      lastContentOffset.value = event.contentOffset.y;
    },
    onBeginDrag: e => {
      isScrolling.value = true;
    },
    onEndDrag: e => {
      isScrolling.value = false;
    },
  });
  return {scrollHandler, scrollAnimatedStyle, addEventButtonStyle};
};

export default useHomeScreenAnimation;
