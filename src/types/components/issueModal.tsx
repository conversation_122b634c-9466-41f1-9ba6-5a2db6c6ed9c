import React from 'react';
import {Modal, View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {ModernCloseIcon} from '~assets/icons';

interface IssueModalProps {
  visible: boolean;
  onClose: () => void;
  onTechnicalIssueClick: () => void;
  onEventIssueClick: () => void;
}

const IssueModal: React.FC<IssueModalProps> = ({visible, onClose, onTechnicalIssueClick, onEventIssueClick}) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Modern Close Icon in Top-Right Corner */}
          <TouchableOpacity onPress={onClose} style={styles.closeIcon}>
            <ModernCloseIcon size={20} variant="minimal" />
          </TouchableOpacity>

          <Text style={styles.title}>Need Assistance?</Text>
          <Text style={styles.message}>Is your question related to a technical issue or an event-related concern?</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={onTechnicalIssueClick}>
              <Text style={styles.buttonText}>Technical Issue</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={onEventIssueClick}>
              <Text style={styles.buttonText}>Event Issue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    position: 'relative',
  },
  closeIcon: {
    position: 'absolute',
    top: 0,
    right: 10,
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#555',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 18,
    backgroundColor: '#5f50ad',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default IssueModal;
