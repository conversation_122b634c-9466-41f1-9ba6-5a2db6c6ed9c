import {AccessibilityInfo, Platform} from 'react-native';

export interface AccessibilityProps {
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?:
    | 'none'
    | 'button'
    | 'link'
    | 'search'
    | 'image'
    | 'keyboardkey'
    | 'text'
    | 'adjustable'
    | 'imagebutton'
    | 'header'
    | 'summary'
    | 'alert'
    | 'checkbox'
    | 'combobox'
    | 'menu'
    | 'menubar'
    | 'menuitem'
    | 'progressbar'
    | 'radio'
    | 'radiogroup'
    | 'scrollbar'
    | 'spinbutton'
    | 'switch'
    | 'tab'
    | 'tablist'
    | 'timer'
    | 'toolbar';
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean | 'mixed';
    busy?: boolean;
    expanded?: boolean;
  };
  accessibilityValue?: {
    min?: number;
    max?: number;
    now?: number;
    text?: string;
  };
  accessibilityActions?: Array<{
    name: string;
    label?: string;
  }>;
  onAccessibilityAction?: (event: {nativeEvent: {actionName: string}}) => void;
}

class AccessibilityManager {
  private isScreenReaderEnabled: boolean = false;
  private isReduceMotionEnabled: boolean = false;
  private isReduceTransparencyEnabled: boolean = false;

  constructor() {
    this.init();
  }

  private async init() {
    try {
      this.isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
      this.isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();

      if (Platform.OS === 'ios') {
        this.isReduceTransparencyEnabled = await AccessibilityInfo.isReduceTransparencyEnabled();
      }

      // Listen for changes
      AccessibilityInfo.addEventListener('screenReaderChanged', this.handleScreenReaderChange);
      AccessibilityInfo.addEventListener('reduceMotionChanged', this.handleReduceMotionChange);

      if (Platform.OS === 'ios') {
        AccessibilityInfo.addEventListener('reduceTransparencyChanged', this.handleReduceTransparencyChange);
      }
    } catch (error) {
      console.warn('Failed to initialize accessibility settings:', error);
    }
  }

  private handleScreenReaderChange = (isEnabled: boolean) => {
    this.isScreenReaderEnabled = isEnabled;
  };

  private handleReduceMotionChange = (isEnabled: boolean) => {
    this.isReduceMotionEnabled = isEnabled;
  };

  private handleReduceTransparencyChange = (isEnabled: boolean) => {
    this.isReduceTransparencyEnabled = isEnabled;
  };

  /**
   * Check if screen reader is enabled
   */
  isScreenReaderActive(): boolean {
    return this.isScreenReaderEnabled;
  }

  /**
   * Check if reduce motion is enabled
   */
  shouldReduceMotion(): boolean {
    return this.isReduceMotionEnabled;
  }

  /**
   * Check if reduce transparency is enabled (iOS only)
   */
  shouldReduceTransparency(): boolean {
    return this.isReduceTransparencyEnabled;
  }

  /**
   * Announce message to screen reader
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    if (this.isScreenReaderEnabled) {
      AccessibilityInfo.announceForAccessibility(message);
    }
  }

  /**
   * Set accessibility focus to a specific element
   */
  setFocus(reactTag: number) {
    if (this.isScreenReaderEnabled) {
      AccessibilityInfo.setAccessibilityFocus(reactTag);
    }
  }

  /**
   * Generate accessibility props for buttons
   */
  buttonProps(label: string, hint?: string, disabled?: boolean): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'button',
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessibilityState: {disabled: disabled || false},
    };
  }

  /**
   * Generate accessibility props for images
   */
  imageProps(label: string, decorative: boolean = false): AccessibilityProps {
    if (decorative) {
      return {
        accessible: false,
        accessibilityElementsHidden: true,
        importantForAccessibility: 'no-hide-descendants',
      };
    }

    return {
      accessible: true,
      accessibilityRole: 'image',
      accessibilityLabel: label,
    };
  }

  /**
   * Generate accessibility props for text inputs
   */
  textInputProps(label: string, hint?: string, required?: boolean, error?: string): AccessibilityProps {
    const accessibilityLabel = required ? `${label}, required` : label;
    const accessibilityHint = error ? `${hint || ''} ${error}` : hint;

    return {
      accessible: true,
      accessibilityLabel,
      accessibilityHint,
      accessibilityState: {
        disabled: false,
        // Add error state if needed
      },
    };
  }

  /**
   * Generate accessibility props for switches/toggles
   */
  switchProps(label: string, value: boolean, hint?: string): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'switch',
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessibilityState: {checked: value},
    };
  }

  /**
   * Generate accessibility props for tabs
   */
  tabProps(label: string, selected: boolean, position: number, total: number): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'tab',
      accessibilityLabel: `${label}, tab ${position} of ${total}`,
      accessibilityState: {selected},
    };
  }

  /**
   * Generate accessibility props for headers
   */
  headerProps(text: string, level: number = 1): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'header',
      accessibilityLabel: text,
      accessibilityValue: {text: `heading level ${level}`},
    };
  }

  /**
   * Generate accessibility props for progress indicators
   */
  progressProps(label: string, current: number, max: number, text?: string): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'progressbar',
      accessibilityLabel: label,
      accessibilityValue: {
        min: 0,
        max,
        now: current,
        text: text || `${Math.round((current / max) * 100)}% complete`,
      },
    };
  }

  /**
   * Generate accessibility props for alerts
   */
  alertProps(message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info'): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'alert',
      accessibilityLabel: `${type} alert: ${message}`,
      accessibilityLiveRegion: 'assertive',
    };
  }

  /**
   * Generate accessibility props for lists
   */
  listProps(itemCount: number): AccessibilityProps {
    return {
      accessible: true,
      accessibilityLabel: `List with ${itemCount} items`,
    };
  }

  /**
   * Generate accessibility props for list items
   */
  listItemProps(label: string, position: number, total: number, selected?: boolean): AccessibilityProps {
    return {
      accessible: true,
      accessibilityLabel: `${label}, ${position} of ${total}`,
      accessibilityState: {selected: selected || false},
    };
  }

  /**
   * Generate accessibility props for cards
   */
  cardProps(title: string, description?: string, actionHint?: string): AccessibilityProps {
    const label = description ? `${title}, ${description}` : title;

    return {
      accessible: true,
      accessibilityRole: 'button',
      accessibilityLabel: label,
      accessibilityHint: actionHint || 'Double tap to open',
    };
  }

  /**
   * Generate accessibility props for event cards
   */
  eventCardProps(eventName: string, date: string, location: string, attendees?: number): AccessibilityProps {
    let label = `Event: ${eventName}, ${date}, at ${location}`;
    if (attendees !== undefined) {
      label += `, ${attendees} attendees`;
    }

    return {
      accessible: true,
      accessibilityRole: 'button',
      accessibilityLabel: label,
      accessibilityHint: 'Double tap to view event details',
    };
  }

  /**
   * Generate accessibility props for map pins
   */
  mapPinProps(eventName: string, location: string): AccessibilityProps {
    return {
      accessible: true,
      accessibilityRole: 'button',
      accessibilityLabel: `Event pin: ${eventName} at ${location}`,
      accessibilityHint: 'Double tap to select event',
    };
  }

  /**
   * Cleanup listeners
   */
  cleanup() {
    AccessibilityInfo.removeEventListener('screenReaderChanged', this.handleScreenReaderChange);
    AccessibilityInfo.removeEventListener('reduceMotionChanged', this.handleReduceMotionChange);

    if (Platform.OS === 'ios') {
      AccessibilityInfo.removeEventListener('reduceTransparencyChanged', this.handleReduceTransparencyChange);
    }
  }
}

// Export singleton instance
export const accessibility = new AccessibilityManager();

// Export utility functions
export const {
  isScreenReaderActive,
  shouldReduceMotion,
  shouldReduceTransparency,
  announce,
  setFocus,
  buttonProps,
  imageProps,
  textInputProps,
  switchProps,
  tabProps,
  headerProps,
  progressProps,
  alertProps,
  listProps,
  listItemProps,
  cardProps,
  eventCardProps,
  mapPinProps,
} = accessibility;

export default accessibility;
