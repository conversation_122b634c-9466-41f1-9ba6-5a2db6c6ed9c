import React from 'react';
import {View, Text, TouchableOpacity, ViewStyle} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {haptics} from '~utils/haptics';
import Animated, {FadeInDown, ScaleIn} from 'react-native-reanimated';
import {useTranslation} from 'react-i18next';

interface ModernPaymentCardProps {
  title: string;
  price: number;
  currency?: string;
  description?: string;
  quantity?: number;
  onQuantityChange?: (quantity: number) => void;
  maxQuantity?: number;
  selected?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
  animationDelay?: number;
}

const ModernPaymentCard: React.FC<ModernPaymentCardProps> = ({
  title,
  price,
  currency = '$',
  description,
  quantity = 0,
  onQuantityChange,
  maxQuantity = 10,
  selected = false,
  onPress,
  style,
  animationDelay = 0,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const handlePress = () => {
    if (onPress) {
      haptics.medium();
      onPress();
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (onQuantityChange && newQuantity >= 0 && newQuantity <= maxQuantity) {
      haptics.light();
      onQuantityChange(newQuantity);
    }
  };

  const cardStyle: ViewStyle = {
    backgroundColor: selected ? colors.primary + '10' : colors.cardBackground,
    borderRadius: borderRadius['2xl'],
    padding: spacing.xl,
    marginVertical: spacing.md,
    borderWidth: selected ? 2 : 1,
    borderColor: selected ? colors.primary : colors.border + '40',
    ...style,
  };

  const formatPrice = (amount: number) => {
    if (amount === 0) return t('payment.free') || 'Free';
    return `${currency}${amount.toFixed(2)}`;
  };

  return (
    <Animated.View entering={FadeInDown.delay(animationDelay).duration(400)}>
      <TouchableOpacity style={cardStyle} onPress={handlePress} activeOpacity={0.8} disabled={!onPress}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start'}}>
          {/* Left Content */}
          <View style={{flex: 1, marginRight: spacing.md}}>
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.xs,
              }}>
              {title}
            </Text>

            {description && (
              <Text
                style={{
                  fontSize: typography.fontSize.sm,
                  fontWeight: typography.fontWeight.normal,
                  color: colors.textSecondary,
                  lineHeight: typography.fontSize.sm * typography.lineHeight.relaxed,
                  marginBottom: spacing.sm,
                }}>
                {description}
              </Text>
            )}

            <Text
              style={{
                fontSize: typography.fontSize.xl,
                fontWeight: typography.fontWeight.bold,
                color: price === 0 ? colors.success : colors.primary,
              }}>
              {formatPrice(price)}
            </Text>
          </View>

          {/* Right Content - Quantity Controls */}
          {onQuantityChange && (
            <View style={{alignItems: 'center'}}>
              <Text
                style={{
                  fontSize: typography.fontSize.sm,
                  fontWeight: typography.fontWeight.medium,
                  color: colors.textSecondary,
                  marginBottom: spacing.xs,
                }}>
                {t('payment.quantity') || 'Quantity'}
              </Text>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: colors.surface,
                  borderRadius: borderRadius.lg,
                  padding: spacing.xs,
                }}>
                <TouchableOpacity
                  onPress={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 0}
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: borderRadius.md,
                    backgroundColor: quantity <= 0 ? colors.border : colors.primary,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: typography.fontSize.lg,
                      fontWeight: typography.fontWeight.bold,
                      color: quantity <= 0 ? colors.textSecondary : colors.white,
                    }}>
                    -
                  </Text>
                </TouchableOpacity>

                <Text
                  style={{
                    fontSize: typography.fontSize.lg,
                    fontWeight: typography.fontWeight.bold,
                    color: colors.textPrimary,
                    marginHorizontal: spacing.md,
                    minWidth: 24,
                    textAlign: 'center',
                  }}>
                  {quantity}
                </Text>

                <TouchableOpacity
                  onPress={() => handleQuantityChange(quantity + 1)}
                  disabled={quantity >= maxQuantity}
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: borderRadius.md,
                    backgroundColor: quantity >= maxQuantity ? colors.border : colors.primary,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: typography.fontSize.lg,
                      fontWeight: typography.fontWeight.bold,
                      color: quantity >= maxQuantity ? colors.textSecondary : colors.white,
                    }}>
                    +
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ModernPaymentCard;
