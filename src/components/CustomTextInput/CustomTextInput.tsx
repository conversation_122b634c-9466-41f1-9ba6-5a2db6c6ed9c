import {useState, useRef} from 'react';
import {TextInput, Animated, TextStyle, TouchableWithoutFeedback, View, Text, TouchableOpacity} from 'react-native';
import getStyles from './styles';
import {EyeIcon, EyeOffIcon} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  placeholder?: string;
  description?: string;
  onSubmitEditing?: () => void;
  buttonWidth?: '100%' | '70%';
  value: string;
  onChangeValue: (value: string) => void;
  isPassword?: boolean;
  errorText?: any;
  isMultiline?: boolean;
  editable?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
}

const CustomTextInput = ({
  isPassword = false,
  placeholder = '',
  description = '',
  onSubmitEditing,
  buttonWidth = '100%',
  value,
  onChangeValue,
  errorText = '',
  isMultiline = false,
  editable = true,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
}: IProps) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const [isFocused, setIsFocused] = useState(false);
  const [textIsVisible, setTextIsVisible] = useState(!isPassword);
  const animatedIsFocused = useRef(new Animated.Value(0)).current;

  const handleFocus = () => {
    setIsFocused(true);
    Animated.timing(animatedIsFocused, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    Animated.timing(animatedIsFocused, {
      toValue: value === '' ? 0 : 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handlePress = () => {
    inputRef.current?.focus();
  };

  const inputRef = useRef<TextInput>(null);

  const viewStyle: Animated.WithAnimatedObject<TextStyle> = {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: animatedIsFocused.interpolate({
      inputRange: [0, 1],
      outputRange: [errorText ? colors.error : colors.border, errorText ? colors.error : colors.border],
    }),
  };

  return (
    <View style={{width: buttonWidth}}>
      <Text style={{marginTop: 8, fontSize: 12, color: colors.statusGray, fontWeight: '500'}}>{description}</Text>
      <TouchableWithoutFeedback onPress={handlePress} style={{width: '100%'}}>
        <Animated.View style={[styles.container, viewStyle, !editable && {backgroundColor: colors.gray400}]}>
          <TextInput
            ref={inputRef}
            style={styles.inputStyle}
            placeholderTextColor={colors.statusGray}
            placeholder={placeholder}
            value={value}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChangeText={onChangeValue}
            returnKeyType={onSubmitEditing ? 'done' : 'default'}
            secureTextEntry={!textIsVisible}
            onSubmitEditing={onSubmitEditing}
            multiline={isMultiline}
            editable={editable}
            keyboardType={keyboardType}
            autoCapitalize={autoCapitalize}
            autoCorrect={autoCorrect}
          />

          {isPassword && (value || isFocused) && (
            <TouchableOpacity style={{justifyContent: 'center'}} onPress={() => setTextIsVisible(prev => !prev)}>
              {textIsVisible ? <EyeIcon /> : <EyeOffIcon />}
            </TouchableOpacity>
          )}
        </Animated.View>
      </TouchableWithoutFeedback>
      <View style={{paddingTop: 4}}>
        <Text style={{fontSize: 12, fontWeight: '400', color: colors.error, width: '100%'}}>{errorText}</Text>
      </View>
    </View>
  );
};

export default CustomTextInput;
