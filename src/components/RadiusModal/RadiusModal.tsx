import {FC, useEffect, useState} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import createStyles from './styles';
import XIcon from '~assets/icons/XIcon';
import {useTranslation} from 'react-i18next';
import IIcon from '~assets/icons/IIcon';
import Slider from '@react-native-community/slider';
import {useHomeStore} from '~providers/home/<USER>';
import Modal from 'react-native-modal/dist/modal';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  modalIsVisible: boolean;
  onClose: () => void;
}

const RadiusModal: FC<IProps> = ({modalIsVisible, onClose}) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {t} = useTranslation();
  const {radius, setRadius} = useHomeStore();
  const [tempRadius, setTempRadius] = useState(radius);

  useEffect(() => {
    setTempRadius(radius);
  }, [radius]);

  return (
    <Modal
      isVisible={modalIsVisible}
      onBackdropPress={onClose}
      style={styles.flex}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropTransitionOutTiming={0}>
      <View style={styles.container}>
        <TouchableOpacity style={styles.iconContainer} onPress={onClose}>
          <XIcon />
        </TouchableOpacity>
        <View style={styles.distanceAdjusterContainer}>
          <Text style={styles.distanceAdjuster}>{t('home.distance_adjuster')}</Text>
          <IIcon />
        </View>
        <View style={styles.maxMinDistancesContainer}>
          <Text style={styles.maxMinDistances}>{tempRadius} km</Text>
        </View>
        <Slider
          minimumValue={1}
          maximumValue={1000}
          minimumTrackTintColor={colors.statusGreen}
          maximumTrackTintColor={colors.gray100}
          step={1}
          onValueChange={setTempRadius}
          value={radius}
        />
        <TouchableOpacity
          style={styles.buttonContainer}
          onPress={() => {
            setRadius(tempRadius);
            onClose();
          }}>
          <Text style={styles.buttonText}>{t('generic.confirm')}</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

export default RadiusModal;
