import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Platform, StyleSheet, Text, TextInput, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import {logScreenView} from '~Utils/firebaseAnalytics';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import ProgressDots from '~components/ProgressDots';
import SubcategoriesLists from '~components/SubcategoriesLists';
import {SCREENS} from '~constants';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useReviveEvent} from '~hooks/event/useReviveEvent';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import FirebaseChatsService from '~services/FirebaseChats';
import {SubCategoryType} from '~types/categories';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';

const isIOS = Platform.OS === 'ios';

const CreateEventTemplate = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.CREATE_EVENT_TEMPLATE>>();
  const {setIsTabBarDisabled} = useTabBar();

  const {subcategory: selectedSubcategory, setImage, reset} = useEventCreationStore();
  const buttonIsDisabled = !selectedSubcategory.length;

  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();

  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {mutateAsync: reviveAEvent} = useReviveEvent();

  const [subCatsList, setSubCatsList] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [isCancelLoading, setIsCancelLoading] = useState(false);
  const [isReviveLoading, setIsReviveLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  const isFocused = useIsFocused();

  const cancelEvent = async () => {
    setIsCancelLoading(true);
    if (!route.params?.item?.event_id) {
      return;
    }
    await cancelAEvent({event_id: route.params?.item?.event_id});
    setIsCancelLoading(false);
    navigation.goBack();
  };

  const reviveEvent = async () => {
    setIsReviveLoading(true);
    if (!route.params?.item?.event_id) {
      return;
    }
    await reviveAEvent({event_id: route.params?.item?.event_id});
    setIsReviveLoading(false);
    navigation.goBack();
  };

  const submitDelete = () =>
    Alert.alert('Are you sure you want to delete this event?', '', [
      {
        text: 'No',
        style: 'cancel',
      },
      {text: 'Yes', onPress: () => handleDeleteSubmitted(), style: 'destructive'},
    ]);

  const handleDeleteSubmitted = async () => {
    setIsDeleteLoading(true);
    if (!route.params?.item?.event_id) {
      return;
    }
    await deleteEvent({event_id: route.params?.item?.event_id});
    try {
      await FirebaseChatsService.deleteGroupChat({event_id: route.params?.item?.event_id});
    } catch (error) {}
    setIsDeleteLoading(false);
    navigation.pop(2);
  };

  useEffect(() => {
    setIsCancelLoading(false);
    setIsReviveLoading(false);
    setIsDeleteLoading(false);
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    if (d && data && !searchValue) {
      setSubCatsList(organizeByCategory([], d, data));
    }
  }, [d, data, searchValue]);

  useEffect(() => {
    logScreenView('Create Event Template', 'CreateEventTemplate');
    // Hide tab bar when entering create event flow
    setIsTabBarDisabled(true);

    // Show tab bar when leaving the screen
    return () => {
      setIsTabBarDisabled(false);
    };
  }, [setIsTabBarDisabled]);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      setImage(data!.find(subcat => subcat.subcategory_id === selectedSubcategory[0])!.image_url);
      navigation.navigate(SCREENS.CREATE_EVENT_INFO, route.params?.item ? {item: route.params?.item} : undefined);
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    handleSearchCategory();
  }, [searchValue]);

  const handleSearchCategory = () => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      // BUG-031 FIX: Include selected categories in search results
      setSubCatsList(organizeByCategory(selectedSubcategory, d, inputData));
    } else if (data && d) {
      setSubCatsList(organizeByCategory(selectedSubcategory, d, data));
    }
  };

  const styles = StyleSheet.create({
    buttonContainer1: {
      flex: 1,
      backgroundColor: colors.statusGray,
    },
    buttonContainer2: {
      flex: 1,
      backgroundColor: colors.error,
    },
    rowView: {
      flexDirection: 'row',
      marginTop: 5,
    },
    buttonTextStyle: {
      color: colors.white,
    },
  });

  return (
    <>
      <GoBackHeader
        customCallback={() => {
          reset();
          setIsTabBarDisabled(false);
          navigation.goBack();
        }}
        isGradientShow={false}
      />
      <View style={{flex: 1, alignItems: 'center', backgroundColor: colors.background, paddingTop: 80}}>
        <View style={{width: '100%', alignItems: 'flex-start', paddingHorizontal: 16}}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: 24,
              color: colors.textPrimary,
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('events.start_by_choosing_category')}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 16,
          }}>
          <TextInput
            placeholder="Search by name"
            value={searchValue}
            onChangeText={setSearchValue}
            style={{
              flex: 1,
              height: 40,
              borderWidth: 1,
              borderColor: colors.textSecondary,
              borderRadius: 5,
              paddingHorizontal: 10,
              marginRight: 8,
            }}
          />
          <Button
            label="Search"
            onPress={handleSearchCategory}
            containerStyle={{backgroundColor: colors.primary, height: 40, borderRadius: 5}}
            textStyle={{color: colors.white, fontWeight: '600'}}
          />
        </View>

        <View
          style={{
            flex: 1,
            width: '100%',
          }}>
          <SubcategoriesLists
            searchValue={searchValue}
            categoriesList={d}
            filteredSubcategoriesData={subCatsList}
            isFromEventCreation
          />
        </View>
        <View
          style={{
            width: '100%',
            paddingHorizontal: 25,
            borderTopWidth: 1,
            borderTopColor: colors.border,
            paddingTop: 16,
            justifyContent: 'flex-end',
            marginBottom: isIOS ? bottom : bottom + 20,
          }}>
          <Button
            label={
              selectedSubcategory.length
                ? t('generic.continue') + ` with ${selectedSubcategory.length} selected`
                : t('generic.continue')
            }
            isLoading={isLoading}
            key={selectedSubcategory.length}
            onPress={handleSubmit}
            textStyle={{
              fontSize: 15,
              fontWeight: '500',
              color: buttonIsDisabled ? colors.statusGray : colors.white,
            }}
            disabled={isDeleteLoading || isReviveLoading || isCancelLoading || isLoading}
            containerStyle={{
              width: '100%',
              height: 40,
              borderRadius: 6,
              backgroundColor: buttonIsDisabled ? colors.gray100 : colors.primary,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          />
          {route.params?.item && (
            <View style={styles.rowView}>
              {!route.params?.item?.is_cancelled ? (
                <>
                  <Button
                    label={t('events.cancel_event')}
                    isLoading={isCancelLoading}
                    onPress={cancelEvent}
                    containerStyle={styles.buttonContainer1}
                    disabled={isDeleteLoading || isReviveLoading || isCancelLoading || isLoading}
                    textStyle={styles.buttonTextStyle}
                  />
                  <View style={{width: 10}} />
                </>
              ) : (
                <>
                  <Button
                    label={t('events.revive_event')} // This is the new revive button
                    isLoading={isReviveLoading}
                    onPress={reviveEvent} // Attach the revive function here
                    containerStyle={styles.buttonContainer1}
                    disabled={isDeleteLoading || isReviveLoading || isCancelLoading || isLoading}
                    textStyle={styles.buttonTextStyle}
                  />
                  <View style={{width: 10}} />
                </>
              )}
              {!route.params?.item?.is_cancelled && <View style={{width: 10}} />}
              <Button
                label={t('events.delete_event')}
                isLoading={isDeleteLoading}
                disabled={isDeleteLoading || isReviveLoading || isCancelLoading || isLoading}
                onPress={() => submitDelete()}
                containerStyle={styles.buttonContainer2}
                textStyle={styles.buttonTextStyle}
              />
            </View>
          )}
          <View
            style={{
              height: 24,
            }}
          />
          <ProgressDots dotsNumber={4} selectedDotNumber={1} />
        </View>
      </View>
    </>
  );
};

export default CreateEventTemplate;
