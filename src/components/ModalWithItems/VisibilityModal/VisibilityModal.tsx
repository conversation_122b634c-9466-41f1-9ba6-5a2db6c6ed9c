import {View, Platform} from 'react-native';
import ModalWithButtons from '../../ModalWithButtons';
import {FC, useEffect, useState} from 'react';
import ModalItem from '../ModalItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  chosenItem: string;
  onClose: () => void;
  onPress: (value: string) => () => void;
}

const VisibilityModal: FC<IProps> = ({isVisible, chosenItem, onClose, onPress}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const isAndroid = Platform.OS === 'android';
  const {bottom} = useSafeAreaInsets();
  const [items, setItems] = useState([
    {title: t('events.events_public'), isActive: false},
    {title: t('events.events_private'), isActive: false},
  ]);

  const handleItemClick = (index: number) => {
    const updatedItems = items.map((item, i) => ({
      ...item,
      isActive: i === index,
    }));
    setItems(updatedItems);
  };

  useEffect(() => {
    if (chosenItem) {
      setItems(prevItems => [
        ...prevItems.map(prevItem => (prevItem.title === chosenItem ? {...prevItem, isActive: true} : prevItem)),
      ]);
    }
  }, [chosenItem]);

  return (
    <ModalWithButtons
      onCloseModal={onClose}
      modalIsVisible={isVisible}
      onPress={items.find(item => item.isActive)?.title ? onPress(items.find(item => item.isActive)!.title) : () => {}}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingTop: 24,
          paddingHorizontal: 46,
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
        }}>
        {items.map((item, index) => (
          <View style={{width: '100%', marginTop: index === 0 ? 0 : 6, height: '40%'}} key={`visibility=${index}`}>
            <ModalItem flex={1} key={index} item={item} onPress={() => handleItemClick(index)} />
          </View>
        ))}
      </View>
    </ModalWithButtons>
  );
};

export default VisibilityModal;
