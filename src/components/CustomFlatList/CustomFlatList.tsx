import React, {useRef, useLayoutEffect, useMemo} from 'react';
import {
  useWindowDimensions,
  ScrollView,
  Animated,
  Platform,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import getStyles from './styles';
import {CategoryType} from '~types/categories';
import {useTranslation} from 'react-i18next';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useTheme} from '~contexts/ThemeContext';

const Item = ({data, onPress}: {data: CategoryType & {isChosen: boolean}; onPress: () => void}) => {
  const {t} = useTranslation();
  const {colors} = useTheme();
  const styles = getStyles(colors);
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.item,
        {
          marginVertical: data.isChosen ? 10 : 12,
          borderWidth: data.isChosen ? 2 : 0,
          borderColor: colors.secondary,
          marginHorizontal: 12,
        },
      ]}>
      <FastImage
        style={[
          StyleSheet.absoluteFill,
          {
            borderRadius: 100,
          },
        ]}
        source={{
          uri: data.image_url,
          priority: 'high',
        }}
      />
      <View
        style={{
          backgroundColor: data.isChosen ? colors.secondary + 'CC' : colors.overlayBackground,
          borderRadius: 100,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      />
      <Text style={styles.title}>
        {t(`categories.${data.category_name}`) === `categories.${data.category_name}`
          ? data?.category_repr
          : t(`categories.${data.category_name}`)}
      </Text>
    </TouchableOpacity>
  );
};

export const CustomFlatList = ({
  categories,
  setCategories,
  withoutMultiselect = false,
}: {
  categories: number[];
  setCategories: (categories: number) => void;
  withoutMultiselect?: boolean;
}) => {
  const {width} = useWindowDimensions();
  const {data: categoriesList} = useGetCategories();

  const flatListRef = useRef<ScrollView>(null);

  useLayoutEffect(() => {
    setTimeout(() => {
      flatListRef?.current?.scrollTo({x: 200, animated: true});
    }, 200);
  }, [categoriesList?.length, width]);

  if (!categoriesList?.length) {
    return null;
  }

  return (
    <ScrollView
      // style={{flex: 1}}
      contentContainerStyle={{height: '100%', alignItems: 'flex-start'}}
      ref={flatListRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      directionalLockEnabled={true}
      alwaysBounceVertical={false}>
      <Animated.FlatList
        style={{flex: 1}}
        contentContainerStyle={{flex: 1}}
        numColumns={Math.ceil(categoriesList.length / 4)}
        data={categoriesList}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => {
          return (
            <View
              style={{
                marginLeft:
                  index === Math.ceil(categoriesList.length / 4) || index === Math.ceil(categoriesList.length / 4) * 3
                    ? 50
                    : 0,
              }}>
              <Item
                data={{...item, isChosen: !!categories?.includes(item.category_id)}}
                onPress={() => {
                  if (withoutMultiselect) {
                    setCategories(item.category_id);
                  } else {
                    setCategories(item.category_id);
                  }
                }}
              />
            </View>
          );
        }}
      />
    </ScrollView>
  );
};
