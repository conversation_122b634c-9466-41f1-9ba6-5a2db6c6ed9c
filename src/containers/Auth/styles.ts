import {Platform, StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    closeBtnTxt: {
      color: colors.white,
      fontWeight: '600',
    },
    closeButton: {
      backgroundColor: colors.secondary,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 20,
      position: 'absolute',
      left: 20,
      right: 20,
      paddingVertical: 12,
    },
    descText: {
      color: colors.white,
      fontSize: 16,
    },
    welcomeText: {
      fontWeight: 'bold',
      color: colors.white,
      fontSize: 28,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.overlayBackground,
      justifyContent: 'flex-end',
      paddingBottom: 35,
      paddingLeft: 10,
    },
    notRobotTxt: {marginLeft: 6},
    flex: {
      flex: 1,
    },
    keyboardAwareScrollView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    imageContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    fullWidth: {
      paddingHorizontal: 32,
      alignItems: 'center',
      flexDirection: 'row',
      width: '100%',
    },
    iconContainer: {
      marginTop: 18,
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 24,
    },
    formContainer: {alignItems: 'center', paddingHorizontal: 30},
    forgotPasswordContainer: {width: '100%', flexDirection: 'row', justifyContent: 'space-between'},
    forgotPasswordText: {fontSize: 13, fontWeight: '500', color: colors.primary},
    loginButton: {
      width: '100%',
      height: 40,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary,
      borderRadius: 6,
      marginTop: 24,
      marginBottom: 24,
    },
    loginText: {color: colors.white, fontSize: 15, fontWeight: '500'},
    divider: {
      height: 1,
      flex: 1,
      backgroundColor: colors.gray100,
    },
    textLight: {
      textAlign: 'center',
      marginVertical: 3,
      color: colors.textSecondary,
      fontWeight: '400',
      marginHorizontal: 8,
    },
    socialButtonsContainer: {
      marginVertical: 8,
      paddingHorizontal: 30,
    },
    bottomContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'flex-end',
      flexDirection: 'row',
    },
    bottomText: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.textPrimary,
    },
    registerButton: {
      marginLeft: 5,
    },
    registerText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
    },
    buttonText: {color: colors.white, fontSize: 15, fontWeight: '500'},
    forgotPasswordButtonWrapper: {flex: 1, width: '100%', justifyContent: 'flex-end', paddingBottom: 30},
  });

export default createStyles;
