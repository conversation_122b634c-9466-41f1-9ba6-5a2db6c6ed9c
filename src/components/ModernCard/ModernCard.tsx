import React from 'react';
import {View, ViewStyle, TouchableOpacity, GestureResponderEvent} from 'react-native';
import {spacing, borderRadius, shadows} from '~constants/design';
import {useTheme} from '~contexts/ThemeContext';

interface ModernCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: keyof typeof spacing;
  margin?: keyof typeof spacing;
  borderRadius?: keyof typeof borderRadius;
  onPress?: (event?: GestureResponderEvent) => void;
  style?: ViewStyle;
  disabled?: boolean;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  padding = 'lg',
  margin = 'sm',
  borderRadius: borderRadiusProp = 'xl',
  onPress,
  style,
  disabled = false,
}) => {
  const {colors} = useTheme();

  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      backgroundColor: colors.surface,
      padding: spacing[padding],
      margin: spacing[margin],
      borderRadius: borderRadius[borderRadiusProp],
    };

    const variantStyles: Record<string, ViewStyle> = {
      default: {
        borderWidth: 1,
        borderColor: colors.border + '40',
      },
      elevated: {
        ...shadows.sm,
        borderWidth: 0,
      },
      outlined: {
        borderWidth: 1,
        borderColor: colors.border,
      },
      filled: {
        backgroundColor: colors.gray50,
        borderWidth: 0,
      },
    };

    const interactiveStyles: ViewStyle = onPress
      ? {
          opacity: disabled ? 0.6 : 1,
        }
      : {};

    return {
      ...baseStyles,
      ...variantStyles[variant],
      ...interactiveStyles,
    };
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[getCardStyles(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={onPress ? 0.95 : 1}>
      {children}
    </CardComponent>
  );
};

export default ModernCard;
