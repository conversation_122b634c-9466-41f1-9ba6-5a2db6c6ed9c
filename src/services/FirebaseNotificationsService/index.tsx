import Config from 'react-native-config';

class OneSignalServ {
  // async sendPushNotification(data: any) {
  //   const options = {
  //     method: 'POST',
  //     headers: {
  //       accept: 'application/json',
  //       Authorization: `Basic ${String(Config.ONE_SIGNAL_TOKEN)}`,
  //       'content-type': 'application/json',
  //     },

  //     body: JSON.stringify({
  //       app_id: String(Config.ONE_SIGNAL_APP_ID),
  //       contents: {en: data.message},
  //       name: '<PERSON><PERSON><PERSON>',
  //       headings: {
  //         en: data.title,
  //       },
  //       included_segments: ['Subscribed Users'],
  //       priority: 1,
  //       data: data.additional_data,
  //       filters: [{field: 'tag', relation: '=', key: 'uid', value: data.uid}],
  //       android_channel_id: 'ff42cf61-ab0d-4860-9ad2-92e6dfdddfe1',
  //     }),
  //   };

  //   try {
  //     await fetch('https://onesignal.com/api/v1/notifications', options);
  //     return true;
  //   } catch (err) {
  //     return false;
  //   }
  // }
  async sendPushNotificationForAllUsers(data: any) {
    const options = {
      method: 'POST',
      headers: {
        accept: 'application/json',
        Authorization: 'Basic MjI1YTBmZDUtZTAzOS00Yzg5LTkzNjMtYzEyODhiODM5MGMz',
        'content-type': 'application/json',
      },

      body: JSON.stringify({
        app_id: '91613d5a-aa94-437f-b62d-97791caaf3ab',
        contents: {en: data.message},
        name: 'Pyxi iOS',
        headings: {
          en: data.title,
        },
        included_segments: ['All'],
        priority: 1,
        android_channel_id: 'ff42cf61-ab0d-4860-9ad2-92e6dfdddfe1',
        data: data.additional_data,
      }),
    };

    try {
      const response = await fetch('https://onesignal.com/api/v1/notifications', options);
      response.json();
    } catch (err) {}
  }
}

const OneSignalService = new OneSignalServ();

export default OneSignalService;
