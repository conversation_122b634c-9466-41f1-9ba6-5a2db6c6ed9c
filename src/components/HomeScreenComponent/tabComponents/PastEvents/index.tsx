import {FlashList} from '@shopify/flash-list';
import {useCallback, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {NativeScrollEvent, NativeSyntheticEvent, Platform, RefreshControl, Text, View} from 'react-native';
import Animated from 'react-native-reanimated';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {Event} from '~types/api/event';
import {EVENTS_TIMEFRAME, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import DefaultItem from '../components/DefaultItem';
import {responsive, spacing} from '~constants';

const AnimatedFlatList = Animated.createAnimatedComponent(FlashList<Event>);

const PastEvents = ({scrollHandler}: {scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void}) => {
  const {width} = useSafeAreaFrame();
  const {top} = useSafeAreaInsets();
  const {t} = useTranslation();
  const {data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch} = useAllEvents({
    distance_km: 10000000,
    tab: TABS.SUBSCRIBED_EVENTS,
    order_by: ORDER_BY.END_DATE,
    order_dir: ORDER_DIR.DESC,
    event_age_group: null,
    enabled: true,
  });

  const [refreshing, setRefreshing] = useState(false);
  const eventsList = getListFromPages<Event>(data);

  const renderFooter = () => {
    return isFetchingNextPage ? <></> : null;
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const contentLayout = useMemo(() => {
    const platformLayout = Platform.OS === 'ios' ? 115 : 125;
    return platformLayout + top;
  }, [top]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      refetch();
      setRefreshing(false);
    }, 2000);
  }, []);

  if (!(isLoading || isFetching || isFetchingNextPage) && !eventsList?.length) {
    return (
      <Text
        style={{
          marginTop: 250,
          textAlign: 'center',
          paddingHorizontal: 30,
          fontSize: 16,
          fontWeight: '500',
        }}>
        {t('home.empty_list_past_events')}
      </Text>
    );
  }

  if (isLoading || !eventsList?.length) {
    return <></>;
  }
  console.log('pastEvent', eventsList);
  return (
    <View
      style={{
        flex: 1,
        marginTop: contentLayout,
      }}>
      <AnimatedFlatList
        // onScroll={scrollHandler}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        contentContainerStyle={{
          paddingBottom: responsive.getValue({xs: 80, sm: 100, md: 120}, 100),
          paddingHorizontal: responsive.getValue({xs: spacing.md, sm: spacing.lg, md: spacing.xl}, spacing.lg),
          paddingTop: spacing.sm,
        }}
        data={eventsList}
        keyExtractor={(item, index) => `${index}-${item.event_id}`}
        renderItem={({item, index}) => <DefaultItem item={item} i={index} />}
        ListFooterComponent={renderFooter}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        estimatedFirstItemOffset={10}
        estimatedItemSize={170}
        estimatedListSize={{height: 1700, width: width}}
      />
    </View>
  );
};

export default PastEvents;
