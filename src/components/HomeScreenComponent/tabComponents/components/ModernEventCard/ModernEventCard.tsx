import React from 'react';
import {View, Text, StyleSheet, Pressable, TouchableOpacity} from 'react-native';
import FastImage from 'react-native-fast-image';
import moment from 'moment-timezone';
import {useNavigation} from '@react-navigation/native';
import {Event} from '~types/api/event';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {CheckIcon} from '~assets/icons';
import LikeButton from '~components/LikeButton';
import {AnimatedPressable, FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows, responsive} from '~constants';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {accessibility} from '~utils/accessibility';
import {haptics} from '~utils/haptics';
import Share from 'react-native-share';
import {useTranslation} from 'react-i18next';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {debounce} from '~utils/viewCleanup';

interface ModernEventCardProps {
  item: Event;
  index: number;
  isMyEvents?: boolean;
  disableAnimation?: boolean;
}

const ModernEventCard: React.FC<ModernEventCardProps> = ({
  item,
  index,
  isMyEvents = false,
  disableAnimation = false,
}) => {
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {data: userAccount} = useGetUserAccount(auth().currentUser!.uid);

  // Event type detection
  const isPyxiSelectEvent = item.host_id === 'CmCCvWvpRVhhFlHdgU9QE6hbun82';
  const isNeighbourHoodEvent = item.host_id === 'bxcOenW6joNFwgiMBctR0zJT07F2';

  const isEventRunning = () => {
    const now = moment();
    return now.isBetween(moment(item.start_date), moment(item.end_date));
  };

  const handleEventPress = React.useMemo(
    () =>
      debounce(() => {
        haptics.buttonPress();
        setIsTabBarDisabled(true);
        navigation.navigate(SCREENS.HOME_EVENT, {
          tag: `${item.event_id}=tag`,
          eventId: item.event_id,
          item: item,
        });
      }, 300),
    [item, navigation, setIsTabBarDisabled],
  );

  const handleShare = React.useMemo(
    () =>
      debounce(async () => {
        haptics.selection();

        try {
          if (!item?.event_id) {
            return;
          }

          // Use the same URL format as EventDetails
          const redirectLink = `https://partner.pyxi.ai/event/detail/${item.event_id}`;
          const url = redirectLink;

          // Get user name for personalized message
          const userName = userAccount?.last_name
            ? `${userAccount.first_name} ${userAccount.last_name}`
            : userAccount?.first_name || 'Someone';

          // Use the same sharing logic as EventDetails with internationalized message
          await Share.open({
            message: t('invite_message', {userName, itemName: item.name, url}),
          });
        } catch (error) {
          console.log('Error sharing:', error);
        }
      }, 500),
    [item, userAccount, t],
  );

  const formatDateRange = () => {
    const startDate = moment.utc(item.start_date);
    const endDate = moment.utc(item.end_date);

    if (startDate.isSame(endDate, 'day')) {
      return startDate.format('MMM DD, YYYY');
    }
    return `${startDate.format('MMM DD')} - ${endDate.format('MMM DD, YYYY')}`;
  };

  const eventDate = formatDateRange();
  const accessibilityProps = accessibility.eventCardProps(
    item.name,
    eventDate,
    item.address_name,
    item.total_attendee_count,
  );

  const styles = createStyles(colors);

  return (
    <FadeIn delay={disableAnimation ? 0 : index * 100} duration={300}>
      <View style={styles.container}>
        {/* Main Card Container */}
        <View style={styles.card}>
          {/* Clickable area for event - excludes action buttons */}
          <AnimatedPressable
            onPress={handleEventPress}
            scaleValue={0.98}
            hapticFeedback={false}
            style={styles.clickableArea}
            {...accessibilityProps}>
            {/* Image Section */}
            <View style={styles.imageContainer}>
              <FastImage
                style={styles.eventImage}
                source={{
                  uri: item.image_url,
                  priority: 'high',
                }}
                resizeMode="cover"
              />

              {/* Dark overlay for better text readability */}
              <View style={styles.imageOverlay} />

              {/* Top Row - Status Indicators */}
              <View style={styles.topRow}>
                {isEventRunning() && (
                  <View style={styles.liveIndicator}>
                    <View style={styles.liveDot} />
                    <Text style={styles.liveText}>Happening Now</Text>
                  </View>
                )}

                {item.is_paid && (
                  <View style={styles.paidIndicator}>
                    <Text style={styles.paidText}>SALES END SOON</Text>
                  </View>
                )}
              </View>

              {/* Event Title Overlay */}
              <View style={styles.titleOverlay}>
                <Text style={styles.overlayTitle} numberOfLines={2}>
                  {item.name.toUpperCase()}
                </Text>
              </View>
            </View>

            {/* Content Section - Date and Price only */}
            <View style={styles.contentContainer}>
              {/* Date and Location */}
              <Text style={styles.eventDate}>
                {eventDate} • {item.address_name}
              </Text>

              {/* Bottom Row - Price only (action buttons moved outside) */}
              <View style={styles.bottomRowClickable}>
                {/* Price */}
                <View style={styles.priceContainer}>
                  {item.is_paid ? <Text style={styles.priceText}>$</Text> : <Text style={styles.priceText}>Free</Text>}
                </View>
              </View>
            </View>
          </AnimatedPressable>

          {/* Action Buttons - Outside clickable area */}
          {!isMyEvents && (
            <View style={styles.actionButtonsOverlay}>
              <TouchableOpacity
                onPress={handleShare}
                style={styles.actionButton}
                activeOpacity={0.7}
                {...accessibility.buttonProps('Share event', 'Share this event with friends')}>
                <Ionicons name="share-outline" size={20} color={colors.textSecondary} />
              </TouchableOpacity>

              <View style={styles.actionButton}>
                <LikeButton liked={item.user_liked} eventId={item.event_id} />
              </View>
            </View>
          )}
        </View>
      </View>
    </FadeIn>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginBottom: spacing.sm,
    },
    card: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.xl,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border + '30',
      position: 'relative',
    },
    clickableArea: {
      // No additional styles needed - inherits from card
    },
    imageContainer: {
      height: responsive.getValue({xs: 200, sm: 220, md: 240}, 220),
      position: 'relative',
    },
    eventImage: {
      width: '100%',
      height: '100%',
    },
    imageOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    topRow: {
      position: 'absolute',
      top: spacing.md,
      right: spacing.md,
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: spacing.xs,
    },
    liveIndicator: {
      backgroundColor: colors.error,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
      gap: spacing.xs,
    },
    liveDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.white,
    },
    liveText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium,
      letterSpacing: 0.5,
    },
    paidIndicator: {
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    paidText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      letterSpacing: 0.5,
    },
    titleOverlay: {
      position: 'absolute',
      bottom: spacing.xl,
      left: spacing.md,
      right: spacing.md,
    },
    overlayTitle: {
      color: colors.white,
      fontSize: responsive.getValue(
        {xs: typography.fontSize['2xl'], sm: typography.fontSize['3xl'], md: typography.fontSize['4xl']},
        typography.fontSize['3xl'],
      ),
      fontWeight: typography.fontWeight.bold,
      lineHeight: responsive.getValue(
        {
          xs: typography.fontSize['2xl'] * 1.1,
          sm: typography.fontSize['3xl'] * 1.1,
          md: typography.fontSize['4xl'] * 1.1,
        },
        typography.fontSize['3xl'] * 1.1,
      ),
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 3,
    },
    contentContainer: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      gap: spacing.xs,
    },
    eventDate: {
      color: colors.textSecondary,
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
    },
    bottomRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    bottomRowClickable: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    priceContainer: {
      flex: 1,
    },
    priceText: {
      color: colors.textPrimary,
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.bold,
      lineHeight: typography.fontSize.base * typography.lineHeight.tight,
    },
    actionButtonsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.xs,
      backgroundColor: 'transparent',
    },
    actionButtonsOverlay: {
      position: 'absolute',
      bottom: spacing.sm,
      right: spacing.sm,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.xs,
      backgroundColor: 'transparent',
    },
    actionButton: {
      padding: spacing.xs,
      borderRadius: borderRadius.full,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },
  });

export default ModernEventCard;
