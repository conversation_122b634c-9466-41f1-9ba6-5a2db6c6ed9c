import React, {useEffect, useMemo, useRef, useState} from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity, Animated} from 'react-native';
import BottomSheet from '@gorhom/bottom-sheet';
import {Event} from '~types/api/event';
import {EventShow} from './EventShow';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {DeviceEventEmitter} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const EventDetailsBottomSheet = ({event}: {event: Event}) => {
  const {colors} = useTheme();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [sheetPosition, setSheetPosition] = useState(1); // 0: 20%, 1: 50%, 2: 95%

  // snap points for bottom sheet (like Apple Maps)
  const snapPoints = useMemo(() => ['20%', '50%', '95%'], []);

  const styles = useMemo(
    () =>
      StyleSheet.create({
        closeIcon: {
          paddingHorizontal: 16,
          paddingVertical: 8,
          alignSelf: 'flex-end',
          borderRadius: 20,
          backgroundColor: colors.surface,
          marginRight: 16,
          marginTop: 8,
          shadowColor: colors.black,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        },
        container: {
          flex: 1,
          backgroundColor: colors.background,
        },
        image: {
          width: '100%',
          height: 200,
          borderRadius: 12,
          marginBottom: 12,
        },
        title: {
          fontSize: 18,
          fontWeight: 'bold',
          color: colors.textPrimary,
        },
        address: {
          fontSize: 14,
          color: colors.textSecondary,
          marginBottom: 8,
        },
        description: {
          fontSize: 14,
          color: colors.textPrimary,
        },
        handleIndicator: {
          width: 40,
          height: 4,
          backgroundColor: colors.gray300,
          borderRadius: 2,
          alignSelf: 'center',
          marginTop: 8,
          marginBottom: 16,
        },
      }),
    [colors],
  );

  useEffect(() => {
    if (event) {
      bottomSheetRef.current?.snapToIndex(0);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [event]);

  const onSheetClose = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      bottomSheetRef.current?.close();
      DeviceEventEmitter.emit('eventSelection', {event: null});
    });
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      onChange={index => setSheetPosition(index)}
      handleStyle={{
        backgroundColor: colors.background,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
      }}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
      }}
      enablePanDownToClose={false}>
      <Animated.View style={[styles.container, {opacity: fadeAnim}]}>
        {/* Modern handle indicator */}
        <View style={styles.handleIndicator} />

        <TouchableOpacity onPress={onSheetClose} style={styles.closeIcon} activeOpacity={0.7}>
          <Ionicons name="close" size={24} color={colors.textSecondary} />
        </TouchableOpacity>
        <EventShow eventId={event.event_id} item={event} sheetPosition={sheetPosition} />
      </Animated.View>
    </BottomSheet>
  );
};

export default EventDetailsBottomSheet;
