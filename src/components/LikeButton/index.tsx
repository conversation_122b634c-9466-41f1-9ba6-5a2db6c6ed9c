import {TouchableOpacity} from 'react-native';
import styles from './styles';
import {FC, useEffect, useRef, useState} from 'react';
import {useLikeEvent} from '~hooks/event/useLikeEvent';
import {useRemoveLikeFromEvent} from '~hooks/event/useRemoveLikeFromEvent';
import LottieView from 'lottie-react-native';
import {haptics} from '~utils/haptics';
import {accessibility} from '~utils/accessibility';

interface IProps {
  liked: boolean;
  eventId?: number;
}

const LikeButton: FC<IProps> = ({liked, eventId}) => {
  const {mutateAsync: likeEvent} = useLikeEvent();
  const {mutateAsync: removeLikeFromEvent} = useRemoveLikeFromEvent();
  const [animation, setAnimation] = useState<any>(null);

  const [isLiked, setIsLiked] = useState(liked);
  const lastItemId = useRef(eventId);

  // Update liked state when prop changes
  useEffect(() => {
    setIsLiked(liked);
  }, [liked]);

  useEffect(() => {
    if (isLiked) {
      // Start animation immediately without delay
      setTimeout(() => animation?.play(), 0);
    } else {
      animation?.reset();
    }
  }, [isLiked, animation]);

  const toggleStatus = () => {
    setIsLiked(!isLiked);
  };

  if (eventId !== lastItemId.current) {
    lastItemId.current = eventId;
    setIsLiked(liked);
  }

  const handlePress = async (event: any) => {
    // Prevent event propagation to parent components
    event?.stopPropagation?.();

    if (!eventId) {
      return;
    }

    try {
      const prevIsLiked = isLiked;

      // Provide immediate haptic feedback
      if (prevIsLiked) {
        haptics.warning(); // Unlike action
        await removeLikeFromEvent({eventId});
        toggleStatus();
        return;
      }

      haptics.likeAction(); // Like action
      await likeEvent({eventId});
      toggleStatus();
    } catch (e) {
      console.log(e);
      // Provide error feedback if the action fails
      haptics.error();
    }
  };

  const accessibilityProps = accessibility.buttonProps(
    isLiked ? 'Unlike event' : 'Like event',
    isLiked ? 'Remove this event from your liked events' : 'Add this event to your liked events',
  );

  return (
    <TouchableOpacity style={styles.likeContainer} onPress={handlePress} {...accessibilityProps}>
      <LottieView
        source={require('../../assets/lottie/heart2.json')}
        style={{width: 37, height: 37}}
        resizeMode={'contain'}
        autoPlay={false}
        loop={false}
        ref={animation => setAnimation(animation)}
      />
    </TouchableOpacity>
  );
};

export default LikeButton;
