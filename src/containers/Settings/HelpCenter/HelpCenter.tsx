import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Text, View, ScrollView} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {contactUs} from '~Utils/Settings';
import ModernButton from '~components/ModernButton';
import ModernCard from '~components/ModernCard/ModernCard';
import {ModernHeader} from '~components/ModernHeader';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
// import { useChatStore } from '~providers/chats/zustand';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import OneSignal from 'react-native-onesignal';
import {useMapsContext} from '~providers/maps/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import getStyles from './styles';
import {SCREENS} from '~constants';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';

const HelpCenter = () => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const {navigate} = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  // const {clearState} = useChatStore();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {bottom} = useSafeAreaInsets();
  const {goBack} = useNavigation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {resetMapsState} = useMapsContext();
  const {resetUser} = useUserStore();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    console.log(auth().currentUser?.providerData[0].providerId);

    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Help Center', 'HelpCenter');
  }, []);

  const deleteAccount = async () => {
    if (auth().currentUser?.providerData[0].providerId == 'password') {
      navigate(SCREENS.PASSWORD_FOR_DELETE_ACCOUNT);
    } else {
      navigate(SCREENS.DELETE_LOADING);
    }
    return;
    // setIsLoading(true);
    // const currentUser = auth().currentUser;
    // currentUser
    //   ?.delete()
    //   .then(async () => {
    //     OneSignal.disablePush(true);
    //     if (userType === 'personal') {
    //       await updateUserMutation({
    //         coords_real: null,
    //       });
    //     }
    //     resetMapsState();
    //     resetUser();
    //     await FirebaseAuth.logOut();
    //     setIsLoading(false);
    //   })
    //   .catch(error => {
    //     console.log(error);

    //     Alert.alert(error.message ? error.message : 'Failed to delete account.');
    //     setIsLoading(false);
    //   });

    // const providerId = auth().currentUser?.providerData[0].providerId;
    // if (providerId === 'google.com' || providerId === 'apple.com') {
    //   navigation.navigate(SCREENS.DELETE_LOADING);
    // } else {
    //   navigation.navigate(SCREENS.PASSWORD_FOR_DELETE_ACCOUNT);
    // }
  };

  const handleDeleteAccountAlert = async () => {
    Alert.alert(t('settings.deleteAccountAlert.title'), t('settings.deleteAccountAlert.description'), [
      {onPress: deleteAccount, text: t('settings.deleteAccountAlert.delete'), style: 'destructive'},
      {text: t('settings.deleteAccountAlert.cancel'), style: 'cancel', isPreferred: true},
    ]);
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.help_center') || 'Help Center'}
        subtitle={t('settings.help_center_subtitle') || 'Get support and manage your account'}
        variant="default"
      />

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingHorizontal: spacing.lg,
          paddingBottom: spacing['6xl'],
        }}
        showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <Animated.View entering={FadeInDown.delay(100).duration(400)}>
          <ModernCard variant="default" padding="xl" margin="md">
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                textAlign: 'center',
                marginBottom: spacing.md,
              }}>
              {t('settings.contact_us')}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                textAlign: 'center',
                lineHeight: typography.fontSize.base * typography.lineHeight.relaxed,
              }}>
              {t('settings.contact_us_body')}
            </Text>
          </ModernCard>
        </Animated.View>

        {/* Support Actions */}
        <Animated.View entering={FadeInDown.delay(200).duration(400)}>
          <Text
            style={{
              fontSize: typography.fontSize.lg,
              fontWeight: typography.fontWeight.bold,
              color: colors.textPrimary,
              marginBottom: spacing.sm,
              marginTop: spacing.md,
            }}>
            {t('settings.support_actions') || 'Support Actions'}
          </Text>

          <ModernCard variant="default" padding="lg" margin="none">
            <ModernButton
              title={t('settings.contact_us')}
              onPress={contactUs}
              variant="primary"
              size="lg"
              fullWidth
              hapticFeedback
              style={{marginBottom: spacing.md}}
            />

            <ModernButton
              title={t('settings.delete_account')}
              onPress={handleDeleteAccountAlert}
              variant="outline"
              size="lg"
              fullWidth
              loading={isLoading}
              hapticFeedback
              style={{
                borderColor: colors.danger,
              }}
              textStyle={{
                color: colors.danger,
              }}
            />
          </ModernCard>
        </Animated.View>

        {/* Additional Help */}
        <Animated.View entering={FadeInDown.delay(300).duration(400)}>
          <Text
            style={{
              fontSize: typography.fontSize.lg,
              fontWeight: typography.fontWeight.bold,
              color: colors.textPrimary,
              marginBottom: spacing.sm,
              marginTop: spacing.lg,
            }}>
            {t('settings.additional_help') || 'Additional Help'}
          </Text>

          <ModernCard variant="default" padding="lg" margin="none">
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.sm * typography.lineHeight.relaxed,
                marginBottom: spacing.md,
              }}>
              {t('settings.help_description') ||
                'Need more help? Check out our FAQ section or reach out to our support team directly.'}
            </Text>

            <ModernButton
              title={t('settings.view_faq') || 'View FAQ'}
              onPress={() => {
                /* Navigate to FAQ */
              }}
              variant="ghost"
              size="md"
              fullWidth
              hapticFeedback
            />
          </ModernCard>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

export default HelpCenter;
