import React from 'react';
import {View, Text, ScrollView, Pressable} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography} from '~constants/design';
import {haptics} from '~utils/haptics';
import {useTranslation} from 'react-i18next';
import {MapIcon} from '~assets/icons';

interface QuickFiltersProps {
  selectedTimeframe: any;
  onTimeframeSelect: (timeframe: any) => void;
  isMapView: boolean;
  setIsMapView: (value: boolean) => void;
}

const QuickFilters: React.FC<QuickFiltersProps> = ({selectedTimeframe, onTimeframeSelect, isMapView, setIsMapView}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  // Quick filter options inspired by Meetup
  const quickFilters = [
    {id: 1, title: t('home.upcoming_events'), key: 'upcoming_events'},
    {id: 2, title: t('events.today'), key: 'today'},
    {id: 3, title: t('events.tomorrow'), key: 'tomorrow'},
    {id: 4, title: t('events.this_week'), key: 'this_week'},
    {id: 5, title: t('events.weekend'), key: 'weekend'},
    {id: 6, title: t('events.next_week'), key: 'next_week'},
  ];

  const handleFilterPress = (filter: any) => {
    haptics.light();
    onTimeframeSelect(filter);
  };

  const FilterChip = ({filter}: {filter: any}) => {
    const isSelected = selectedTimeframe?.title === filter.title;

    return (
      <Pressable
        onPress={() => handleFilterPress(filter)}
        style={({pressed}) => ({
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs,
          borderRadius: borderRadius.full,
          backgroundColor: isSelected ? colors.primary : colors.surface,
          borderWidth: 1,
          borderColor: isSelected ? colors.primary : colors.border,
          marginRight: spacing.xs,
          minHeight: 32,
          justifyContent: 'center',
          transform: [{scale: pressed ? 0.95 : 1}],
        })}>
        <Text
          style={{
            fontSize: typography.fontSize.xs,
            fontWeight: typography.fontWeight.semibold,
            color: isSelected ? colors.white : colors.textPrimary,
          }}>
          {filter.title}
        </Text>
      </Pressable>
    );
  };

  // View Toggle Component
  const ViewToggle = () => (
    <View
      style={{
        flexDirection: 'row',
        backgroundColor: colors.surface,
        borderRadius: borderRadius.full,
        padding: 3,
        borderWidth: 1,
        borderColor: colors.border,
        height: 40,
        width: 80,
        marginLeft: spacing.sm,
        // Left shadow on the entire toggle
        shadowColor: '#000',
        shadowOffset: {
          width: -3,
          height: 0,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 3,
      }}>
      <Pressable
        onPress={() => setIsMapView(false)}
        style={({pressed}) => ({
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.xs,
          borderRadius: borderRadius.full,
          backgroundColor: !isMapView ? colors.textPrimary : 'transparent',
          flex: 1,
          justifyContent: 'center',
          transform: [{scale: pressed ? 0.95 : 1}],
        })}>
        {/* List Icon */}
        <View style={{alignItems: 'center', justifyContent: 'center'}}>
          <View
            style={{
              width: 16,
              height: 2.5,
              backgroundColor: !isMapView ? colors.white : colors.textSecondary,
              marginBottom: 3,
              borderRadius: 1,
            }}
          />
          <View
            style={{
              width: 16,
              height: 2.5,
              backgroundColor: !isMapView ? colors.white : colors.textSecondary,
              marginBottom: 3,
              borderRadius: 1,
            }}
          />
          <View
            style={{
              width: 16,
              height: 2.5,
              backgroundColor: !isMapView ? colors.white : colors.textSecondary,
              borderRadius: 1,
            }}
          />
        </View>
      </Pressable>

      <Pressable
        onPress={() => setIsMapView(true)}
        style={({pressed}) => ({
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.xs,
          borderRadius: borderRadius.full,
          backgroundColor: isMapView ? colors.textPrimary : 'transparent',
          flex: 1,
          justifyContent: 'center',
          transform: [{scale: pressed ? 0.95 : 1}],
        })}>
        {/* Map Icon */}
        <MapIcon color={isMapView ? colors.white : colors.textSecondary} size={20} />
      </Pressable>
    </View>
  );

  return (
    <View style={{paddingHorizontal: spacing.sm}}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={{flex: 1}}
          contentContainerStyle={{
            alignItems: 'center',
            paddingRight: spacing.sm,
          }}>
          {quickFilters.map(filter => (
            <FilterChip key={filter.id} filter={filter} />
          ))}
        </ScrollView>

        {/* View Toggle */}
        <ViewToggle />
      </View>
    </View>
  );
};

export default QuickFilters;
