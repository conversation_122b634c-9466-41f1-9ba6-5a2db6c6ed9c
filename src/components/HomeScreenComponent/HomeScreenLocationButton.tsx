import {Text, Pressable, TouchableOpacity, View, Image, DeviceEventEmitter} from 'react-native';
import {FloatingPlusIcon, NavigationIcon, SmallChevronIcon} from '~assets/icons';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import Animated from 'react-native-reanimated';
import {useGetUserGroups} from '~hooks/user/useGetUserGroups';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import LocationModal, {Location} from '~components/LocationModal';
import {useEffect, useState} from 'react';
import {useMapsContext} from '~providers/maps/zustand';
import {useTranslation} from 'react-i18next';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {HeaderDropdown} from '~components/HeaderDropdown';
import {RadiusModal} from '~components/RadiusModal';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {Business} from '~types/api/business';
import {User} from '~types/api/user';
import {useHomeStore} from '~providers/home/<USER>';
import useHomeScreenAnimation from './useHomeScreenAnimation';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import NavigationSquareIcon from '~assets/icons/NavigationSquareIcon';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  setSelectedTab?: any;
}

const HomeScreenLocationButton = ({setSelectedTab}: IProps) => {
  const {colors} = useTheme();
  const {currentPositionState, userLocation, setCurrentPositionState} = useMapsContext();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {data: userAccountData} = useGetUserAccount(auth().currentUser!.uid);
  const {data: businessAccountData} = useGetBusinessAccount(auth().currentUser!.uid);
  const {data: userGroups} = useGetUserGroups();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const [locationModalIsVisible, setLocationModalIsVisible] = useState(false);
  const {t} = useTranslation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const {getCurrentPosition} = useGetCurrentPosition();
  const [isRadiusModal, setIsRadiusModal] = useState(false);
  const {isForKids, toggleIsForKids} = useHomeStore();
  const [isFiltered, setIsFiltered] = useState(false);
  const {scrollHandler, scrollAnimatedStyle, addEventButtonStyle} = useHomeScreenAnimation();

  const data = userType === 'personal' ? userAccountData : businessAccountData;

  const isCurrentUserLocation = currentPositionState?.address === userLocation?.locationName;

  const handleUseCurrentLocation = () => {
    getCurrentPosition(true);
  };

  const handleLocationChange = async (location: Location | null) => {
    if (!location) {
      return;
    }
    setCurrentPositionState(location);
    DeviceEventEmitter.emit('homeLocationChange', {lat: location.latitude, long: location.longitude});

    try {
      if (businessAccountData?.uid) {
        const payload = {uid: auth().currentUser!.uid, coords: {lat: location.latitude, long: location.longitude}};
        updateBusinessMutation(payload);
        return;
      }

      await updateUserMutation({
        coords_real: {lat: location.latitude, long: location.longitude},
      });
    } catch (error) {
      console.log(error);
    }
    if (setSelectedTab) {
      setSelectedTab();
    }
  };

  const handleRadiusPress = () => {
    setTimeout(() => {
      setIsRadiusModal(true);
      setIsFiltered(true);
    }, 500);
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 5,
      }}>
      <View style={{alignItems: 'center', flexDirection: 'row'}}>
        <TouchableOpacity
          style={{
            padding: 6,
            borderRadius: 8,
            backgroundColor: isCurrentUserLocation ? colors.statusGreen : colors.border,
          }}
          onPress={() => {
            setLocationModalIsVisible(true);
          }}>
          <NavigationSquareIcon color={isCurrentUserLocation ? colors.white : colors.textPrimary} />
        </TouchableOpacity>
      </View>

      <LocationModal
        isVisible={locationModalIsVisible}
        close={() => {
          setLocationModalIsVisible(false);
        }}
        onLocationChange={handleLocationChange}
        isFromHomeScreen
      />
      <RadiusModal modalIsVisible={isRadiusModal} onClose={() => setIsRadiusModal(false)} />
    </View>
  );
};

export default HomeScreenLocationButton;
