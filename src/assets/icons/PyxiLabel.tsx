import {Path, Svg} from 'react-native-svg';

const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = () => (
  <Svg width="82" height="32" viewBox="0 0 82 32" fill="none">
    <Path
      d="M43.9157 5.55027C44.198 5.54582 44.4784 5.59595 44.7402 5.69767C45.002 5.7994 45.2398 5.95064 45.4395 6.1424C45.6392 6.33416 45.7967 6.56253 45.9026 6.81392C46.0086 7.06531 46.0608 7.3346 46.0561 7.60575V9.71469C46.0368 10.4051 45.9566 11.0927 45.8164 11.7702C45.6751 12.4395 45.4569 13.0916 45.1657 13.7147C44.6052 14.9602 43.8148 16.098 42.8326 17.0733C42.3187 17.5476 41.7677 17.9833 41.1844 18.3765C40.6047 18.7631 39.9832 19.0884 39.3308 19.3467C38.9669 19.4783 38.603 19.6016 38.252 19.7085C37.8865 19.8226 37.5131 19.9119 37.1346 19.9757V24.728C37.1415 24.9945 37.0901 25.2595 36.984 25.5059C36.8778 25.7523 36.7191 25.9746 36.5182 26.1586C36.3198 26.3478 36.0837 26.4966 35.824 26.5962C35.5644 26.6958 35.2865 26.7441 35.007 26.7382C34.7336 26.7417 34.4622 26.6921 34.2092 26.5925C33.9561 26.4929 33.7266 26.3453 33.5343 26.1586C33.3328 25.9748 33.1735 25.7526 33.0666 25.5063C32.9597 25.2599 32.9076 24.9948 32.9136 24.728V19.951C32.5366 19.8867 32.1647 19.7974 31.8006 19.6838C31.4452 19.5769 31.0856 19.4536 30.7175 19.322C30.0659 19.0646 29.4458 18.7392 28.8681 18.3518C28.2848 17.9586 27.7338 17.5229 27.2199 17.0487C26.2241 16.0772 25.4217 14.9389 24.8526 13.69C24.3217 12.4205 24.0318 11.07 23.9964 9.70235V7.59342C23.9917 7.32227 24.0439 7.05298 24.1499 6.80159C24.2558 6.5502 24.4133 6.32183 24.613 6.13007C24.8127 5.9383 25.0505 5.78706 25.3123 5.68534C25.5741 5.58362 25.8545 5.53349 26.1369 5.53793C26.4151 5.53131 26.6917 5.58054 26.949 5.68247C27.2062 5.78441 27.4385 5.93681 27.6309 6.12991C27.8273 6.32065 27.9816 6.54748 28.0846 6.79683C28.1876 7.04618 28.2371 7.31292 28.2302 7.58109V9.55847C28.2248 10.4303 28.407 11.2939 28.7654 12.0949C29.1095 12.863 29.6036 13.5607 30.2209 14.1504C30.8367 14.7423 31.5628 15.2178 32.3614 15.5523C33.1966 15.8927 34.0953 16.0662 35.0027 16.062C36.3446 16.0665 37.658 15.6905 38.7782 14.9811C39.8985 14.2717 40.7757 13.2606 41.3 12.0744C41.657 11.273 41.8377 10.4095 41.8308 9.53791V7.59342C41.8248 7.325 41.8751 7.05815 41.9788 6.80881C42.0825 6.55948 42.2375 6.33278 42.4345 6.14225C42.6251 5.95029 42.8552 5.79853 43.1102 5.69661C43.3652 5.5947 43.6395 5.54486 43.9157 5.55027Z"
      fill="#1D1E20"
    />
    <Path
      d="M71.8018 8.11551C71.7063 9.70065 71.2441 11.2454 70.449 12.6376C69.6764 13.9891 68.6054 15.1621 67.3111 16.0744C68.6143 17.0005 69.6911 18.1889 70.4662 19.5564C71.2688 20.9608 71.7257 22.5232 71.8018 24.1237V24.6869C71.8059 24.9579 71.7532 25.2269 71.6471 25.478C71.541 25.7292 71.3834 25.9573 71.1839 26.1489C70.9843 26.3406 70.7467 26.4919 70.4852 26.5938C70.2237 26.6957 69.9435 26.7462 69.6613 26.7424C69.3782 26.745 69.0973 26.6937 68.835 26.5914C68.5727 26.489 68.3341 26.3378 68.133 26.1463C67.9281 25.9609 67.7658 25.7364 67.6567 25.4872C67.5476 25.2379 67.494 24.9695 67.4995 24.6992V24.3703C67.4593 23.5435 67.2559 22.7316 66.9001 21.9777C66.5539 21.256 66.077 20.5991 65.4917 20.0373C64.9024 19.4709 64.2104 19.0126 63.4497 18.6848C62.6622 18.3418 61.8099 18.1572 60.9453 18.1422C60.8912 18.1643 60.8329 18.1755 60.7741 18.1751C60.7039 18.1787 60.6337 18.1675 60.5686 18.1422C59.7039 18.1562 58.8514 18.341 58.0642 18.6848C57.3035 19.0126 56.6115 19.4709 56.0222 20.0373C55.4343 20.6006 54.9521 21.257 54.5967 21.9777C54.2286 22.7268 54.0302 23.5422 54.0144 24.3703V24.6992C54.0191 24.9694 53.9652 25.2376 53.8561 25.4867C53.747 25.7358 53.5852 25.9604 53.3809 26.1463C53.1798 26.3378 52.9412 26.489 52.6789 26.5914C52.4166 26.6937 52.1357 26.745 51.8526 26.7424C51.5704 26.7462 51.2902 26.6957 51.0287 26.5938C50.7672 26.4919 50.5296 26.3406 50.33 26.1489C50.1304 25.9573 49.9729 25.7292 49.8668 25.478C49.7607 25.2269 49.708 24.9579 49.7121 24.6869V24.1278C49.7875 22.5267 50.246 20.9639 51.052 19.5605C51.8254 18.1919 52.9024 17.0032 54.2071 16.0785C52.9116 15.1674 51.8404 13.9941 51.0691 12.6417C50.2726 11.2499 49.809 9.70508 49.7121 8.11963V7.56053C49.7074 7.28938 49.7597 7.02009 49.8656 6.7687C49.9715 6.51731 50.129 6.28894 50.3287 6.09718C50.5284 5.90542 50.7662 5.75418 51.028 5.65245C51.2898 5.55073 51.5702 5.5006 51.8526 5.50504C52.4222 5.50502 52.9689 5.72092 53.3737 6.10582C53.7785 6.49072 54.0088 7.0135 54.0144 7.56053V7.79075C54.0311 8.62004 54.2295 9.43666 54.5967 10.1874C54.9497 10.9131 55.4322 11.574 56.0222 12.1402C56.6102 12.7017 57.3029 13.1522 58.0642 13.468C58.8545 13.8008 59.7058 13.9797 60.5686 13.9942H60.9453C62.6522 13.9585 64.2796 13.2948 65.4917 12.1402C66.0792 11.5755 66.5562 10.9141 66.9001 10.1874C67.2554 9.43195 67.4587 8.61878 67.4995 7.79075V7.56053C67.4917 7.14915 67.6128 6.74501 67.8469 6.40041C68.081 6.05581 68.4174 5.7866 68.8126 5.62763C69.2078 5.46866 69.6435 5.42723 70.0635 5.50872C70.4835 5.59021 70.8683 5.79085 71.1682 6.08469C71.3764 6.27379 71.5404 6.50308 71.6496 6.75745C71.7588 7.01182 71.8107 7.28552 71.8018 7.56053V8.11551Z"
      fill="#1D1E20"
    />
    <Path
      d="M79.4776 6.93566C80.0472 6.94108 80.5916 7.1622 80.9924 7.55094C81.3932 7.93969 81.6181 8.46465 81.618 9.01171V24.5964C81.6103 25.1393 81.3823 25.6578 80.9825 26.0416C80.5828 26.4255 80.0429 26.6445 79.4776 26.6519C79.1968 26.6588 78.9175 26.6098 78.6574 26.5079C78.3973 26.406 78.162 26.2535 77.9664 26.0599C77.7667 25.8681 77.6092 25.6397 77.5033 25.3883C77.3974 25.1369 77.3453 24.8676 77.3499 24.5964V9.01171C77.3475 8.74267 77.4007 8.47583 77.5065 8.22671C77.6123 7.97759 77.7687 7.75115 77.9664 7.56053C78.1589 7.36082 78.3928 7.20204 78.6531 7.09442C78.9133 6.98679 79.1942 6.93272 79.4776 6.93566Z"
      fill="#1D1E20"
    />
    <Path
      d="M79.4733 4.80618C79.1453 4.81222 78.8196 4.75286 78.5168 4.63186C78.214 4.51086 77.9405 4.33085 77.7138 4.1032C77.25 3.65032 76.9901 3.04056 76.9901 2.40537C76.9901 1.77017 77.25 1.16041 77.7138 0.707535C77.9397 0.478538 78.2128 0.297359 78.5158 0.175599C78.8188 0.0538382 79.1449 -0.00581909 79.4733 0.000447128C79.8053 -0.0047697 80.135 0.055264 80.442 0.17686C80.7489 0.298456 81.0268 0.479046 81.2584 0.707535C81.4932 0.929079 81.6797 1.19332 81.8071 1.48482C81.9344 1.77633 81.9999 2.08926 81.9999 2.40537C81.9999 2.72147 81.9344 3.03441 81.8071 3.32591C81.6797 3.61742 81.4932 3.88166 81.2584 4.1032C81.026 4.33036 80.7479 4.50981 80.441 4.63065C80.1341 4.75149 79.8049 4.81121 79.4733 4.80618Z"
      fill="#1D1E20"
    />
    <Path
      d="M22.1982 13.495C21.9831 12.6253 21.661 11.7832 21.2388 10.9864C20.815 10.1905 20.2907 9.44765 19.6776 8.77458C19.0585 8.08837 18.3473 7.48383 17.5626 6.97672C15.9399 5.92803 14.0617 5.29986 12.1108 5.15332C10.1599 5.00679 8.20257 5.34688 6.42935 6.14051C5.56944 6.54086 4.76395 7.04074 4.03088 7.62897C3.30488 8.21354 2.65873 8.88363 2.10774 9.62334C1.55415 10.3672 1.09451 11.1714 0.738436 12.0191C0.381973 12.8674 0.152924 13.7599 0.0581442 14.6699C0.0363399 14.8497 0.018897 15.0211 0.00581447 15.1883C-0.00726808 15.3556 0.00581447 15.5312 0.00581447 15.711V29.8848C0.000678896 30.2996 0.124366 30.7065 0.361137 31.0536C0.597907 31.4008 0.937057 31.6726 1.33543 31.8345C1.7338 31.9963 2.17338 32.0409 2.59824 31.9625C3.02309 31.884 3.41402 31.6862 3.72126 31.3942C3.92825 31.1999 4.09094 30.9666 4.19903 30.7089C4.30712 30.4513 4.35826 30.1749 4.34922 29.8973V24.4034C5.52809 25.248 6.85827 25.8786 8.27398 26.264C9.69767 26.6517 11.1883 26.7597 12.6566 26.5817C14.1201 26.4036 15.5333 25.9534 16.8169 25.2563C18.1095 24.5619 19.2403 23.6214 20.1399 22.4927C20.8573 21.5815 21.4223 20.5687 21.8144 19.4907C22.2103 18.414 22.4353 17.2865 22.4816 16.1458C22.5077 15.2541 22.4124 14.363 22.1982 13.495ZM8.67954 8.61988C8.57488 8.66169 8.46586 8.71186 8.3612 8.76204C8.4397 8.72441 8.51819 8.68259 8.60105 8.64915L8.67954 8.61988ZM21.7185 15.8657C21.6907 15.9844 21.6216 16.0905 21.5225 16.1665C21.4234 16.2425 21.3003 16.2839 21.1734 16.2838H19.1761C19.1783 16.32 19.1783 16.3563 19.1761 16.3925C19.1257 17.335 18.8812 18.2587 18.4566 19.1102C18.0357 19.9603 17.4664 20.7351 16.7733 21.4014C16.426 21.7005 16.0492 21.9666 15.6482 22.1958C15.2601 22.4299 14.8545 22.6474 14.4271 22.8522C13.9665 23.0484 13.4876 23.2024 12.9968 23.3122L11.8717 25.6201C11.8322 25.7176 11.7635 25.8018 11.6743 25.8621C11.5851 25.9223 11.4793 25.956 11.3702 25.9588C11.3297 25.9649 11.2885 25.9649 11.2481 25.9588C11.1242 25.9337 11.0132 25.8685 10.9337 25.774C10.8543 25.6795 10.8113 25.5617 10.812 25.4403V23.5003C9.25296 23.4138 7.75411 22.8931 6.49923 22.0019C5.24436 21.1107 4.288 19.8877 3.74742 18.483C3.63629 18.1839 3.54596 17.878 3.47705 17.5674L1.0655 16.4928C0.949159 16.4431 0.854239 16.3565 0.796732 16.2475C0.739226 16.1386 0.722646 16.014 0.749788 15.8946C0.776929 15.7753 0.846132 15.6686 0.945738 15.5925C1.04534 15.5164 1.16927 15.4756 1.29663 15.4768H3.30698C3.33015 15.137 3.37676 14.799 3.44652 14.465C3.71601 13.126 4.3459 11.8778 5.27372 10.8442C5.96467 10.0431 6.84326 9.40991 7.8379 8.99618C7.92948 8.946 8.03414 8.90419 8.13008 8.8582C8.56222 8.68028 9.01032 8.54033 9.46886 8.44009L10.5896 6.13214C10.6351 6.04053 10.7068 5.96319 10.7963 5.90915C10.8859 5.85511 10.9896 5.82659 11.0955 5.82693C11.1344 5.82076 11.1742 5.82076 11.2132 5.82693C11.3374 5.85123 11.4489 5.9163 11.5285 6.01092C11.6081 6.10555 11.6508 6.22382 11.6493 6.34538V8.25613C12.2187 8.29055 12.7828 8.38163 13.3326 8.5279C14.2202 8.76169 15.0585 9.14231 15.8095 9.6526C16.5711 10.1653 17.2299 10.8055 17.7545 11.5424C18.3524 12.3359 18.7753 13.2378 18.9973 14.1932L21.4089 15.272C21.5221 15.3237 21.6141 15.4102 21.6702 15.5177C21.7263 15.6253 21.7433 15.7478 21.7185 15.8657Z"
      fill="#1D1E20"
    />
    <Path
      d="M18.1774 17.0571L16.7296 17.6884C16.4307 18.5202 15.9307 19.2724 15.2705 19.8835C14.6104 20.4946 13.8088 20.9473 12.9313 21.2047C12.7028 21.2729 12.4697 21.326 12.2336 21.3636C11.9455 21.4116 11.6539 21.4381 11.3614 21.443V25.4067L12.7918 22.4799C14.1469 22.1853 15.381 21.5134 16.3392 20.5487C17.2973 19.5839 17.9369 18.3692 18.1774 17.0571Z"
      fill="#F5A865"
    />
    <Path
      d="M4.29689 14.699L5.74469 14.0676C6.12879 12.9906 6.84812 12.0524 7.80567 11.3795C8.76323 10.7067 9.91295 10.3315 11.0998 10.3047V6.34937L9.67381 9.27611C8.32033 9.57217 7.0881 10.2447 6.13159 11.2094C5.17508 12.174 4.53684 13.388 4.29689 14.699Z"
      fill="#F5A865"
    />
    <Path
      d="M18.1077 14.3812C17.7993 13.0863 17.0995 11.9073 16.0958 10.9918C15.0921 10.0763 13.829 9.46499 12.4647 9.2343L13.1276 10.6224C14.2509 10.9907 15.2295 11.6804 15.9313 12.5984C16.6331 13.5165 17.0243 14.6188 17.0524 15.7568H21.1864L18.1077 14.3812Z"
      fill="#F5A865"
    />
    <Path
      d="M10.0183 22.5343L9.35984 21.142C8.70293 20.9284 8.09247 20.601 7.55881 20.1762C6.91218 19.6671 6.38828 19.0297 6.02317 18.308C5.65805 17.5862 5.46039 16.7971 5.4438 15.9951H1.29663L4.34923 17.3623C4.6564 18.6645 5.35862 19.8505 6.36742 20.7708C7.37623 21.6912 8.64653 22.3048 10.0183 22.5343Z"
      fill="#F5A865"
    />
    <Path
      d="M13.5288 13.6704C13.4739 13.5768 13.3942 13.4989 13.2976 13.4445C13.2011 13.3901 13.0912 13.3613 12.9793 13.361H9.25949C9.14735 13.3605 9.03712 13.3889 8.94042 13.4433C8.84372 13.4978 8.76413 13.5762 8.71002 13.6704L7.70266 15.3972C7.63633 15.5084 7.60901 15.6371 7.62471 15.7643C7.64041 15.8915 7.69831 16.0105 7.78988 16.1038L10.655 19.0765C10.7145 19.1374 10.7864 19.1859 10.8663 19.2191C10.9462 19.2523 11.0323 19.2694 11.1194 19.2694C11.2064 19.2694 11.2926 19.2523 11.3725 19.2191C11.4523 19.1859 11.5243 19.1374 11.5838 19.0765L14.4489 16.1038C14.5405 16.0105 14.5984 15.8915 14.6141 15.7643C14.6298 15.6371 14.6024 15.5084 14.5361 15.3972L13.5288 13.6704ZM11.1172 17.772L9.02836 15.6062L9.63016 14.5693H12.6086L13.2104 15.6062L11.1172 17.772Z"
      fill="#F5A865"
    />
  </Svg>
);

export default PyxiLabel;
