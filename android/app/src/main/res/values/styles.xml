<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:textColor">#1F2937</item>

        <!-- Status Bar Customization for Light Theme -->
        <item name="android:statusBarColor">#FCF9ED</item>        <!-- Light background matching app theme -->
        <item name="android:windowLightStatusBar">true</item>        <!-- Makes icons gray/dark for light backgrounds -->

        <!-- Full screen support -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="SplashScreenTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:statusBarColor">#FCF9ED</item>        <!-- Light background matching app theme -->

        <!-- Full screen support for splash -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
</resources>
