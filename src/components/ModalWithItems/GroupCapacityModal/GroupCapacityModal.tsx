import {View, Text, TextInput, Platform, TouchableOpacity} from 'react-native';
import ModalWithButtons from '~components/ModalWithButtons';
import {FC, useState} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  initialCapacity: number;
  onClose: () => void;
  onPress: (value: number) => void;
}

const options = [20, 30, 50, 100];

const GroupCapacityModal: FC<IProps> = ({isVisible, initialCapacity, onClose, onPress}) => {
  const {colors} = useTheme();
  const isAndroid = Platform.OS === 'android';
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const [capacity, setCapacity] = useState<number | ''>(initialCapacity);

  return (
    <ModalWithButtons
      onCloseModal={onClose}
      modalIsVisible={isVisible}
      onPress={() => onPress(typeof capacity === 'number' ? capacity : initialCapacity)}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingTop: 24,
          paddingHorizontal: 46,
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
        }}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          {options.map(option => (
            <TouchableOpacity
              key={option}
              style={{flexDirection: 'row', alignItems: 'center', marginBottom: 12}}
              onPress={() => setCapacity(option)}>
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  borderWidth: 2,
                  borderColor: colors.primary,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 10,
                }}>
                {capacity === option && (
                  <View
                    style={{
                      width: 10,
                      height: 10,
                      borderRadius: 5,
                      backgroundColor: colors.primary,
                    }}
                  />
                )}
              </View>
              <Text>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <TextInput
          style={{
            borderWidth: 1,
            borderColor: colors.gray400,
            borderRadius: 8,
            padding: 10,
            fontSize: 16,
            marginTop: 10,
            color: colors.black,
          }}
          keyboardType="numeric"
          placeholderTextColor={colors.gray400}
          placeholder={t('events.enter_capacity')}
          value={String(capacity)}
          onChangeText={text => {
            const num = parseInt(text, 10);
            if (!isNaN(num) && num > 0) {
              setCapacity(num);
            } else {
              setCapacity('');
            }
          }}
        />
      </View>
    </ModalWithButtons>
  );
};

export default GroupCapacityModal;
