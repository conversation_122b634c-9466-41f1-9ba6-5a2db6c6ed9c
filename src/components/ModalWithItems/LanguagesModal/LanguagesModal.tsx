import {View, Platform, NativeModules} from 'react-native';
import ModalWithButtons from '../../ModalWithButtons';
import {FC, useEffect, useState} from 'react';
import ModalItem from '../ModalItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {useLanguage} from '~containers/Core/navigation/SettingsStack/zustand';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  onClose: () => void;
}

const LanguagesModal: FC<IProps> = ({isVisible, onClose}) => {
  const {colors} = useTheme();
  const isAndroid = Platform.OS === 'android';
  const {bottom} = useSafeAreaInsets();
  const {i18n} = useTranslation();
  const {setLanguage} = useLanguage();
  const selectedLanguage = i18n.language;

  const [data, setData] = useState('');

  const [items, setItems] = useState([
    {title: 'ENG', isActive: selectedLanguage !== 'gr'},
    {title: 'GR', isActive: selectedLanguage === 'gr'},
  ]);

  useEffect(() => {
    setItems([
      {title: 'ENG', isActive: selectedLanguage !== 'gr'},
      {title: 'GR', isActive: selectedLanguage === 'gr'},
    ]);
  }, [selectedLanguage]);

  const handleItemClick = (title: string) => {
    const updatedItems = items.map(item => ({
      ...item,
      isActive: item.title === title,
    }));
    setItems(updatedItems);
    setData(title);
  };

  const handleSubmit = () => {
    const locale = items.find(item => item.isActive)?.title === 'GR' ? 'gr' : 'en';
    i18n.changeLanguage(locale);
    setLanguage(locale);
  };

  return (
    <ModalWithButtons
      onCloseModal={onClose}
      modalIsVisible={isVisible}
      onPress={handleSubmit}
      isLanguage={data}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingTop: 24,
          paddingHorizontal: 46,
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
        }}>
        {items.map((item, index) => (
          <View key={`${index}-language-modal`} style={{width: '100%', marginTop: index === 0 ? 0 : 6, height: '40%'}}>
            <ModalItem flex={1} key={index} item={item} onPress={() => handleItemClick(item.title)} />
          </View>
        ))}
      </View>
    </ModalWithButtons>
  );
};

export default LanguagesModal;
