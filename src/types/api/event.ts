import {Coords, User} from './user';

export interface MatchAvailable {
  matches_available: boolean;
  capacity: number;
  currency: string;
  description: string;
  name: string;
  price: number;
  removed: boolean;
  sold: number;
  discounted_price: number;
  quantity?: number;
  type?: string;
}
export interface Ticket {
  capacity: number;
  currency: string;
  description: string;
  name: string;
  price: number;
  removed: boolean;
  sold: number;
  discounted_price: number;
  quantity?: number;
  matches_available: boolean;
  type?: string;
}
export interface PromoCodes {
  description: string;
  discount_percentage: number;
  minimum_amount: number;
  minimum_tickets: number;
  name: string;
  promo_code_id: string;
}
export interface Event {
  payment_url: string;
  is_paid: boolean;
  repeat?: boolean;
  private: boolean;
  name: string;
  end_date: string;
  start_date: string;
  number_slots: number;
  image_url: string;
  is_exported: boolean;
  is_for_kids: boolean;
  coords: Coords;
  description: string;
  host_photo?: string;
  event_type: string;
  address_name: string;
  subcategories: number[];
  host_id: string;
  event_id: number;
  user_liked: boolean;
  total_attendee_count: number;
  user_event: boolean;
  is_cancelled: boolean;
  likes_count: number;
  confirmedAttendees: {name: string; uid: string}[];
  recurrences: Recurrence[];
  event_group: {
    id: number;
    name: string;
    repeat_rule: Repeatrule;
    start_date: string;
    end_date: string;
  };
  tickets: Ticket[];
  event_group_id: number | null;
  recurrence_events: Array<Event> | null;
  coord_address: string | null;
  promo_codes: PromoCodes[];
}

export interface ShortUrl {
  id: number;
  original_url: string;
  short_url: string;
  created_at: string;
}

export interface OrderDetail {
  order_id: string;
  payment_intent_id: string;
  uid: string;
  event_id: number;
  total_amount: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  order_details: OrderDetailA[];
  event: EventA;
}

export interface OrderDetailA {
  order_id: string;
  ticket_id: string;
  count: number;
  name: string;
  currency: string;
  price: number;
  discounted_price: number;
  description: string;
}

export interface EventA {
  event_id: number;
  name: string;
  start_date: string;
  end_date: string;
  description: string;
  image_url: string;
}

export interface Comment {
  id: number;
  event_id: number;
  host_id: string;
  host_name: string;
  comment: string;
  pin: boolean;
  created_at: string;
  updated_at: string;
  user: UserA;
}

interface UserA {
  id: string;
  name: string;
  photo: string;
  type: string;
}

export interface Recurrence {
  id: number;
  event_id: number;
  subscribed: boolean;
  start_time: string;
  end_time: string;
  start_date: string;
  end_date: string;
  is_cancelled: boolean | null;
}
export interface Repeatrule {
  duration: string;
  repeat_every: number;
  repeat_on: string[];
  repeat_on_same_day: Repeatonsameday[];
  end_date: string;
}
interface Repeatonsameday {
  start_time: string;
  end_time: string;
}

export interface CreateCommentEventRequest {
  event_id: number;
  comment: string;
  pin: boolean;
  parent_id: number | null;
}

export interface DeleteEventRequest {
  event_id: number;
}

export interface DeleteEventResponse {
  details: string;
}

export interface GetEventCategory {
  event_id: number;
  subcategory_id: number;
  subcategory_name: string;
}

export type GetEventCategoriesResponse = GetEventCategory[];

export interface EditEventSubcategory {
  event_id: number;
  ids: number[];
}

export interface UpdateUserStatus {
  event_id: number;
  user_id: string;
  email: string;
}

export interface RemoveAttendeePayload {
  event_id: number;
  user_id: string;
}

export interface RemoveLikePayload {
  eventId: number;
}

export interface PaymentIntentResponse {
  client_secret: string;
  order_id: string;
}

export interface CreatePaymentIntentPayload {
  tickets: Ticket[];
  promo_code_id: string;
  eventId: number;
}

export interface Ticket {
  ticket_id: string;
  count: number;
}

export interface AddLikePayload {
  eventId: number;
}

export type GetEventAttendeesResponse = {
  event_id: number;
  user_id: string;
  status: string;
  user: User;
  name: string;
  email: string;
}[];
