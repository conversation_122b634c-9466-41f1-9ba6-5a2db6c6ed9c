import moment from 'moment-timezone';
import {FC, useEffect, useState} from 'react';
import {TIME_FORMAT} from '../DateTimeToggle/DateTimeToggle';
import {CustomTextInput} from '../CustomTextInput';
import {Text} from 'react-native';
import {useTranslation} from 'react-i18next';

interface IProps {
  time: Date;
  onTimeChange: (value: Date) => void;
}

const TIME_FORMAT_REGEX = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|PM)$/i;

const AndroidTimePicker: FC<IProps> = ({time, onTimeChange}) => {
  const [changeableDate, setChangeableDate] = useState(moment(time).format(TIME_FORMAT));
  const [changeableTimeError, setChangeableTimeError] = useState('');

  const {t} = useTranslation();

  useEffect(() => {
    setChangeableDate(moment(time).format(TIME_FORMAT));
    setChangeableTimeError('');
  }, [time]);

  const handleChange = (value: string) => {
    setChangeableDate(value);
    const valueMoment = moment(value, TIME_FORMAT);
    const isDateFormat = TIME_FORMAT_REGEX.test(value);

    // we check isDateFormat if the string has format as we expect
    // we check valueMoment.isValid() to be sure that date is correct
    if (isDateFormat && valueMoment.isValid()) {
      onTimeChange(moment(value, TIME_FORMAT).toDate());
      setChangeableTimeError('');
    } else {
      setChangeableTimeError(t('generic.invalid_time_format'));
    }
  };

  return (
    <>
      <CustomTextInput
        value={changeableDate}
        errorText={changeableTimeError}
        onChangeValue={handleChange}
        placeholder="9:00 PM"
      />
      <Text style={{fontSize: 14}}>{t('generic.enter_time_format')}</Text>
    </>
  );
};

export default AndroidTimePicker;
