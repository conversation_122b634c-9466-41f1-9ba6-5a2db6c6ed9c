import {Text, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal/dist/modal';
import createStyles from './styles';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  children: React.ReactNode;
  backgroundColor?: string;
  onCloseModal: () => void;
  modalIsVisible?: boolean;
  onPress: () => void;
  isLanguage?: string;
}

const ModalWithButtons = ({
  children,
  onCloseModal,
  onPress,
  modalIsVisible = false,
  backgroundColor,
  isLanguage,
}: IProps) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {t} = useTranslation();
  const defaultBackgroundColor = backgroundColor || colors.white;
  return (
    <Modal
      avoidKeyboard={true}
      propagateSwipe={false}
      isVisible={modalIsVisible}
      swipeDirection={[]}
      style={[styles.modal]}
      onBackdropPress={onCloseModal}>
      <SafeAreaView edges={['left', 'right']} style={styles.safeAreaContainer}>
        <View style={[styles.modalContent, {height: '30%', backgroundColor: defaultBackgroundColor, paddingTop: 12}]}>
          <View
            style={{
              width: '100%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingHorizontal: 16,
            }}>
            <TouchableOpacity onPress={onCloseModal}>
              <Text style={styles.buttonText}>
                {isLanguage === 'ENG' ? 'Close' : isLanguage === 'GR' ? 'Κλείσιμο' : t('generic.close')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                onPress();
                onCloseModal();
              }}>
              <Text style={styles.buttonText}>
                {isLanguage === 'ENG' ? 'Done' : isLanguage === 'GR' ? 'Ολοκληρώθηκε' : t('generic.done')}
              </Text>
            </TouchableOpacity>
          </View>
          {children}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default ModalWithButtons;
