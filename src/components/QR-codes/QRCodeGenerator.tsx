import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, Modal} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import {Image} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const QRCodeGenerator = ({user, event, isBigQr, qrCode, onQrPress}: any) => {
  const {colors} = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  // console.log('user', user);
  // console.log('event', event?.event_id);
  const [qrData, setQrData] = useState(`${user?.uid}###${event?.event_id}###0`);

  useEffect(() => {
    if (qrCode && qrCode.qr_code) {
      setQrData(qrCode.qr_code);
    }
  }, [qrCode]);

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    const options: any = {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    };

    return date.toLocaleString('en-GB', options);
  };
  // console.log('name', `${user?.first_name}${user?.last_name}`);
  return (
    <View
      style={
        isBigQr
          ? {
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 10,
            }
          : {
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              maxHeight: 70,
              width: 70,
              backgroundColor: colors.background,
              margin: 10,
              borderRadius: 10,
            }
      }>
      <TouchableOpacity
        onPress={() => {
          if (qrCode && qrCode.qr_code) {
            setModalVisible(true);
          } else {
            onQrPress();
          }
        }}>
        <QRCode value={qrData} size={isBigQr ? 200 : 55} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          onPress={() => setModalVisible(false)}
          style={{flex: 1, alignItems: 'center', backgroundColor: colors.overlayBackground, justifyContent: 'center'}}>
          <View
            style={{width: 300, backgroundColor: colors.white, borderRadius: 10, padding: 20, alignItems: 'center'}}>
            <TouchableOpacity style={{position: 'absolute', top: 10, right: 10}} onPress={() => setModalVisible(false)}>
              <Text style={{fontSize: 18, color: colors.black}}>✕</Text>
            </TouchableOpacity>
            <Text style={{fontSize: 20, fontWeight: 'bold', marginBottom: 10}}>{event?.name}</Text>
            <Text
              style={{fontSize: 14, fontWeight: 'bold', color: colors.black, textAlign: 'center', marginBottom: 20}}>
              {`${formatDate(event?.start_date)}\n ${event?.address_name} `}
            </Text>
            <QRCode value={qrData} size={170} />
            <TouchableOpacity onPress={() => console.log('pressed')}>
              <Image
                source={require('~assets/images/addToAppleWallet.png')}
                style={{
                  height: 50, // встановіть висоту для зображення
                  resizeMode: 'contain', // встановіть режим зміни розмірів зображення
                  marginTop: 30,
                }}
              />
            </TouchableOpacity>
            <Text style={{fontSize: 12, color: colors.gray400, textAlign: 'center', marginTop: 10}}>
              Show this to the cashier to validate your entrance to the event.
            </Text>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default QRCodeGenerator;
