import React, {useEffect, useRef} from 'react';
import {Dimensions, ViewStyle} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
  cancelAnimation,
} from 'react-native-reanimated';
import {PanGestureHandler, PanGestureHandlerGestureEvent, State} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';
import {haptics} from '~utils/haptics';
import {useAnimationCleanup, safeRunOnJS} from '~utils/viewCleanup';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface SwipeBackGestureProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
  style?: ViewStyle;
}

export const SwipeBackGesture: React.FC<SwipeBackGestureProps> = ({
  children,
  enabled = true,
  threshold = SCREEN_WIDTH * 0.3,
  style,
}) => {
  const navigation = useNavigation();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(translateX, opacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
    opacity: opacity.value,
  }));

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      if (enabled) {
        runOnJS(haptics.light)();
      }
    },
    onActive: event => {
      if (!enabled) {
        return;
      }

      // Only allow right swipe (positive translation)
      if (event.translationX > 0) {
        translateX.value = event.translationX;

        // Fade out as we swipe
        opacity.value = interpolate(event.translationX, [0, threshold], [1, 0.7], Extrapolate.CLAMP);

        // Haptic feedback at threshold
        if (event.translationX > threshold) {
          runOnJS(haptics.medium)();
        }
      }
    },
    onEnd: event => {
      if (!enabled) {
        return;
      }

      const shouldGoBack = event.translationX > threshold && event.velocityX > 0;

      if (shouldGoBack) {
        // Animate out and navigate back
        translateX.value = withTiming(SCREEN_WIDTH, {duration: 200});
        opacity.value = withTiming(0, {duration: 200});

        runOnJS(
          safeRunOnJS(isMountedRef, () => {
            haptics.success();
            navigation.goBack();
          }),
        )();
      } else {
        // Spring back to original position
        translateX.value = withSpring(0, {damping: 15, stiffness: 300});
        opacity.value = withTiming(1, {duration: 200});
      }
    },
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler} enabled={enabled}>
      <Animated.View style={[{flex: 1}, style, animatedStyle]}>{children}</Animated.View>
    </PanGestureHandler>
  );
};

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => void;
  refreshing?: boolean;
  threshold?: number;
  style?: ViewStyle;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  refreshing = false,
  threshold = 80,
  style,
}) => {
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(translateY, scale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateY: translateY.value}, {scale: scale.value}],
  }));

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      runOnJS(haptics.light)();
    },
    onActive: event => {
      // Only allow downward pull (positive translation)
      if (event.translationY > 0) {
        translateY.value = event.translationY * 0.5; // Damping effect

        // Scale effect for visual feedback
        scale.value = interpolate(event.translationY, [0, threshold], [1, 1.05], Extrapolate.CLAMP);

        // Haptic feedback at threshold
        if (event.translationY > threshold) {
          runOnJS(haptics.medium)();
        }
      }
    },
    onEnd: event => {
      const shouldRefresh = event.translationY > threshold && event.velocityY > 0;

      if (shouldRefresh && !refreshing) {
        runOnJS(
          safeRunOnJS(isMountedRef, () => {
            haptics.success();
            onRefresh();
          }),
        )();
      }

      // Spring back to original position
      translateY.value = withSpring(0, {damping: 15, stiffness: 300});
      scale.value = withSpring(1, {damping: 15, stiffness: 300});
    },
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[{flex: 1}, style, animatedStyle]}>{children}</Animated.View>
    </PanGestureHandler>
  );
};

interface SwipeToActionProps {
  children: React.ReactNode;
  leftAction?: {
    onAction: () => void;
    color: string;
    icon?: React.ReactNode;
    threshold?: number;
  };
  rightAction?: {
    onAction: () => void;
    color: string;
    icon?: React.ReactNode;
    threshold?: number;
  };
  style?: ViewStyle;
}

export const SwipeToAction: React.FC<SwipeToActionProps> = ({children, leftAction, rightAction, style}) => {
  const translateX = useSharedValue(0);
  const leftActionOpacity = useSharedValue(0);
  const rightActionOpacity = useSharedValue(0);
  const isMountedRef = useAnimationCleanup(translateX, leftActionOpacity, rightActionOpacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
  }));

  const leftActionStyle = useAnimatedStyle(() => ({
    opacity: leftActionOpacity.value,
  }));

  const rightActionStyle = useAnimatedStyle(() => ({
    opacity: rightActionOpacity.value,
  }));

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      runOnJS(haptics.light)();
    },
    onActive: event => {
      translateX.value = event.translationX;

      // Show left action
      if (event.translationX > 0 && leftAction) {
        leftActionOpacity.value = interpolate(
          event.translationX,
          [0, leftAction.threshold || 80],
          [0, 1],
          Extrapolate.CLAMP,
        );
        rightActionOpacity.value = 0;

        if (event.translationX > (leftAction.threshold || 80)) {
          runOnJS(haptics.medium)();
        }
      }
      // Show right action
      else if (event.translationX < 0 && rightAction) {
        rightActionOpacity.value = interpolate(
          Math.abs(event.translationX),
          [0, rightAction.threshold || 80],
          [0, 1],
          Extrapolate.CLAMP,
        );
        leftActionOpacity.value = 0;

        if (Math.abs(event.translationX) > (rightAction.threshold || 80)) {
          runOnJS(haptics.medium)();
        }
      }
    },
    onEnd: event => {
      const leftThreshold = leftAction?.threshold || 80;
      const rightThreshold = rightAction?.threshold || 80;

      const shouldTriggerLeft = event.translationX > leftThreshold && leftAction;
      const shouldTriggerRight = event.translationX < -rightThreshold && rightAction;

      if (shouldTriggerLeft) {
        runOnJS(
          safeRunOnJS(isMountedRef, () => {
            haptics.success();
            leftAction.onAction();
          }),
        )();
      } else if (shouldTriggerRight) {
        runOnJS(
          safeRunOnJS(isMountedRef, () => {
            haptics.success();
            rightAction.onAction();
          }),
        )();
      }

      // Reset position
      translateX.value = withSpring(0, {damping: 15, stiffness: 300});
      leftActionOpacity.value = withTiming(0, {duration: 200});
      rightActionOpacity.value = withTiming(0, {duration: 200});
    },
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[{position: 'relative'}, style]}>
        {/* Left Action Background */}
        {leftAction && (
          <Animated.View
            style={[
              {
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: 100,
                backgroundColor: leftAction.color,
                justifyContent: 'center',
                alignItems: 'center',
              },
              leftActionStyle,
            ]}>
            {leftAction.icon}
          </Animated.View>
        )}

        {/* Right Action Background */}
        {rightAction && (
          <Animated.View
            style={[
              {
                position: 'absolute',
                right: 0,
                top: 0,
                bottom: 0,
                width: 100,
                backgroundColor: rightAction.color,
                justifyContent: 'center',
                alignItems: 'center',
              },
              rightActionStyle,
            ]}>
            {rightAction.icon}
          </Animated.View>
        )}

        {/* Main Content */}
        <Animated.View style={animatedStyle}>{children}</Animated.View>
      </Animated.View>
    </PanGestureHandler>
  );
};

export default {
  SwipeBackGesture,
  PullToRefresh,
  SwipeToAction,
};
