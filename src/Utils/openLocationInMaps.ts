import {Platform, Linking} from 'react-native';

export const openLocationInMaps = ({lat, long, pointLabel}: {lat: number; long: number; pointLabel: string}) => {
  const scheme = Platform.select({ios: 'maps://0,0?q=', android: 'geo:0,0?q='});
  const latLng = `${lat},${long}`;
  const url = Platform.select({
    ios: `${scheme}${pointLabel}@${latLng}`,
    android: `${scheme}${latLng}(${pointLabel})`,
  });

  Linking.openURL(url || '');
};
