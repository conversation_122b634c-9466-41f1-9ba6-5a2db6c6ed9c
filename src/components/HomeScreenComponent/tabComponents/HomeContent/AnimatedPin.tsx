import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';
import ModernEventPin from '~assets/icons/ModernEventPin';
import {haptics} from '~utils/haptics';

interface AnimatedPinProps {
  isSelected: boolean;
  eventType: string;
}

const AnimatedPin: React.FC<AnimatedPinProps> = ({isSelected, eventType}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    if (isSelected) {
      // Provide haptic feedback when pin is selected
      haptics.mapPinSelect();

      // Apple Maps-style bounce animation on selection
      Animated.sequence([
        // Initial bounce up
        Animated.timing(bounceAnim, {
          toValue: -8,
          duration: 200,
          useNativeDriver: true,
        }),
        // Bounce back down with slight overshoot
        Animated.spring(bounceAnim, {
          toValue: 0,
          tension: 300,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Start pulsing animation with improved smoothness
      pulseAnim.current = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.15, // Slightly reduced for more subtle effect
            duration: 1000, // Slower, more elegant pulsing
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1.05, // Don't go all the way back to 1 to maintain selected state
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      );
      pulseAnim.current.start();
    } else {
      // Stop pulsing and return to default scale
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }

      // Smooth deselection animation
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }

    // Cleanup on unmount or when isSelected changes
    return () => {
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }
    };
  }, [isSelected, scaleAnim, bounceAnim]);

  // Map event types to our modern pin types
  const getEventPinType = (type: string): 'business' | 'influencer' | 'community' | 'pyxi_select' => {
    switch (type) {
      case 'business':
        return 'business';
      case 'influencer':
        return 'influencer';
      case 'community':
        return 'community';
      case 'pyxi_select':
        return 'pyxi_select';
      default:
        return 'community';
    }
  };

  return (
    <Animated.View
      style={[
        styles.iconContainer,
        {
          transform: [{scale: scaleAnim}, {translateY: bounceAnim}],
          // Increased size to accommodate 1.15x scaling and bounce animation
          height: 110,
          width: 110,
          // Allow content to overflow the container bounds
          overflow: 'visible',
        },
      ]}>
      <ModernEventPin
        eventType={getEventPinType(eventType)}
        size={50} // Increased from 40 to 50
        isSelected={false} // Pass false to prevent double scaling
        disableInternalScaling={true} // New prop to disable internal scaling
      />
    </Animated.View>
  );
};

export default AnimatedPin;

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // Ensure content can overflow for smooth animations
    overflow: 'visible',
    // Add z-index to ensure pin appears above other elements during animation
    zIndex: 1000,
  },
});
