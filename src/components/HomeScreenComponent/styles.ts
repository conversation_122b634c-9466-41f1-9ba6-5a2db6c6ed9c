import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    selectIcon: {
      width: 30,
      height: 30,
      borderRadius: 30,
      marginLeft: 5,
    },
    neighbourHoodEvent: {
      backgroundColor: colors.eventInfluencer + '99', // Keep this as is for event type
      padding: 6,
      borderRadius: 15,
      position: 'absolute',
      top: 43,
      left: 10,
      zIndex: 1,
    },
    redDotImg: {
      position: 'absolute',
      right: 10,
      bottom: 10,
      width: 26,
      height: 26,
      tintColor: colors.error,
    },
    background: {
      backgroundColor: colors.surface,
    },
    wrapper: {
      paddingHorizontal: 16,
    },
    headline: {
      fontSize: 32,
      color: colors.textPrimary,
      textAlign: 'center',
    },
    image: {
      position: 'absolute',
      borderRadius: 10,
      width: '100%',
      height: 185, // Збільшили висоту
    },
    imageWrapper: {
      width: '100%',
      height: 185, // Збільшили висоту
      justifyContent: 'flex-end',
    },
    label: {
      fontSize: 20,
      color: colors.textPrimary,
      marginBottom: 8,
    },
    description: {
      color: colors.gray400,
      width: 170,
    },
    container: {
      flexDirection: 'column',
      marginVertical: 10,
    },
    margin: {
      marginBottom: 32,
    },
    textContainer: {
      flex: 1,
      borderRadius: 8,
      padding: 10,
      justifyContent: 'flex-end',
    },
    textRowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    contentContainer: {
      flexDirection: 'column', // Змінили напрямок на колонку
      backgroundColor: colors.overlayBackground,
      flex: 1,
      borderRadius: 8,
      alignItems: 'flex-start', // Вирівняли на початок
      padding: 3,
    },
    name: {
      color: colors.white,
      fontWeight: '700',
      fontSize: 24, // Збільшили розмір шрифту
      lineHeight: 24,
    },
    date: {
      color: colors.white,
      fontSize: 12,
      lineHeight: 15,
      // fontWeight: '500',
      flex: 1,
      // fontWeight: 'bold',
    },
    dot: {
      width: 4.5,
      height: 4.5,
      borderRadius: 100,
      backgroundColor: colors.white + 'CC',
      marginHorizontal: 4,
    },
    addressName: {
      color: colors.warning,
      fontSize: 14,
      lineHeight: 15,
      fontWeight: '500',
      flex: 1,
      width: '70%',
    },
    bottomTextContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      marginTop: 4,
      paddingRight: 40, // Add padding to avoid overlap with live icon
    },
    dateTextBg: {
      backgroundColor: colors.overlayBackground,
      padding: 6,
      borderRadius: 15,
    },
    dateRangeContainer: {
      zIndex: 1,
      position: 'absolute',
      top: 10,
      left: 10,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateRangeText: {
      zIndex: 2,
      color: colors.white,
      fontSize: 13,
      fontWeight: 'bold',
    },
    iconContainer: {
      zIndex: 100,
      backgroundColor: colors.overlayBackground,
      width: 35,
      height: 35,
      borderRadius: 35,
      position: 'absolute',
      top: 10,
      right: 10,
      flexDirection: 'row',
      gap: 10, // Відступ між іконками
    },
    paidIcon: {
      width: 24,
      height: 24,
      position: 'absolute',
      top: 16,
      right: 55,
      zIndex: 1,
    },
    likeIcon: {
      zIndex: 2,
      width: 10,
      height: 10,
    },
    iconVerifiedContainer: {
      position: 'absolute',
      top: 10,
      right: 10,
      flexDirection: 'row',
      alignItems: 'center',
    },
    verifiedIcon: {
      width: 35,
      height: 35,
    },
    goingIconContainer: {
      zIndex: 1,
      position: 'absolute',
      top: 45,
      left: 10,
      backgroundColor: colors.overlayBackground,
      height: 30,
      borderRadius: 15,
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: 8,
    },
    going: {
      color: colors.white,
      fontSize: 13,
      lineHeight: 15,
      fontWeight: 'bold',
      paddingEnd: 5,
    },
  });

export default createStyles;
