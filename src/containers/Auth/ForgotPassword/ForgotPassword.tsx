import {useIsFocused, useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, StatusBar, Text, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {PyxiLabel, ChevronIcon} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernCard from '~components/ModernCard/ModernCard';
import {FadeIn, SlideIn} from '~components/MicroInteractions/MicroInteractions';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';
import {useLanguage} from '~containers/Core/navigation/SettingsStack/zustand';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {FadeInDown} from 'react-native-reanimated';
const validationSchema = yup.object().shape({
  email: yup
    .string()
    .email('Enter a valid email')
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter a valid email')
    .required('Email is required'),
});

const ForgotPasswordView = () => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {t} = useTranslation();
  const {width} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const [isLoading, setIsLoading] = useState(false);
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);
  const isFocused = useIsFocused();
  const {language} = useLanguage();
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const isAndroid = Platform.OS === 'android';

  const paddingBottom = isAndroid ? bottom + 20 : bottom;
  const {height} = useWindowDimensions();

  useEffect(() => {
    logScreenView('Forgot Password', 'ForgotPassword');
  }, []);

  return (
    <SafeAreaView style={styles.flex} edges={['right', 'left']}>
      <ScrollView showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
        <KeyboardAwareScrollView
          style={[styles.flex, {minHeight: height - (StatusBar.currentHeight || 0) - 20}]}
          bounces={false}
          enableOnAndroid={true}
          extraHeight={Platform.OS === 'android' ? 50 : 0}
          extraScrollHeight={50}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always">
          <View style={[styles.flex, {minHeight: height - (StatusBar.currentHeight || 0) - 20}]}>
            <View style={[styles.fullWidth, {height: width * 0.631, position: 'relative'}]}>
              <FastImage
                source={require('../../../assets/images/SignInImage.png')}
                style={{width: '100%', height: '100%'}}
                resizeMode="cover"
              />
              <TouchableOpacity
                style={{position: 'absolute', top: 16, right: 20}}
                onPress={() => setIsLanguagesModalVisible(true)}>
                <Text
                  style={{
                    color: colors.white,
                    fontSize: 18,
                    fontWeight: '600',
                    backgroundColor: colors.overlayBackground,
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    borderRadius: 4,
                  }}>
                  {language.toUpperCase()}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.iconContainer}>
              <PyxiLabel />
              <View style={{marginTop: 20, width: '100%'}}>
                <Text style={{fontSize: 20, fontWeight: '500', marginBottom: 10}}>Forgot Password</Text>
              </View>
            </View>

            <Formik
              initialValues={{email: ''}}
              onSubmit={async values => {
                setIsLoading(true);
                try {
                  const response = await FirebaseAuth.sendChangePasswordLink(values.email.trim());
                  if (response) {
                    navigation.goBack();
                  }
                } catch (error) {
                  console.error('Password reset error:', error);
                } finally {
                  setIsLoading(false);
                }
              }}
              validationSchema={validationSchema}>
              {formikProps => (
                <View style={styles.formContainer}>
                  <CustomTextInput
                    description={t('signin.email')}
                    onChangeValue={value => {
                      formikProps.handleChange('email')(value);
                    }}
                    value={formikProps.values.email}
                    errorText={formikProps.touched.email && formikProps.errors.email}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />

                  <ModernButton
                    onPress={() => formikProps.handleSubmit()}
                    disabled={!formikProps.isValid || !formikProps.values.email.trim()}
                    title={t('signin.send_message')}
                    loading={isLoading}
                    variant="primary"
                    size="lg"
                    fullWidth
                    style={styles.loginButton}
                  />
                </View>
              )}
            </Formik>
            {/* <>
            <View style={[styles.fullWidth, {marginBottom: 24}]}>
              <View style={[styles.divider]} />
              <Text style={styles.textLight}>{t('signin.sign_with_google')}</Text>
              <View style={[styles.divider]} />
            </View>
            <View style={styles.socialButtonsContainer}>
              <SocialButtons />
            </View>
          </> */}
            <View
              style={[
                styles.bottomContainer,
                {
                  paddingBottom,
                },
              ]}>
              <Text style={styles.bottomText}>{t('signin.existing_account')}</Text>
              <TouchableOpacity
                style={styles.registerButton}
                onPress={() => {
                  navigation.goBack();
                }}>
                <Text style={styles.registerText}>{t('signin.login')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </ScrollView>
      <LanguagesModal isVisible={isLanguagesModalVisible} onClose={() => setIsLanguagesModalVisible(false)} />
    </SafeAreaView>
  );
};

export default ForgotPasswordView;
