// Light theme colors
const lightColors = {
  // Base colors
  white: '#FFFFFF',
  black: '#000000',

  // Primary brand colors
  primary: '#FF9500',
  primaryLight: '#FFB84D',
  primaryDark: '#E6850E',

  // Secondary colors
  secondary: '#4A48AD',
  secondaryLight: '#6B69C7',
  secondaryDark: '#3A3894',

  // Neutral colors
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',

  // Semantic colors
  success: '#10B981',
  successLight: '#34D399',
  successDark: '#059669',

  warning: '#F59E0B',
  warningLight: '#FBBF24',
  warningDark: '#D97706',

  error: '#EF4444',
  errorLight: '#F87171',
  errorDark: '#DC2626',

  info: '#3B82F6',
  infoLight: '#60A5FA',
  infoDark: '#2563EB',

  // Background colors
  background: '#FCF9ED',
  backgroundSecondary: '#F8F9FA',
  surface: '#FFFFFF',
  surfaceSecondary: '#F8F9FA',

  // Text colors
  textPrimary: '#1F2937',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',

  // Border colors
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  borderDark: '#D1D5DB',

  // Event type colors
  eventBusiness: '#4A48AD',
  eventInfluencer: '#F5A865',
  eventCommunity: '#10B981',
  eventPyxiSelect: '#FF6B6B',

  // Status colors
  statusGray: '#8E8E93',
  statusGreen: '#34C759',
  statusBlue: '#007AFF',
  statusPurple: '#2E2EAB',
  statusOrange: '#FF9500',

  // UI specific colors
  placeholderText: '#8E8E93',
  separatorLine: '#E0E0E0',
  cardBackground: '#FFFFFF',
  modalBackground: '#FFFFFF',
  overlayBackground: 'rgba(0, 0, 0, 0.5)',

  // Input colors
  inputBackground: '#F2F2F6',
  inputBorder: '#E5E5EA',
  inputText: '#1D1E20',
  inputPlaceholder: '#8E8E93',

  // Button colors
  buttonPrimary: '#FF9500',
  buttonSecondary: '#4A48AD',
  buttonDisabled: '#F1F1F3',
  buttonText: '#FFFFFF',
  buttonTextDisabled: '#8E8E93',

  // Legacy colors (for backward compatibility)
  gray: '#808080',
  lightGray: '#D3D3D3',
  red: '#EF4444',
} as const;

// Dark theme colors
const darkColors = {
  // Base colors
  white: '#000000',
  black: '#FFFFFF',

  // Primary brand colors
  primary: '#FFB84D',
  primaryLight: '#FFC970',
  primaryDark: '#FF9500',

  // Secondary colors
  secondary: '#6B69C7',
  secondaryLight: '#8B89D4',
  secondaryDark: '#4A48AD',

  // Neutral colors
  gray50: '#111827',
  gray100: '#1F2937',
  gray200: '#374151',
  gray300: '#4B5563',
  gray400: '#6B7280',
  gray500: '#9CA3AF',
  gray600: '#D1D5DB',
  gray700: '#E5E7EB',
  gray800: '#F3F4F6',
  gray900: '#F9FAFB',

  // Semantic colors
  success: '#34D399',
  successLight: '#6EE7B7',
  successDark: '#10B981',

  warning: '#FBBF24',
  warningLight: '#FCD34D',
  warningDark: '#F59E0B',

  error: '#F87171',
  errorLight: '#FCA5A5',
  errorDark: '#EF4444',

  info: '#60A5FA',
  infoLight: '#93C5FD',
  infoDark: '#3B82F6',

  // Background colors
  background: '#0F0F0F',
  backgroundSecondary: '#1A1A1A',
  surface: '#1F1F1F',
  surfaceSecondary: '#2A2A2A',

  // Text colors
  textPrimary: '#F9FAFB',
  textSecondary: '#D1D5DB',
  textTertiary: '#9CA3AF',
  textInverse: '#1F2937',

  // Border colors
  border: '#374151',
  borderLight: '#4B5563',
  borderDark: '#1F2937',

  // Event type colors
  eventBusiness: '#6B69C7',
  eventInfluencer: '#FFC970',
  eventCommunity: '#34D399',
  eventPyxiSelect: '#FF8A8A',

  // Status colors
  statusGray: '#8E8E93',
  statusGreen: '#34D399',
  statusBlue: '#60A5FA',
  statusPurple: '#6B69C7',
  statusOrange: '#FFB84D',

  // UI specific colors
  placeholderText: '#8E8E93',
  separatorLine: '#374151',
  cardBackground: '#1F1F1F',
  modalBackground: '#1F1F1F',
  overlayBackground: 'rgba(0, 0, 0, 0.7)',

  // Input colors
  inputBackground: '#2A2A2A',
  inputBorder: '#374151',
  inputText: '#F9FAFB',
  inputPlaceholder: '#8E8E93',

  // Button colors
  buttonPrimary: '#FFB84D',
  buttonSecondary: '#6B69C7',
  buttonDisabled: '#374151',
  buttonText: '#000000',
  buttonTextDisabled: '#8E8E93',

  // Legacy colors (for backward compatibility)
  gray: '#9CA3AF',
  lightGray: '#4B5563',
  red: '#F87171',
} as const;

// Theme management
let isDarkMode = false;

export const setDarkMode = (enabled: boolean) => {
  isDarkMode = enabled;
};

export const getDarkMode = () => isDarkMode;

export const colors = new Proxy(lightColors, {
  get(target, prop) {
    const colorSet = isDarkMode ? darkColors : lightColors;
    return colorSet[prop as keyof typeof colorSet];
  },
});

export const lightTheme = lightColors;
export const darkTheme = darkColors;

export type ColorKey = keyof typeof colors;
