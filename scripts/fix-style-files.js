#!/usr/bin/env node

/**
 * Script to fix style files that incorrectly received useTheme hooks
 * These are .ts files that are not React components
 */

const fs = require('fs');
const path = require('path');

// Style files that need fixing
const STYLE_FILES = [
  'src/components/Chat/ChatRow/styles.ts',
  'src/components/CreateEvent/CreateEventDateTime/styles.ts',
  'src/components/CustomFlatList/styles.ts',
  'src/components/CustomModal/styles.ts',
  'src/components/CustomTextInput/styles.ts',
  'src/components/DateTimeModal/styles.ts',
  'src/components/DateTimePickerModal/styles.ts',
  'src/components/EventsHeader/styles.ts',
  'src/components/HeaderDropdown/styles.ts',
  'src/components/HomeScreenComponent/styles.ts',
  'src/components/HomeScreenComponent/tabComponents/components/HomeBusinessHeader/styles.ts',
  'src/components/JoinModalWithButtons/styles.ts',
  'src/components/Matching/AnimatedLoadingModal/styles.ts',
  'src/components/Matching/PersonItem/styles.ts',
  'src/components/ModalWithButtons/styles.ts',
  'src/components/ModalWithItems/ModalWithJoin/styles.ts',
  'src/components/RadiusModal/styles.ts',
  'src/components/Settings/OptionItem/styles.ts',
  'src/components/Settings/ProfileImageComponent/styles.tsx',
  'src/components/SubcategoriesLists/styles.ts',
  'src/containers/Auth/styles.ts',
  'src/containers/Event/EditEvent/EditSubcategory/styles.ts',
  'src/containers/Event/EditEvent/styles.ts',
  'src/containers/Event/EventDetails/PendingAttendeesScreen/styles.ts',
  'src/containers/Event/EventDetails/styles.ts',
  'src/containers/Event/Matching/MatchingUsersScreen/styles.ts',
  'src/containers/Onboarding/AccountType/styles.ts',
  'src/containers/Onboarding/BusinessInfo/styles.ts',
  'src/containers/Onboarding/Children/styles.ts',
  'src/containers/Onboarding/Group/styles.ts',
  'src/containers/Settings/ChangePassword/styles.ts',
  'src/containers/Settings/EditSubcategories/styles.ts',
  'src/containers/Settings/HelpCenter/styles.ts',
  'src/containers/Settings/PasswordForDeleteAccount/styles.ts',
  'src/containers/Settings/PurchaseHistory/styles.ts',
  'src/containers/Settings/SettingsView/styles.ts',
];

function fixStyleFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Remove useTheme import if it exists
  const themeImportRegex = /import\s+{[^}]*useTheme[^}]*}\s+from\s+['"]~contexts\/ThemeContext['"];?\n?/g;
  if (content.match(themeImportRegex)) {
    content = content.replace(themeImportRegex, '');
    hasChanges = true;
  }

  // Remove standalone useTheme hook calls
  const themeHookRegex = /\s*const\s+{colors}\s*=\s*useTheme\(\);\n?/g;
  if (content.match(themeHookRegex)) {
    content = content.replace(themeHookRegex, '');
    hasChanges = true;
  }

  // Replace colors.* with hardcoded values or import from colors file
  if (content.includes('colors.')) {
    // Add import for colors if not present
    if (!content.includes('~constants/colors')) {
      const importMatch = content.match(/import[^;]+;/);
      if (importMatch) {
        const insertIndex = content.indexOf(importMatch[0]) + importMatch[0].length;
        content =
          content.slice(0, insertIndex) +
          "\nimport {lightTheme as colors} from '~constants/colors';" +
          content.slice(insertIndex);
        hasChanges = true;
      }
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed style file: ${filePath}`);
    return true;
  }

  return false;
}

// Main execution
console.log('🎨 Fixing style files...\n');

let fixedCount = 0;
for (const filePath of STYLE_FILES) {
  try {
    if (fixStyleFile(filePath)) {
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

console.log(`\n✨ Fixed ${fixedCount} style files!`);
