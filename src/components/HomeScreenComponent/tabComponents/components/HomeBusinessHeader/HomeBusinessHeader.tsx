import {TouchableOpacity, View} from 'react-native';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import auth from '@react-native-firebase/auth';
import getStyles from './styles';
import Animated, {FadeInLeft, FadeInRight} from 'react-native-reanimated';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';

const HomeBusinessHeader = () => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const {data} = useGetBusinessAccount(auth().currentUser!.uid);
  const navigation = useNavigation<NavigationProps>();

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <Animated.Text entering={FadeInLeft.delay(340)} style={styles.titleText} numberOfLines={1}>
          {data?.name}
        </Animated.Text>
        <Animated.Text entering={FadeInLeft.delay(340)} style={styles.descriptionText} numberOfLines={2}>
          {data?.description}
        </Animated.Text>
      </View>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate(SCREENS.PERSONAL_INFO, {
            user: {
              tag: 'main-screen-photo-tag',
              source: data?.photo || '',
              name: data?.name,
              description: data?.description,
              user_id: data?.uid,
            },
            isMain: true,
          });
        }}>
        <Animated.Image
          style={styles.image}
          sharedTransitionTag="main-screen-photo-tag"
          resizeMode="cover"
          source={{
            uri: data?.photo,
          }}
          entering={FadeInRight.delay(340)}
        />
      </TouchableOpacity>
    </View>
  );
};

export default HomeBusinessHeader;
