import moment from 'moment-timezone';
import {Event, Recurrence} from '~types/api/event';

export const formatDateTime = (dateString: Date | string) => {
  const date = moment(dateString);

  const currentDate = moment();

  if (date.isSame(currentDate, 'day')) {
    const formattedTime = date.format('hh:mm a');
    return formattedTime;
  } else {
    const formattedDate = date.format('DD/MM/YY');
    return formattedDate;
  }
};

export const getTimestamp = (messages: any) => {
  if (!Array.isArray(messages) || messages.length === 0) {
    return 0;
  }
  const lastKey = messages.length - 1;
  return messages[lastKey]?.timestamp ? moment(messages[lastKey]?.timestamp).valueOf() : 0;
};

export function groupByDate(array: Event[]) {
  if (!array) {
    return [];
  }
  const groups = array.reduce((groups: any, game: any) => {
    const date = game.start_date.split('T')[0];
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(game);
    return groups;
  }, {});

  const groupArrays = groups
    ? Object.keys(groups).map(date => {
        return {
          date,
          times: groups[date],
        };
      })
    : [];
  return groupArrays;
}

export const LONG_DATE_FORMAT = 'h:mm A, DD.MM.YY';
