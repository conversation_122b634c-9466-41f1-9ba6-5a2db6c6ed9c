import {Platform} from 'react-native';

// ============================================================================
// BUNDLE OPTIMIZATION & CODE SPLITTING
// ============================================================================

/**
 * Dynamic import wrapper with error handling and retries
 */
export const dynamicImport = async <T>(
  importFn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000,
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      return await importFn();
    } catch (error) {
      lastError = error as Error;

      if (attempt < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)));
      }
    }
  }

  throw lastError!;
};

/**
 * Platform-specific code splitting
 */
export const createPlatformSplit = <T>(config: {
  ios?: () => Promise<{default: T}>;
  android?: () => Promise<{default: T}>;
  default: () => Promise<{default: T}>;
}) => {
  const importFn = Platform.select({
    ios: config.ios || config.default,
    android: config.android || config.default,
    default: config.default,
  });

  return () => dynamicImport(importFn);
};

/**
 * Feature-based code splitting
 */
export const createFeatureSplit = <T>(
  featureFlag: string,
  enabledImport: () => Promise<{default: T}>,
  disabledImport?: () => Promise<{default: T}>,
) => {
  return async () => {
    const isEnabled = await checkFeatureFlag(featureFlag);

    if (isEnabled) {
      return dynamicImport(enabledImport);
    } else if (disabledImport) {
      return dynamicImport(disabledImport);
    } else {
      throw new Error(`Feature ${featureFlag} is disabled and no fallback provided`);
    }
  };
};

/**
 * Route-based code splitting with preloading
 */
export class RouteBasedSplitting {
  private routeMap = new Map<string, () => Promise<any>>();
  private preloadMap = new Map<string, string[]>();
  private loadedRoutes = new Set<string>();

  registerRoute(routeName: string, importFn: () => Promise<any>, preloadRoutes: string[] = []) {
    this.routeMap.set(routeName, importFn);
    this.preloadMap.set(routeName, preloadRoutes);
  }

  async loadRoute(routeName: string) {
    const importFn = this.routeMap.get(routeName);
    if (!importFn) {
      throw new Error(`Route ${routeName} not registered`);
    }

    try {
      const module = await dynamicImport(importFn);
      this.loadedRoutes.add(routeName);

      // Preload related routes
      this.preloadRelatedRoutes(routeName);

      return module;
    } catch (error) {
      console.error(`Failed to load route ${routeName}:`, error);
      throw error;
    }
  }

  private async preloadRelatedRoutes(currentRoute: string) {
    const relatedRoutes = this.preloadMap.get(currentRoute) || [];

    for (const routeName of relatedRoutes) {
      if (!this.loadedRoutes.has(routeName)) {
        const importFn = this.routeMap.get(routeName);
        if (importFn) {
          // Preload in background
          setTimeout(() => {
            dynamicImport(importFn).catch(() => {
              // Silent fail for preloading
            });
          }, 100);
        }
      }
    }
  }

  getLoadedRoutes(): string[] {
    return Array.from(this.loadedRoutes);
  }

  preloadRoute(routeName: string) {
    const importFn = this.routeMap.get(routeName);
    if (importFn && !this.loadedRoutes.has(routeName)) {
      dynamicImport(importFn).catch(() => {
        // Silent fail for preloading
      });
    }
  }
}

export const routeSplitter = new RouteBasedSplitting();

/**
 * Dependency-based code splitting
 */
export class DependencyManager {
  private dependencies = new Map<string, () => Promise<any>>();
  private loadedDeps = new Map<string, any>();
  private loadingPromises = new Map<string, Promise<any>>();

  registerDependency(name: string, importFn: () => Promise<any>) {
    this.dependencies.set(name, importFn);
  }

  async loadDependency(name: string): Promise<any> {
    // Return cached if already loaded
    if (this.loadedDeps.has(name)) {
      return this.loadedDeps.get(name);
    }

    // Return existing promise if currently loading
    if (this.loadingPromises.has(name)) {
      return this.loadingPromises.get(name);
    }

    const importFn = this.dependencies.get(name);
    if (!importFn) {
      throw new Error(`Dependency ${name} not registered`);
    }

    const loadingPromise = dynamicImport(importFn).then(module => {
      this.loadedDeps.set(name, module);
      this.loadingPromises.delete(name);
      return module;
    });

    this.loadingPromises.set(name, loadingPromise);
    return loadingPromise;
  }

  async loadDependencies(names: string[]): Promise<any[]> {
    const promises = names.map(name => this.loadDependency(name));
    return Promise.all(promises);
  }

  isLoaded(name: string): boolean {
    return this.loadedDeps.has(name);
  }

  preloadDependencies(names: string[]) {
    names.forEach(name => {
      if (!this.isLoaded(name) && !this.loadingPromises.has(name)) {
        this.loadDependency(name).catch(() => {
          // Silent fail for preloading
        });
      }
    });
  }
}

export const dependencyManager = new DependencyManager();

/**
 * Bundle size analyzer
 */
export class BundleAnalyzer {
  private moduleStats = new Map<
    string,
    {
      size: number;
      loadTime: number;
      lastAccessed: number;
    }
  >();

  recordModuleLoad(moduleName: string, size: number, loadTime: number) {
    this.moduleStats.set(moduleName, {
      size,
      loadTime,
      lastAccessed: Date.now(),
    });
  }

  recordModuleAccess(moduleName: string) {
    const stats = this.moduleStats.get(moduleName);
    if (stats) {
      stats.lastAccessed = Date.now();
    }
  }

  getStats() {
    const modules = Array.from(this.moduleStats.entries()).map(([name, stats]) => ({
      name,
      ...stats,
    }));

    const totalSize = modules.reduce((sum, module) => sum + module.size, 0);
    const averageLoadTime = modules.reduce((sum, module) => sum + module.loadTime, 0) / modules.length;

    return {
      modules,
      totalSize,
      averageLoadTime,
      moduleCount: modules.length,
      largestModules: modules.sort((a, b) => b.size - a.size).slice(0, 10),
      slowestModules: modules.sort((a, b) => b.loadTime - a.loadTime).slice(0, 10),
    };
  }

  getUnusedModules(thresholdDays: number = 7): string[] {
    const threshold = Date.now() - thresholdDays * 24 * 60 * 60 * 1000;

    return Array.from(this.moduleStats.entries())
      .filter(([, stats]) => stats.lastAccessed < threshold)
      .map(([name]) => name);
  }
}

export const bundleAnalyzer = new BundleAnalyzer();

/**
 * Tree shaking utilities
 */
export const createTreeShakableImport = <T extends Record<string, any>>(
  importFn: () => Promise<T>,
  requiredKeys: (keyof T)[],
) => {
  return async (): Promise<Pick<T, keyof T>> => {
    const module = await dynamicImport(importFn);

    // Only return required keys to enable tree shaking
    const result = {} as Pick<T, keyof T>;
    for (const key of requiredKeys) {
      if (key in module) {
        result[key] = module[key];
      }
    }

    return result;
  };
};

/**
 * Conditional imports based on device capabilities
 */
export const createCapabilityBasedImport = <T>(config: {
  highEnd: () => Promise<{default: T}>;
  midRange: () => Promise<{default: T}>;
  lowEnd: () => Promise<{default: T}>;
}) => {
  return async () => {
    const deviceTier = await getDeviceTier();

    switch (deviceTier) {
      case 'high':
        return dynamicImport(config.highEnd);
      case 'mid':
        return dynamicImport(config.midRange);
      case 'low':
        return dynamicImport(config.lowEnd);
      default:
        return dynamicImport(config.midRange);
    }
  };
};

// Helper functions
async function checkFeatureFlag(flag: string): Promise<boolean> {
  // Implement your feature flag logic here
  // This could check remote config, local storage, etc.
  return true;
}

async function getDeviceTier(): Promise<'high' | 'mid' | 'low'> {
  // Implement device capability detection
  // This could check RAM, CPU, etc.
  return 'mid';
}

/**
 * Module federation for micro-frontends
 */
export class ModuleFederation {
  private remoteModules = new Map<string, string>();
  private loadedRemotes = new Map<string, any>();

  registerRemote(name: string, url: string) {
    this.remoteModules.set(name, url);
  }

  async loadRemoteModule(remoteName: string, moduleName: string) {
    const remoteUrl = this.remoteModules.get(remoteName);
    if (!remoteUrl) {
      throw new Error(`Remote ${remoteName} not registered`);
    }

    const cacheKey = `${remoteName}:${moduleName}`;

    if (this.loadedRemotes.has(cacheKey)) {
      return this.loadedRemotes.get(cacheKey);
    }

    try {
      // Load remote module (implementation depends on your setup)
      const module = await this.loadRemoteScript(remoteUrl, moduleName);
      this.loadedRemotes.set(cacheKey, module);
      return module;
    } catch (error) {
      console.error(`Failed to load remote module ${remoteName}:${moduleName}:`, error);
      throw error;
    }
  }

  private async loadRemoteScript(url: string, moduleName: string): Promise<any> {
    // Implementation for loading remote modules
    // This would typically involve dynamic script loading
    throw new Error('Remote module loading not implemented');
  }
}

export const moduleFederation = new ModuleFederation();
