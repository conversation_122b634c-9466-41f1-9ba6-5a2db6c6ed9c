import moment from 'moment';

export const getMessageOfRecurrence = (event_group: any) => {
  const repeatRule = event_group?.repeat_rule;
  if (repeatRule?.duration === 'Weekly') {
    return `Every ${repeatRule.repeat_every} weeks on ${repeatRule.repeat_on.join(',')}, until ${moment(
      repeatRule.end_date,
    ).format('MMM DD, YYYY')}`;
  } else {
    let every = '';
    if (repeatRule?.duration === 'Daily') {
      if (repeatRule.repeat_every == 1) {
        every = 'day';
      } else {
        every = 'days';
      }
    } else if (repeatRule?.duration === 'Monthly') {
      if (repeatRule.repeat_every == 1) {
        every = 'month';
      } else {
        every = 'months';
      }
    } else if (repeatRule?.duration === 'Yearly') {
      if (repeatRule.repeat_every == 1) {
        every = 'year';
      } else {
        every = 'years';
      }
    }
    return `Every ${repeatRule?.repeat_every} ${every}, until ${moment(repeatRule?.end_date).format('MMM DD, YYYY')}`;
  }
};
