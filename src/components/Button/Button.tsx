import {GestureResponderEvent, Text, TextStyle, TouchableOpacity, ViewStyle} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import ModernSpinner from '~components/ModernSpinner';

interface Props {
  label: string;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  isLoading?: boolean;
  onPress: (event?: GestureResponderEvent) => void;
  disabled?: boolean;
  icon?: JSX.Element;
  spinnerColor?: string;
}

const Button = ({
  label,
  icon,
  containerStyle,
  textStyle,
  disabled,
  isLoading = false,
  onPress,
  spinnerColor,
}: Props) => {
  const {colors} = useTheme();

  return (
    <>
      {isLoading === true ? (
        <TouchableOpacity
          disabled={true}
          style={{
            paddingHorizontal: 20,
            paddingVertical: 11,
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 6,
            backgroundColor: colors.error,
            ...containerStyle,
          }}>
          <ModernSpinner size={20} color={spinnerColor ? spinnerColor : colors.white} variant="circular" />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          onPress={onPress}
          disabled={disabled}
          style={{
            paddingHorizontal: 20,
            paddingVertical: 11,
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 6,
            opacity: disabled ? 0.6 : 1,
            ...containerStyle,
          }}>
          {icon ? icon : <Text style={{fontSize: 15, lineHeight: 18, ...textStyle}}>{label}</Text>}
        </TouchableOpacity>
      )}
    </>
  );
};

export default Button;
