// import notifee, {EventType} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {PERMISSIONS, request} from 'react-native-permissions';
import {SCREENS} from '~constants';
import {navigationRef} from '~containers/Core/navigation/Navigation';

export const getFcmToken = async () => {
  let token = null;
  await checkApplicationNotificationPermission();
  await registerAppWithFCM();
  try {
    token = await messaging().getToken();
  } catch (error) {
    console.log('getFcmToken Device Token error ', error);
  }
  return token;
};

export async function registerAppWithFCM() {
  console.log('registerAppWithFCM status', messaging().isDeviceRegisteredForRemoteMessages);
  if (!messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .registerDeviceForRemoteMessages()
      .then(status => {
        console.log('registerDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('registerDeviceForRemoteMessages error ', error);
      });
  }
}

export async function unRegisterAppWithFCM() {
  console.log('unRegisterAppWithFCM status', messaging().isDeviceRegisteredForRemoteMessages);

  if (messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .unregisterDeviceForRemoteMessages()
      .then(status => {
        console.log('unregisterDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('unregisterDeviceForRemoteMessages error ', error);
      });
  }
  await messaging().deleteToken();
  console.log('unRegisterAppWithFCM status', messaging().isDeviceRegisteredForRemoteMessages);
}

export const checkApplicationNotificationPermission = async () => {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED || authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    console.log('Authorization status:', authStatus);
  }
  request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS)
    .then(result => {
      console.log('POST_NOTIFICATIONS status:', result);
    })
    .catch(error => {
      console.log('POST_NOTIFICATIONS error ', error);
    });
};

function onNotificationClickActionHandling(clickAction: any, data: any) {
  console.log('Notification click action:', clickAction);
  switch (clickAction) {
    case 'OPEN_CHAT':
      navigationRef.current?.navigate(SCREENS.CHAT_STACK, {
        screen: `${SCREENS.USER_CHAT}`,
        params: {
          chatId: data.chat_id,
        },
      });
      break;
    case 'OPEN_PROFILE':
      // Navigate to profile screen
      break;
    case 'OPEN_NOTIFICATIONS':
      const item = JSON.parse(data.item); // Розпарсити дані item
      navigationRef.current?.navigate(SCREENS.PENDING_ATTENDEES, {eventId: item.event_id, eventName: item.name});
      break;
    default:
      break;
  }
}

export function registerListenerWithFCM() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    console.log('onMessage Received:', JSON.stringify(remoteMessage));
    if (remoteMessage?.notification?.title && remoteMessage?.notification?.body) {
      onDisplayNotification(remoteMessage.notification?.title, remoteMessage.notification?.body, remoteMessage?.data);
    }
  });

  // notifee.onForegroundEvent(({type, detail}) => {
  //   switch (type) {
  //     case EventType.DISMISSED:
  //       console.log('User dismissed notification', detail.notification);
  //       break;
  //     case EventType.PRESS:
  //       console.log('User pressed notification', detail.notification);
  //       if (detail.notification?.data?.clickAction) {
  //         onNotificationClickActionHandling(detail.notification.data.clickAction, detail.notification.data);
  //       }
  //       break;
  //   }
  // });

  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log('onNotificationOpenedApp Received', JSON.stringify(remoteMessage));
    if (remoteMessage?.data?.clickAction) {
      onNotificationClickActionHandling(remoteMessage.data.clickAction, remoteMessage.data);
    }
  });

  messaging()
    .getInitialNotification()
    .then(remoteMessage => {
      if (remoteMessage) {
        console.log('Notification caused app to open from quit state:', remoteMessage.notification);
        if (remoteMessage?.data?.clickAction) {
          onNotificationClickActionHandling(remoteMessage.data.clickAction, remoteMessage.data);
        }
      }
    });

  return unsubscribe;
}

async function onDisplayNotification(title: any, body: any, data: any) {
  console.log('onDisplayNotification:', JSON.stringify(data));

  // Temporarily using react-native-notifier instead of notifee
  Notifier.showNotification({
    title: title,
    description: body,
    Component: NotifierComponents.Alert,
    componentProps: {
      alertType: 'info',
    },
  });

  // TODO: Re-enable notifee when circular dependency is resolved
  // // Request permissions (required for iOS)
  // await notifee.requestPermission();

  // // Create a channel (required for Android)
  // const channelId = await notifee.createChannel({
  //   id: 'default',
  //   name: 'Default Channel',
  // });

  // // Display a notification
  // await notifee.displayNotification({
  //   title: title,
  //   body: body,
  //   data: data,
  //   android: {
  //     channelId,
  //     pressAction: {
  //       id: 'default',
  //       launchActivity: 'default',
  //     },
  //   },
  // });
}
