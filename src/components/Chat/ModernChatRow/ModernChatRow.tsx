import React, {memo, useCallback, useMemo} from 'react';
import {Text, TouchableOpacity, View, Image} from 'react-native';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useNavigation} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';

import {formatDateTime} from '~Utils/Time';
import {getLastMessage} from '~Utils/chat';
import {LogoIcon, ChatIcon, CheckIcon} from '~assets/icons';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import FirebaseChatsService from '~services/FirebaseChats';
import {CHAT_TYPE_ENUM, ChatType, ChatTypeWithKey} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useTheme} from '~contexts/ThemeContext';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {spacing, borderRadius, shadows, typography} from '~constants/design';

interface ModernChatRowProps {
  chat: ChatTypeWithKey;
  index?: number;
}

const ModernChatRow: React.FC<ModernChatRowProps> = memo(({chat, index = 0}) => {
  const {colors} = useTheme();
  const uid = auth().currentUser?.uid;
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {data: userAccount} = useGetUserAccount(uid);

  // All hooks must be called before any early returns
  const unreadMessagesCount = useMemo(() => {
    if (!chat?.history) return 0;
    return (
      chat.history.filter((message: any) => message?.readUserIds && !message.readUserIds.includes(uid))?.length || 0
    );
  }, [chat?.history, uid]);

  const lastMessage = useMemo(() => {
    if (!chat?.history) return null;
    return getLastMessage(chat.history);
  }, [chat?.history]);

  const renderUserImage = useMemo(() => {
    const image = chat?.eventImage || chat?.history?.find(message => message.sender_id !== uid)?.sender_image;
    if (!image) {
      return (
        <View
          style={{
            width: 56,
            height: 56,
            borderRadius: borderRadius['2xl'],
            backgroundColor: colors.primary,
            alignItems: 'center',
            justifyContent: 'center',
            ...shadows.sm,
          }}>
          <LogoIcon color={colors.white} />
        </View>
      );
    }
    return (
      <FastImage
        source={{uri: image, priority: 'high'}}
        style={{
          width: 56,
          height: 56,
          borderRadius: borderRadius['2xl'],
          ...shadows.sm,
        }}
      />
    );
  }, [chat?.eventImage, chat?.history, uid, colors]);

  const selectChat = useCallback(async () => {
    if (!chat) return;

    const interlocutorId = chat?.userIds?.find(userId => userId !== uid);
    const interlocutorMessage = chat?.history?.find(message => message.sender_id === interlocutorId);
    const interlocutorName = interlocutorMessage?.sender || chat?.users?.find(user => user !== lastMessage?.sender);
    const interlocutorImage = interlocutorMessage?.sender_image;

    const topText = chat?.eventName || interlocutorName;
    const image = chat?.eventImage || interlocutorImage;
    const userId = chat?.userIds?.find(id => id !== uid);
    const userIndex = chat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
    const userName = chat?.users?.[userIndex];

    try {
      const chatRef = firestore()
        .collection('chats')
        .doc(chat?.key || '');
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(uid)) {
            return {...message, readUserIds: [...(message.readUserIds || []), uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      setIsTabBarDisabled(true);

      if (chat?.type === CHAT_TYPE_ENUM.ORGANISATION || chat?.type === 'contact-pyxi') {
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chat?.key, image: image, userName: userName});
      } else {
        const chatId = await FirebaseChatsService.createPrivateChat({
          user_id1: uid!,
          user_id2: userId!,
          user_name1: topText || '',
          user_name2: userName || '',
          user_image: image || '',
        });
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chatId, image: image, userName: userName});
      }
    } catch (error) {
      console.error(error);
    }
  }, [chat, uid, lastMessage, setIsTabBarDisabled, navigation]);

  // Safety check for chat prop - now after all hooks
  if (!chat) {
    return null;
  }

  const interlocutorId = chat?.userIds?.find(userId => userId !== uid);
  const interlocutorMessage = chat?.history?.find(message => message.sender_id === interlocutorId);
  const interlocutorName = interlocutorMessage?.sender || chat?.users?.find(user => user !== lastMessage?.sender);
  const interlocutorImage = interlocutorMessage?.sender_image;

  const topText = chat?.eventName || interlocutorName;
  const middleText = chat?.eventId ? lastMessage?.sender : null;
  const bottomText = lastMessage?.message;
  const image = chat?.eventImage || interlocutorImage;
  const userIndex = chat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
  const userName = chat?.users?.[userIndex];

  const getLastMessageTime = () => {
    if (!lastMessage?.timestamp) {
      return '';
    }
    return formatDateTime(lastMessage.timestamp);
  };

  const isUnread = unreadMessagesCount > 0;

  return (
    <FadeIn delay={index * 50} duration={300}>
      <TouchableOpacity
        style={[
          {
            backgroundColor: colors.surface,
            borderRadius: borderRadius.xl,
            marginHorizontal: spacing.md,
            marginVertical: spacing.xs,
            padding: spacing.md,
            flexDirection: 'row',
            alignItems: 'center',
            ...shadows.sm,
            borderWidth: isUnread ? 1 : 0,
            borderColor: isUnread ? colors.primary : 'transparent',
          },
        ]}
        onPress={selectChat}
        activeOpacity={0.7}>
        {/* User Avatar */}
        <View style={{marginRight: spacing.md}}>{renderUserImage}</View>

        {/* Chat Content */}
        <View style={{flex: 1, marginRight: spacing.sm}}>
          <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: spacing.xs}}>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: isUnread ? typography.fontWeight.semibold : typography.fontWeight.medium,
                color: colors.textPrimary,
                flex: 1,
              }}
              numberOfLines={1}>
              {userName || 'Unknown User'} {chat?.type === 'contact-pyxi' ? '(Support)' : ''}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.xs,
                color: colors.textSecondary,
                fontWeight: typography.fontWeight.medium,
              }}>
              {getLastMessageTime()}
            </Text>
          </View>

          {middleText && (
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.primary,
                fontWeight: typography.fontWeight.medium,
                marginBottom: spacing.xs,
              }}
              numberOfLines={1}>
              {middleText}
            </Text>
          )}

          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.textSecondary,
                flex: 1,
                fontWeight: isUnread ? typography.fontWeight.medium : typography.fontWeight.normal,
              }}
              numberOfLines={1}>
              {bottomText}
            </Text>

            {isUnread && (
              <View
                style={{
                  backgroundColor: colors.primary,
                  borderRadius: borderRadius.full,
                  minWidth: 20,
                  height: 20,
                  paddingHorizontal: spacing.xs,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: spacing.sm,
                }}>
                <Text
                  style={{
                    fontSize: typography.fontSize.xs,
                    color: colors.white,
                    fontWeight: typography.fontWeight.semibold,
                  }}>
                  {unreadMessagesCount > 99 ? '99+' : unreadMessagesCount}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </FadeIn>
  );
});

export default ModernChatRow;
