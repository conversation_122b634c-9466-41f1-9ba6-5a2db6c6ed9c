import React, {useState, useRef, useEffect} from 'react';
import {Pressable, TextInput, View, Text} from 'react-native';
import {SendArrow} from '~assets/icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  Extrapolation,
  SharedValue,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~utils/haptics';
import {spacing, borderRadius, shadows} from '~constants/design';
import {useMessageStatus} from '~hooks/chat/useMessageStatus';
import {MESSAGE_STATUS} from '~types/chat';

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface ChatInputProps {
  submit: (text: string, tempMessageId?: string) => Promise<string | void>;
  h: SharedValue<number>;
  progress: SharedValue<number>;
  placeholder?: string;
  maxLength?: number;
  showCharacterCount?: boolean;
  disabled?: boolean;
  onMessageStatusChange?: (tempId: string, status: MESSAGE_STATUS, realMessageId?: string) => void;
}

export default function ChatInput({
  submit,
  h,
  progress,
  placeholder = 'Type a message...',
  maxLength = 1000,
  showCharacterCount = false,
  disabled = false,
  onMessageStatusChange,
}: ChatInputProps) {
  const {colors} = useTheme();
  const [textInput, setTextInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {bottom} = useSafeAreaInsets();
  const textInputRef = useRef<TextInput>(null);
  const {addPendingMessage, updateMessageStatus} = useMessageStatus();

  // Animation values
  const sendButtonScale = useSharedValue(0.8);
  const sendButtonOpacity = useSharedValue(0.6);
  const inputBorderWidth = useSharedValue(1);
  const inputShadowOpacity = useSharedValue(0);

  // Update send button animation based on text input
  useEffect(() => {
    const hasText = textInput.trim().length > 0;
    sendButtonScale.value = withSpring(hasText ? 1 : 0.8, {damping: 15});
    sendButtonOpacity.value = withTiming(hasText ? 1 : 0.6, {duration: 200});
  }, [textInput, sendButtonScale, sendButtonOpacity]);

  // Update input styling based on focus
  useEffect(() => {
    inputBorderWidth.value = withTiming(isFocused ? 2 : 1, {duration: 200});
    inputShadowOpacity.value = withTiming(isFocused ? 0.1 : 0, {duration: 200});
  }, [isFocused, inputBorderWidth, inputShadowOpacity]);

  const containerStyle = useAnimatedStyle(
    () => ({
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.surface,
      paddingBottom: interpolate(progress.value, [0, 1], [bottom || spacing.lg, spacing.md], Extrapolation.CLAMP),
      width: '100%',
      transform: [{translateY: -h.value}],
      borderTopWidth: 1,
      borderTopColor: colors.border,
      ...shadows.sm,
    }),
    [colors],
  );

  const inputContainerStyle = useAnimatedStyle(() => ({
    flex: 1,
    minHeight: 44,
    maxHeight: 120,
    borderRadius: borderRadius['2xl'],
    backgroundColor: colors.inputBackground,
    borderWidth: inputBorderWidth.value,
    borderColor: isFocused ? colors.primary : colors.inputBorder,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: inputShadowOpacity.value,
    shadowRadius: 4,
    elevation: inputShadowOpacity.value * 5,
    justifyContent: 'center',
  }));

  const sendButtonStyle = useAnimatedStyle(() => ({
    width: 44,
    height: 44,
    borderRadius: borderRadius.full,
    backgroundColor: textInput.trim() ? colors.primary : colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{scale: sendButtonScale.value}],
    opacity: sendButtonOpacity.value,
    ...shadows.sm,
  }));

  const characterCountStyle = useAnimatedStyle(() => ({
    opacity: withTiming(showCharacterCount && textInput.length > maxLength * 0.8 ? 1 : 0, {duration: 200}),
  }));

  const handleSubmit = async () => {
    const text = textInput.trim();
    if (!text || isSubmitting || disabled) {
      return;
    }

    // Generate a temporary message ID
    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Haptic feedback for send action
    haptics.medium();

    setIsSubmitting(true);

    // Add pending message with "sending" status
    addPendingMessage(tempMessageId, text);
    onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENDING);

    // Animate send button
    sendButtonScale.value = withSpring(0.9, {damping: 15}, () => {
      sendButtonScale.value = withSpring(1, {damping: 15});
    });

    try {
      console.log('ChatInput: Attempting to send message:', text);
      const realMessageId = await submit(text, tempMessageId);
      console.log('ChatInput: Message sent successfully');

      // Update status to "sent"
      updateMessageStatus(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string);
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string);

      // Only clear the text input after successful submission
      setTextInput('');
    } catch (error) {
      console.log('ChatInput: Failed to send message:', error);

      // Update status to "failed"
      updateMessageStatus(tempMessageId, MESSAGE_STATUS.FAILED);
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.FAILED);

      // Keep the text in the input on error
      haptics.error();
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: any) => {
    if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    haptics.light();
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleTextChange = (text: string) => {
    if (text.length <= maxLength) {
      setTextInput(text);
    } else {
      haptics.warning();
    }
  };

  const isOverLimit = textInput.length > maxLength * 0.9;
  const characterCount = textInput.length;

  return (
    <Animated.View style={containerStyle}>
      <Animated.View style={inputContainerStyle}>
        <AnimatedTextInput
          ref={textInputRef}
          value={textInput}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          placeholderTextColor={colors.placeholderText}
          style={{
            flex: 1,
            fontSize: 16,
            lineHeight: 22,
            color: colors.inputText,
            textAlignVertical: 'top',
            paddingVertical: spacing.sm,
            paddingTop: spacing.sm,
            minHeight: 20,
            maxHeight: 100,
          }}
          multiline={true}
          maxLength={maxLength}
          editable={!disabled && !isSubmitting}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
          blurOnSubmit={false}
          scrollEnabled={true}
          enablesReturnKeyAutomatically={true}
        />

        {showCharacterCount && (
          <Animated.View style={[characterCountStyle, {position: 'absolute', right: spacing.sm, bottom: 2}]}>
            <Text
              style={{
                fontSize: 12,
                color: isOverLimit ? colors.error : colors.textSecondary,
                fontWeight: isOverLimit ? '600' : '400',
              }}>
              {characterCount}/{maxLength}
            </Text>
          </Animated.View>
        )}
      </Animated.View>

      <AnimatedPressable
        style={sendButtonStyle}
        onPress={handleSubmit}
        disabled={!textInput.trim() || isSubmitting || disabled}>
        <SendArrow isActive={!!textInput.trim() && !isSubmitting} />
      </AnimatedPressable>
    </Animated.View>
  );
}
