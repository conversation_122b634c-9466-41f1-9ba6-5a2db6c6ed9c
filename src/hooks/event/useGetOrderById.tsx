import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {OrderDetail} from '~types/api/event';

export const useGetOrderById = (order_id: string) => {
  const {t} = useTranslation();

  return useQuery<OrderDetail, Error>(
    ['orderById', order_id],
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}orders/${order_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (response.status === 404) {
        Alert.alert(t('events.event_has_been_deleted'));
      }
      if (!response.ok) {
        throw new Error('Failed to get the event by id');
      }

      const data = await response.json();

      return data;
    },
    {enabled: !!order_id},
  );
};
