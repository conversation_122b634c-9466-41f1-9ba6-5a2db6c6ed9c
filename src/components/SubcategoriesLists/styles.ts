import {StyleSheet} from 'react-native';
export const createStyles = (colors: any) =>
  StyleSheet.create({
    moreCatView: {
      flexDirection: 'row',
      marginLeft: '6%',
      marginTop: 10,
      marginBottom: 10,
      alignItems: 'center',
    },
    categoryText: {
      fontSize: 16,
      color: colors.textSecondary,
      fontWeight: 'bold',
    },
    moreCatText: {
      fontSize: 15,
      color: colors.secondary,
      fontWeight: 'bold',
      marginRight: 10,
    },
    categoryView: {
      backgroundColor: colors.textSecondary + '33',
      borderRadius: 30,
      width: '92%',
      alignSelf: 'center',
      paddingVertical: 10,
      paddingHorizontal: 15,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 14,
    },
    header: {
      fontSize: 20,
      fontWeight: 'bold',
      marginTop: 20,
      marginLeft: 10,
    },
    itemContainer: {
      paddingHorizontal: 16,
      paddingVertical: 10,
      margin: 10,
      borderRadius: 30,
      flexDirection: 'row',
    },
    subCatDesc: {
      fontSize: 15,
      color: colors.white,
      marginTop: 3,
    },
    subCatTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.white,
    },
    imageContainer: {
      padding: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      flex: 1,
    },
    image: {
      width: '100%',
      borderRadius: 70,
      height: '100%',
      overflow: 'hidden',
    },
    imageView: {
      height: 85,
      width: '91%',
      alignSelf: 'center',
      marginTop: 14,
      borderRadius: 70,
    },
    overlay: {
      borderRadius: 70,
      ...StyleSheet.absoluteFillObject,
      backgroundColor: colors.overlayBackground,
    },
  });
