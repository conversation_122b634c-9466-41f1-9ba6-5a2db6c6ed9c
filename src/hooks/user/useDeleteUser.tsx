import Config from 'react-native-config';
import {useMutation} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';

export function useDeleteUser() {
  const mutation = useMutation(async (user_id: string) => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}users/${user_id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete user');
    }
  });

  return mutation;
}
