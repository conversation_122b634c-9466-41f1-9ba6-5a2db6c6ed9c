#!/usr/bin/env node

/**
 * <PERSON>ript to fix JSX attributes that were incorrectly replaced
 * Usage: node scripts/fix-jsx-attributes.js
 */

const fs = require('fs');
const path = require('path');

// File extensions to process
const EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to process
const DIRECTORIES = ['src/components', 'src/containers'];

function fixJSXAttributes(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Fix JSX attributes that are missing curly braces
  // Pattern: attribute=colors.something should be attribute={colors.something}
  const jsxAttrRegex = /(\w+)=(colors\.\w+)(?!\})/g;
  if (content.match(jsxAttrRegex)) {
    content = content.replace(jsxAttrRegex, '$1={$2}');
    hasChanges = true;
  }

  // Fix specific patterns that might have been missed
  const patterns = [
    // Fix fill=colors.something
    {from: /fill=(colors\.\w+)(?!\})/g, to: 'fill={$1}'},
    // Fix stroke=colors.something
    {from: /stroke=(colors\.\w+)(?!\})/g, to: 'stroke={$1}'},
    // Fix color=colors.something
    {from: /color=(colors\.\w+)(?!\})/g, to: 'color={$1}'},
    // Fix backgroundColor=colors.something
    {from: /backgroundColor=(colors\.\w+)(?!\})/g, to: 'backgroundColor={$1}'},
    // Fix backgroundActive=colors.something
    {from: /backgroundActive=(colors\.\w+)(?!\})/g, to: 'backgroundActive={$1}'},
    // Fix textColor=colors.something
    {from: /textColor=(colors\.\w+)(?!\})/g, to: 'textColor={$1}'},
    // Fix placeholderTextColor=colors.something
    {from: /placeholderTextColor=(colors\.\w+)(?!\})/g, to: 'placeholderTextColor={$1}'},
    // Fix minimumTrackTintColor=colors.something
    {from: /minimumTrackTintColor=(colors\.\w+)(?!\})/g, to: 'minimumTrackTintColor={$1}'},
    // Fix maximumTrackTintColor=colors.something
    {from: /maximumTrackTintColor=(colors\.\w+)(?!\})/g, to: 'maximumTrackTintColor={$1}'},
    // Fix spinnerColor=colors.something
    {from: /spinnerColor=(colors\.\w+)(?!\})/g, to: 'spinnerColor={$1}'},
  ];

  patterns.forEach(pattern => {
    if (content.match(pattern.from)) {
      content = content.replace(pattern.from, pattern.to);
      hasChanges = true;
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed JSX attributes in: ${filePath}`);
  }

  return hasChanges;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath, {withFileTypes: true});

  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);

    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (EXTENSIONS.some(ext => file.name.endsWith(ext))) {
      try {
        fixJSXAttributes(fullPath);
      } catch (error) {
        console.error(`❌ Error processing ${fullPath}:`, error.message);
      }
    }
  }
}

// Main execution
console.log('🔧 Fixing JSX attributes...\n');

for (const dir of DIRECTORIES) {
  console.log(`Processing directory: ${dir}`);
  processDirectory(dir);
}

console.log('\n✨ JSX attribute fixes completed!');
