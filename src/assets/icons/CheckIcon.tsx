import Svg, {Path} from 'react-native-svg';

interface CheckIconProps {
  color?: string;
  width?: number;
  height?: number;
}

const CheckIcon = ({color, width = 40, height = 41}: CheckIconProps) => (
  <Svg width={width} height={height} viewBox="0 0 40 41" fill="none">
    <Path
      d="M19.9053 37.9473C29.1689 37.9473 36.8389 30.2607 36.8389 21.0137C36.8389 11.75 29.1523 4.08008 19.8887 4.08008C10.6416 4.08008 2.97168 11.75 2.97168 21.0137C2.97168 30.2607 10.6582 37.9473 19.9053 37.9473ZM19.9053 35.125C12.0693 35.125 5.81055 28.8496 5.81055 21.0137C5.81055 13.1777 12.0527 6.90234 19.8887 6.90234C27.7246 6.90234 34.0166 13.1777 34.0166 21.0137C34.0166 28.8496 27.7412 35.125 19.9053 35.125ZM18.0791 28.8994C18.627 28.8994 19.0918 28.6338 19.4238 28.1191L27.0107 16.1826C27.1934 15.8506 27.4092 15.4854 27.4092 15.1201C27.4092 14.373 26.7451 13.8916 26.0479 13.8916C25.6328 13.8916 25.2178 14.1572 24.9023 14.6387L18.0127 25.6953L14.7422 21.4619C14.3438 20.9307 13.9785 20.7979 13.5137 20.7979C12.7998 20.7979 12.2354 21.3789 12.2354 22.1094C12.2354 22.4746 12.3848 22.8232 12.6172 23.1387L16.668 28.1191C17.083 28.667 17.5312 28.8994 18.0791 28.8994Z"
      fill={color || '#2E2EAB'}
    />
  </Svg>
);

export default CheckIcon;
