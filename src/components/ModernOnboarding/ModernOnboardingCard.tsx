import React from 'react';
import {View, Text, TouchableOpacity, ViewStyle, TextStyle} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {haptics} from '~utils/haptics';
import Animated, {FadeInDown, ScaleIn} from 'react-native-reanimated';

interface ModernOnboardingCardProps {
  title: string;
  description: string;
  children?: React.ReactNode;
  onPress?: () => void;
  selected?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  animationDelay?: number;
}

const ModernOnboardingCard: React.FC<ModernOnboardingCardProps> = ({
  title,
  description,
  children,
  onPress,
  selected = false,
  disabled = false,
  style,
  titleStyle,
  descriptionStyle,
  animationDelay = 0,
}) => {
  const {colors} = useTheme();

  const handlePress = () => {
    if (onPress && !disabled) {
      haptics.medium();
      onPress();
    }
  };

  const cardStyle: ViewStyle = {
    backgroundColor: selected ? colors.primary + '10' : colors.cardBackground,
    borderRadius: borderRadius['3xl'],
    padding: spacing.xl,
    marginVertical: spacing.md,
    borderWidth: selected ? 2 : 1,
    borderColor: selected ? colors.primary : colors.border + '40',
    opacity: disabled ? 0.6 : 1,
    ...style,
  };

  const titleTextStyle: TextStyle = {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.textPrimary,
    marginBottom: spacing.xs,
    ...titleStyle,
  };

  const descriptionTextStyle: TextStyle = {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.normal,
    color: colors.textSecondary,
    lineHeight: typography.fontSize.sm * typography.lineHeight.relaxed,
    ...descriptionStyle,
  };

  if (onPress) {
    return (
      <Animated.View entering={FadeInDown.delay(animationDelay).duration(400)}>
        <TouchableOpacity style={cardStyle} onPress={handlePress} disabled={disabled} activeOpacity={0.8}>
          {children}
          <View style={{marginTop: children ? spacing.md : 0}}>
            <Text style={titleTextStyle}>{title}</Text>
            <Text style={descriptionTextStyle}>{description}</Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View entering={ScaleIn.delay(animationDelay).duration(400)} style={cardStyle}>
      {children}
      <View style={{marginTop: children ? spacing.md : 0}}>
        <Text style={titleTextStyle}>{title}</Text>
        <Text style={descriptionTextStyle}>{description}</Text>
      </View>
    </Animated.View>
  );
};

export default ModernOnboardingCard;
