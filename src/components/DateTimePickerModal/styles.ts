import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 20,
      paddingBottom: 30,
      paddingHorizontal: 12,
      alignItems: 'center',
      overflow: 'hidden',
      backgroundColor: colors.white,
    },
    title: {
      fontSize: 20,
      textAlign: 'center',
      marginBottom: 8,
    },
    label: {
      fontSize: 14,
      color: colors.textSecondary,
      marginRight: 10,
    },
    buttonContainer: {
      width: 75,
      height: 40,
      borderRadius: 8,
      backgroundColor: colors.gray100,
      marginRight: 15,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonLabel: {
      fontSize: 16,
      color: colors.textSecondary,
    },
    selectedButton: {
      borderColor: colors.secondary,
      borderWidth: 1,
    },
    datePickerContainer: {
      marginTop: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    doneButton: {
      width: '100%',
      backgroundColor: colors.eventInfluencer,
      borderRadius: 10,
      marginTop: 8,
      alignSelf: 'center',
      alignItems: 'center',
    },
    doneButtonLabel: {
      fontSize: 15,
      marginVertical: 10,
      color: colors.white,
    },
  });

export default getStyles;
