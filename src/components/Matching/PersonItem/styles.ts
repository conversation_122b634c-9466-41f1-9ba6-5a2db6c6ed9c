import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {paddingVertical: 6, width: '100%'},
    wrapper: {
      backgroundColor: colors.white,
      marginHorizontal: 16,
      minHeight: 108,
      borderRadius: 5,
    },
    radius: {borderRadius: 16},
    viewWrapper: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
      paddingHorizontal: 16,
      paddingVertical: 16,
    },
    image: {
      width: 76,
      height: 76,
      borderRadius: 12,
      marginRight: 10,
    },
    flex3: {flex: 3, marginTop: 5},
    nameText: {
      fontSize: 20,
      color: colors.black,
      fontWeight: '600',
      alignItems: 'center',
      lineHeight: 24,
      letterSpacing: -0.35,
    },
    descriptionText: {
      color: colors.textSecondary,
      fontWeight: 'normal',
      fontSize: 14,
    },
    fullWidth: {width: '100%'},
  });

export default getStyles;
