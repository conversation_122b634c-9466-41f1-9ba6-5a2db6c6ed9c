import React, {useEffect, useMemo, useRef, useState} from 'react';
import {View, StyleSheet, Animated, Modal} from 'react-native';
import {Event} from '~types/api/event';
import CompleteModernEventDetail from './CompleteModernEventDetail';
import {DeviceEventEmitter} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

const EventDetailsBottomSheet = ({event}: {event: Event}) => {
  const {colors} = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (event) {
      setIsVisible(true);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsVisible(false);
      });
    }
  }, [event]);

  const onClose = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
      DeviceEventEmitter.emit('eventSelection', {event: null});
    });
  };

  if (!event || !isVisible) {
    return null;
  }

  return (
    <Modal visible={isVisible} animationType="none" presentationStyle="fullScreen" statusBarTranslucent>
      <Animated.View style={[styles.container, {opacity: fadeAnim}]}>
        <CompleteModernEventDetail event={event} onClose={onClose} />
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default EventDetailsBottomSheet;
