'use strict';
Object.defineProperty(exports, '__esModule', {value: true});
exports.SUBSCRIPTION_STATUS = exports.USER_EVENT_STATUS = exports.Gender = void 0;
var Gender;
(function (Gender) {
  Gender.MAN = 'man';
  Gender.WOMAN = 'woman';
  Gender.OTHER = 'other';
  Gender.PREFER_NOT_TO_SAY = 'prefer';
})((Gender = exports.Gender || (exports.Gender = {})));
var USER_EVENT_STATUS;
(function (USER_EVENT_STATUS) {
  USER_EVENT_STATUS.PENDING = 'pending';
  USER_EVENT_STATUS.ACCEPTED = 'accepted';
  USER_EVENT_STATUS.JOIN = 'Join';
})((USER_EVENT_STATUS = exports.USER_EVENT_STATUS || (exports.USER_EVENT_STATUS = {})));
var SUBSCRIPTION_STATUS;
(function (SUBSCRIPTION_STATUS) {
  SUBSCRIPTION_STATUS.PENDING = 'pending';
  SUBSCRIPTION_STATUS.ACCEPTED = 'accepted';
})((SUBSCRIPTION_STATUS = exports.SUBSCRIPTION_STATUS || (exports.SUBSCRIPTION_STATUS = {})));
//# sourceMappingURL=user.js.map
