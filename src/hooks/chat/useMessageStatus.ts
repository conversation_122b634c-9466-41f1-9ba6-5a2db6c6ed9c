import {useState, useCallback, useRef} from 'react';
import {MESSAGE_STATUS} from '~types/chat';

interface PendingMessage {
  tempId: string;
  text: string;
  timestamp: string;
  status: MESSAGE_STATUS;
}

interface MessageStatusHook {
  pendingMessages: PendingMessage[];
  addPendingMessage: (tempId: string, text: string) => void;
  updateMessageStatus: (tempId: string, status: MESSAGE_STATUS, realMessageId?: string) => void;
  removePendingMessage: (tempId: string) => void;
  getMessageStatus: (messageId: string) => MESSAGE_STATUS | undefined;
}

export const useMessageStatus = (): MessageStatusHook => {
  const [pendingMessages, setPendingMessages] = useState<PendingMessage[]>([]);
  const messageStatusMap = useRef<Map<string, MESSAGE_STATUS>>(new Map());

  const addPendingMessage = useCallback((tempId: string, text: string) => {
    const newMessage: PendingMessage = {
      tempId,
      text,
      timestamp: new Date().toISOString(),
      status: MESSAGE_STATUS.SENDING,
    };

    setPendingMessages(prev => [...prev, newMessage]);
    messageStatusMap.current.set(tempId, MESSAGE_STATUS.SENDING);
  }, []);

  const updateMessageStatus = useCallback((tempId: string, status: MESSAGE_STATUS, realMessageId?: string) => {
    // Update the status in our map
    messageStatusMap.current.set(tempId, status);

    // If we have a real message ID, also map it
    if (realMessageId) {
      messageStatusMap.current.set(realMessageId, status);
    }

    // Update pending messages
    setPendingMessages(prev => prev.map(msg => (msg.tempId === tempId ? {...msg, status} : msg)));

    // If message is sent successfully, we can remove it from pending after a delay
    if (status === MESSAGE_STATUS.SENT && realMessageId) {
      setTimeout(() => {
        setPendingMessages(prev => prev.filter(msg => msg.tempId !== tempId));
      }, 1000);
    }
  }, []);

  const removePendingMessage = useCallback((tempId: string) => {
    setPendingMessages(prev => prev.filter(msg => msg.tempId !== tempId));
    messageStatusMap.current.delete(tempId);
  }, []);

  const getMessageStatus = useCallback((messageId: string): MESSAGE_STATUS | undefined => {
    return messageStatusMap.current.get(messageId);
  }, []);

  return {
    pendingMessages,
    addPendingMessage,
    updateMessageStatus,
    removePendingMessage,
    getMessageStatus,
  };
};
