module.exports = {
  root: true,
  extends: ['prettier', '@react-native-community/eslint-config'],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'import', 'react-hooks'],
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      rules: {
        '@typescript-eslint/no-shadow': 'off', // Disabled per user request
        'no-shadow': 'off',
        'no-undef': 'off',
        'react/react-in-jsx-scope': 'off',
        'react/no-unstable-nested-components': 'off',
        '@typescript-eslint/no-explicit-any': 'off', // Disabled per user request
        '@typescript-eslint/no-unused-vars': 'off', // Disabled per user request
        'no-unused-vars': 'off', // Also disable the base rule
        'react-hooks/rules-of-hooks': 'error', // Checks rules of Hooks
        'react-hooks/exhaustive-deps': 'warn', // Checks effect dependencies
        'react-native/no-inline-styles': 'off', // Allow inline styles
        'react/style-prop-object': 'off', // Allow style prop objects
      },
    },
  ],
};
