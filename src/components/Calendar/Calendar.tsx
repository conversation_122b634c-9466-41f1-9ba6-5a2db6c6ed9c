import moment from 'moment';
import {RefObject, useEffect, useRef, useState} from 'react';
import {Alert, Dimensions, FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import {ChevronIcon} from '~assets/icons';
import Button from '~components/Button';
import {Event, Recurrence} from '~types/api/event';
import {groupByDate} from '~Utils/Time';
import {useTheme, useThemedStyles} from '~contexts/ThemeContext';

interface Props {
  refRBSheet: RefObject<RBSheet>;
  recurrences: Event[];
  onJoinEvent: (event_id: number, isLeave: boolean) => void;
}

type MonthItem = {
  month: string;
  year: string;
  monthNumeric: string;
};

type DayItem = {
  day: number;
  active: boolean;
  recurrence: {date: string; times: Recurrence[]};
};

const deviceWidth = Dimensions.get('window').width;
const cellWidth = (deviceWidth - 75) / 7;

const Calendar = ({refRBSheet, recurrences, onJoinEvent}: Props) => {
  const {colors} = useTheme();
  const styles = useThemedStyles(createStyles);
  const [month, setMonth] = useState(new Date().toLocaleString('default', {month: 'long'}));
  const [year, setYear] = useState(moment(new Date()).format('YYYY'));
  const [monthArray, setMonthArray] = useState<Array<MonthItem>>([]);
  const [daysArray, setDaysArray] = useState<Array<DayItem>>([]);
  const [dateSelect, setDateSelect] = useState<DayItem>();
  const [selectedId, setSelectedId] = useState<number>();
  const [isSubscribed, setIsSubScribed] = useState(false);

  const getMonthNames = (date = new Date()) => {
    let d = new Date(+date);
    d.setDate(1);
    let result = [];
    for (let i = 0; i < 12; i++) {
      result.push({
        month: d.toLocaleString('default', {month: 'long'}),
        monthNumeric: d.toLocaleString('default', {month: 'numeric'}),
        year: moment(d).format('YYYY'),
      });
      d.setMonth(d.getMonth() + 1);
    }
    onMonthSelect(result[0]);
    setMonthArray(result);
  };

  const daysInMonth = (month: string, year: string) => {
    const groupByDateArray = groupByDate(recurrences);
    const dateMonth = moment(`${year}-${month}`, 'YYYY-MM');
    const totalDays = dateMonth.daysInMonth();
    const date = `01-${minTwoDigits(Number(month))}-${year}`;
    const monthDay = moment(date, 'DD-MM-YYYY').day();

    const daysArray = new Array(monthDay).fill({day: ''});

    for (let index = 1; index <= totalDays; index++) {
      const date = minTwoDigits(index);
      const dateString = `${year}-${minTwoDigits(Number(month))}-${date}`;
      const isActive = groupByDateArray?.filter(
        (item: {date: string; times: Recurrence[]}) => item.date === dateString,
      );
      daysArray.push({
        day: index + '',
        active: isActive?.length > 0,
        recurrence: isActive[0],
      });
    }
    return daysArray;
  };

  const minTwoDigits = (n: Number) => {
    return ((n as number) < 10 ? '0' : '') + n;
  };

  useEffect(() => {
    getMonthNames();
  }, [recurrences]);

  const onMonthSelect = (selectMonth: MonthItem) => {
    setMonth(selectMonth.month);
    setYear(selectMonth.year);
    const daysArray = daysInMonth(selectMonth.monthNumeric, year);
    setDaysArray(daysArray);
  };

  const onDatePress = (item: DayItem) => {
    setDateSelect(item);
  };

  const onApplyClick = () => {
    if (!dateSelect || !dateSelect.day) {
      Alert.alert('Select date to join.');
      return;
    }

    if (!selectedId) {
      Alert.alert('Select slot to join.');
      return;
    }
    onJoinEvent(selectedId, isSubscribed);
    refRBSheet.current.close();
  };

  return (
    <RBSheet
      ref={refRBSheet}
      useNativeDriver={false}
      customStyles={{
        wrapper: styles.wrapperStyle,
        container: styles.bottomSheetStyle,
      }}
      customModalProps={{
        animationType: 'slide',
        statusBarTranslucent: true,
      }}
      height={450}
      customAvoidingViewProps={{
        enabled: false,
      }}>
      <View style={styles.bottomSheetStyle}>
        {dateSelect && dateSelect.day ? (
          <View style={styles.headerTime}>
            <TouchableOpacity onPress={() => setDateSelect({})}>
              <ChevronIcon />
            </TouchableOpacity>
            <Text style={[styles.calendarTitle, {marginStart: 10}]}>Select event time</Text>
          </View>
        ) : (
          <Text style={styles.calendarTitle}>Select event date</Text>
        )}
        {dateSelect && dateSelect.day ? (
          <View>
            <Text style={styles.recurenceDateTitle}>{dateSelect.recurrence.date}</Text>
            <FlatList
              data={dateSelect.recurrence.times}
              style={{height: 230}}
              renderItem={({item}) => {
                return (
                  <TouchableOpacity
                    onPress={() => {
                      setSelectedId(item.event_id);
                      setIsSubScribed(item.subscribed);
                    }}
                    disabled={!!item.is_cancelled}
                    style={[
                      styles.slotItem,
                      item.event_id === selectedId && {borderColor: colors.eventInfluencer, borderWidth: 2},
                    ]}>
                    {item.is_cancelled ? (
                      <View>
                        <Text>
                          {moment(item.start_date).format('hh:mm a')} - {moment(item.end_date).format('hh:mm a')}
                        </Text>
                        <Text style={{color: colors.error, marginTop: 5}}>
                          This event was cancelled by the organiser
                        </Text>
                      </View>
                    ) : (
                      <Text>
                        {moment(item.start_date).format('hh:mm a')} - {moment(item.end_date).format('hh:mm a')}
                      </Text>
                    )}
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        ) : (
          <>
            <FlatList
              data={monthArray}
              bounces={false}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.monthList}
              renderItem={({item}) => {
                return (
                  <TouchableOpacity
                    onPress={() => onMonthSelect(item)}
                    style={[styles.monthTextTag, month === item.month && styles.selectMonth]}>
                    <Text style={[styles.monthText, month === item.month && styles.selectMonthText]}>{item.month}</Text>
                  </TouchableOpacity>
                );
              }}
            />
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>S</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>M</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>T</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>W</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>T</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>F</Text>
              </View>
              <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: colors.statusGray}]}>
                <Text style={styles.textDay}>S</Text>
              </View>
            </View>
            <FlatList
              data={daysArray}
              showsVerticalScrollIndicator={false}
              style={styles.dayList}
              numColumns={7}
              bounces={false}
              renderItem={({item}) => {
                if (!item.day) {
                  return <View style={[styles.datTextTag, {borderWidth: 0, backgroundColor: 'transparent'}]} />;
                }
                return (
                  <TouchableOpacity
                    onPress={() => onDatePress(item)}
                    style={[
                      styles.datTextTag,
                      item.active ? {backgroundColor: colors.eventInfluencer} : {backgroundColor: colors.gray100},
                    ]}
                    disabled={!item.active}>
                    <Text
                      style={[styles.monthText, item.active ? {color: colors.white} : {color: colors.textTertiary}]}>
                      {minTwoDigits(item.day)}
                    </Text>
                  </TouchableOpacity>
                );
              }}
            />
          </>
        )}
        <Button
          label={isSubscribed ? 'Leave' : 'Continue'}
          containerStyle={styles.applyBtn}
          textStyle={styles.textStyle}
          onPress={onApplyClick}
        />
      </View>
    </RBSheet>
  );
};

export default Calendar;

const createStyles = (colors: any) =>
  StyleSheet.create({
    textDay: {
      color: colors.white,
    },
    slotItem: {
      marginTop: 15,
      flexDirection: 'row',
      padding: 12,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 10,
      marginEnd: 20,
      backgroundColor: colors.surface,
    },
    recurenceDateTitle: {
      fontWeight: 'bold',
      fontSize: 16,
      marginTop: 20,
      color: colors.textPrimary,
    },
    headerTime: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    textStyle: {
      color: colors.white,
    },
    applyBtn: {
      backgroundColor: colors.secondary,
      width: deviceWidth - 40,
      marginTop: 25,
      borderRadius: 27.5,
    },
    dayList: {
      marginTop: 5,
    },
    selectMonthText: {
      color: colors.white,
    },
    monthText: {
      fontWeight: '400',
      fontSize: 15,
      lineHeight: 18,
      color: colors.textPrimary,
    },
    selectMonth: {
      backgroundColor: colors.statusGray,
    },
    datTextTag: {
      borderColor: colors.border,
      borderWidth: 1,
      marginEnd: 6,
      borderRadius: 56,
      width: cellWidth,
      height: cellWidth * 0.8375,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 6,
    },
    monthTextTag: {
      paddingHorizontal: 12,
      paddingVertical: 9,
      borderColor: colors.border,
      borderWidth: 1,
      marginEnd: 6,
      borderRadius: 56,
      backgroundColor: colors.surface,
    },
    monthList: {
      marginTop: 15,
    },
    wrapperStyle: {
      backgroundColor: colors.overlayBackground,
    },
    bottomSheetStyle: {
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      paddingVertical: 10,
      paddingStart: 10,
      backgroundColor: colors.surface,
    },
    calendarTitle: {
      fontWeight: '700',
      color: colors.textPrimary,
      fontSize: 17,
      lineHeight: 22,
    },
  });
