import {useEffect, useState} from 'react';
import auth from '@react-native-firebase/auth';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';

/**
 * Hook to track loading state after successful login while user data is being fetched
 */
export const usePostLoginLoading = () => {
  const [isPostLoginLoading, setIsPostLoginLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(!!auth().currentUser);

  const userId = auth().currentUser?.uid || '';
  const {isLoading: userTypeIsLoading, data: userType} = useGetUserType(userId);
  const {isLoading: userAccountIsLoading, data: userData} = useGetUserAccount(userId);
  const {isLoading: businessIsLoading, data: businessData} = useGetBusinessAccount(userId);

  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged(user => {
      if (user && user.emailVerified) {
        // User just logged in successfully
        setIsAuthenticated(true);
        setIsPostLoginLoading(true);
      } else {
        // User logged out or not verified
        setIsAuthenticated(false);
        setIsPostLoginLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      setIsPostLoginLoading(false);
      return;
    }

    // Check if all necessary data has been loaded
    const isStillLoading =
      userTypeIsLoading ||
      !userType ||
      (userType === 'business' && (businessIsLoading || !businessData)) ||
      (userType === 'personal' && (userAccountIsLoading || !userData));

    if (!isStillLoading) {
      // All data loaded, stop post-login loading after a small delay
      const timer = setTimeout(() => {
        setIsPostLoginLoading(false);
      }, 300); // Small delay to ensure smooth transition

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, userTypeIsLoading, userAccountIsLoading, businessIsLoading, userType, userData, businessData]);

  return {
    isPostLoginLoading,
    isAuthenticated,
  };
};
