import {useCallback, useEffect} from 'react';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {Alert} from 'react-native';
import dynamicLinks, {FirebaseDynamicLinksTypes} from '@react-native-firebase/dynamic-links';
import auth from '@react-native-firebase/auth';
import {NavigationProps} from '~types/navigation/navigation.type';

const useDeepLinking = () => {
  const navigation = useNavigation<NavigationProps>();
  const {refetch} = useGetUserAccount(auth().currentUser!.uid);
  const openEvent = useCallback(
    async (eventID: string) => {
      const data = await refetch();
      if (data?.data) {
        navigation.navigate(SCREENS.HOME_STACK, {eventId: Number(eventID)});
        return;
      }
      Alert.alert('No permission to event');
    },
    [navigation, refetch],
  );
  const handleDynamicLink = useCallback(
    async (link: FirebaseDynamicLinksTypes.DynamicLink) => {
      const parts = link.url.split('/');
      const eventID = parts[parts.length - 1];
      openEvent(eventID);
    },
    [openEvent],
  );
  useEffect(() => {
    const unsubscribe = dynamicLinks().onLink(handleDynamicLink);
    return () => unsubscribe();
  }, [handleDynamicLink]);

  const androidDeepLinking = useCallback(async () => {
    const initialLink = await dynamicLinks().getInitialLink();
    if (!initialLink) {
      return;
    }

    handleDynamicLink(initialLink);
  }, [handleDynamicLink]);
  useEffect(() => {
    // if (!isAndroid) {
    //   return;
    // }

    androidDeepLinking();
  }, [androidDeepLinking]);
};

export default useDeepLinking;
