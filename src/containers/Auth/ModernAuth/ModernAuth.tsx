import React, {useState, useEffect} from 'react';
import {View, Text, ScrollView, TouchableOpacity, StatusBar, useWindowDimensions, Platform} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {Formik} from 'formik';
import * as yup from 'yup';
import {PyxiLabel} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernCard from '~components/ModernCard/ModernCard';
import SocialButtons from '~components/SocialButtons';
import {FadeIn, SlideIn} from '~components/MicroInteractions/MicroInteractions';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';
import {useLanguage} from '~containers/Core/navigation/SettingsStack/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import ModernSpinner from '~components/ModernSpinner';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useUserStore} from '~providers/userStore/zustand';
import auth from '@react-native-firebase/auth';

const loginValidationSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const signupValidationSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
});

const ModernAuth: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();
  const isFocused = useIsFocused();
  const {language} = useLanguage();
  const {setUser} = useUserStore();

  const [isLogin, setIsLogin] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);
  const [isPostLoginLoading, setIsPostLoginLoading] = useState(false);

  // Get current user ID
  const userId = auth().currentUser?.uid || '';

  // Data fetching hooks
  const {data: userType, isLoading: userTypeIsLoading} = useGetUserType(userId);
  const {data: userData, isLoading: userAccountIsLoading} = useGetUserAccount(userId);
  const {data: businessData, isLoading: businessIsLoading} = useGetBusinessAccount(userId);

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  // Handle authentication state changes and navigation
  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged(user => {
      if (user && user.emailVerified) {
        console.log('🔄 [DEBUG] User authenticated in ModernAuth, starting post-login loading');
        setIsPostLoginLoading(true);
      } else {
        setIsPostLoginLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  // Handle navigation after data is loaded
  useEffect(() => {
    if (!isPostLoginLoading) return;

    // Check if all necessary data has been loaded
    const isStillLoading =
      userTypeIsLoading ||
      !userType ||
      (userType === 'business' && (businessIsLoading || !businessData)) ||
      (userType === 'personal' && (userAccountIsLoading || !userData));

    if (!isStillLoading) {
      console.log('🔄 [DEBUG] All data loaded in ModernAuth, making navigation decision:', {
        userType,
        userData,
        businessData,
        hasOnboardingAnswers:
          userType === 'business'
            ? (businessData?.onboarding_answers?.length || 0) > 0
            : (userData?.onboarding_answers?.length || 0) > 0,
        isRegistrationFinished:
          userType === 'business' ? businessData?.is_registration_finished : userData?.is_registration_finished,
      });

      // Set user data in store
      if (userType === 'personal' && userData) {
        setUser(userData);
      }

      // Make navigation decision
      const currentUserData = userType === 'business' ? businessData : userData;
      const hasOnboardingAnswers = (currentUserData?.onboarding_answers?.length || 0) > 0;
      const isRegistrationFinished = currentUserData?.is_registration_finished;

      setTimeout(() => {
        setIsPostLoginLoading(false);

        if (!hasOnboardingAnswers) {
          console.log('📱 [DEBUG] No onboarding answers, navigating to questions');
          navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        } else if (!isRegistrationFinished) {
          console.log('📱 [DEBUG] Registration not finished, navigating to onboarding');
          if (!currentUserData?.photo) {
            navigation.navigate(SCREENS.ONBOARDING_PERSONAL_INFO);
          } else {
            navigation.navigate(SCREENS.ONBOARDING_GROUP);
          }
        } else {
          console.log(
            '📱 [DEBUG] User fully set up, should navigate to home - but navigation is limited from auth screen',
          );
          // Since we can't navigate to APP_ROOT from auth screen, we'll let the main navigation handle this
        }
      }, 300);
    }
  }, [
    isPostLoginLoading,
    userTypeIsLoading,
    userAccountIsLoading,
    businessIsLoading,
    userType,
    userData,
    businessData,
    navigation,
    setUser,
  ]);

  useEffect(() => {
    logScreenView('ModernAuth', isLogin ? 'Login' : 'Signup');
  }, [isLogin]);

  const handleSubmit = async (values: any) => {
    setIsLoading(true);
    try {
      if (isLogin) {
        await FirebaseAuth.signInEmailPassword(values.email, values.password, setIsLoading);
      } else {
        await FirebaseAuth.signUpEmailPassword(values.email, values.password, setIsLoading);
      }
    } catch (error) {
      console.error('Auth error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      minHeight: height,
    },
    heroSection: {
      height: height * 0.35,
      position: 'relative' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      overflow: 'hidden' as const,
    },
    heroGradient: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100%',
      height: '100%',
    },
    heroContent: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 2,
      paddingHorizontal: spacing.lg,
    },
    languageButton: {
      position: 'absolute' as const,
      top: spacing.lg,
      right: spacing.lg,
      backgroundColor: colors.textInverse + '15',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: borderRadius['2xl'],
      borderWidth: 1.5,
      borderColor: colors.textInverse + '25',
      zIndex: 3,
      ...shadows.md,
      minWidth: 60,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    languageText: {
      color: colors.textInverse,
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      letterSpacing: 0.5,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: spacing.xl,
      transform: [{scale: 1.4}],
    },
    welcomeText: {
      fontSize: typography.fontSize['4xl'],
      fontWeight: typography.fontWeight.black,
      color: colors.textInverse,
      textAlign: 'center' as const,
      marginTop: spacing.md,
      letterSpacing: -0.5,
      textShadowColor: colors.black + '20',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
    },
    subtitleText: {
      fontSize: typography.fontSize.xl,
      color: colors.textInverse,
      textAlign: 'center' as const,
      opacity: 0.95,
      marginTop: spacing.sm,
      fontWeight: typography.fontWeight.semibold,
      letterSpacing: 0.2,
      textShadowColor: colors.black + '15',
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 2,
    },
    formSection: {
      flex: 1,
      paddingHorizontal: 0,
      paddingTop: spacing.xl,
    },
    tabContainer: {
      flexDirection: 'row' as const,
      backgroundColor: colors.surface + '90',
      borderRadius: borderRadius['3xl'],
      padding: spacing.sm,
      marginBottom: spacing.xl,
      ...shadows.none,
      borderWidth: 0,
    },
    tabButton: {
      flex: 1,
      paddingVertical: spacing.lg,
      borderRadius: borderRadius['2xl'],
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginHorizontal: spacing.xs,
    },
    activeTab: {
      backgroundColor: colors.primary,
      ...shadows.none,
      transform: [{scale: 1.02}],
    },
    inactiveTab: {
      backgroundColor: 'transparent',
      transform: [{scale: 1}],
    },
    tabText: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      letterSpacing: 0.5,
    },
    activeTabText: {
      color: colors.white,
      textShadowColor: colors.black + '30',
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 2,
    },
    inactiveTabText: {
      color: colors.textSecondary,
    },
    formCard: {
      marginBottom: spacing.lg,
    },
    inputContainer: {
      marginBottom: spacing.md,
    },
    forgotPasswordContainer: {
      alignItems: 'flex-end' as const,
      marginBottom: spacing.lg,
    },
    forgotPasswordText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.primary,
    },
    dividerContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginVertical: spacing.lg,
    },
    divider: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginHorizontal: spacing.md,
      fontWeight: typography.fontWeight.medium,
    },
    bottomSection: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.xl,
      alignItems: 'center' as const,
    },
    switchContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginTop: spacing.lg,
    },
    switchText: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      fontWeight: typography.fontWeight.normal,
    },
    switchButton: {
      marginLeft: spacing.xs,
    },
    switchButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.primary,
    },
    loadingOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.background + 'E6', // Semi-transparent background
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 1000,
    },
    loadingContainer: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...shadows.lg,
      minWidth: 160,
    },
    loadingText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textPrimary,
      marginTop: spacing.md,
      textAlign: 'center' as const,
    },
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {/* Hero Section */}
        <FadeIn duration={600}>
          <View style={styles.heroSection}>
            <LinearGradient
              colors={[colors.primary, colors.primaryLight, colors.warning, colors.warningLight]}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 1}}
              style={styles.heroGradient}
            />

            <TouchableOpacity style={styles.languageButton} onPress={() => setIsLanguagesModalVisible(true)}>
              <Text style={styles.languageText}>{language.toUpperCase()}</Text>
            </TouchableOpacity>

            <View style={styles.heroContent}>
              <FadeIn delay={300} duration={800}>
                <View style={styles.logoContainer}>
                  <PyxiLabel />
                </View>
              </FadeIn>

              <FadeIn delay={500} duration={800}>
                <Text style={styles.welcomeText}>Welcome to Pyxi</Text>
                <Text style={styles.subtitleText}>Discover amazing events around you</Text>
              </FadeIn>
            </View>
          </View>
        </FadeIn>

        {/* Form Section */}
        <View style={styles.formSection}>
          <FadeIn delay={200} duration={600}>
            {/* Tab Switcher */}
            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[styles.tabButton, isLogin ? styles.activeTab : styles.inactiveTab]}
                onPress={() => setIsLogin(true)}>
                <Text style={[styles.tabText, isLogin ? styles.activeTabText : styles.inactiveTabText]}>Sign In</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tabButton, !isLogin ? styles.activeTab : styles.inactiveTab]}
                onPress={() => setIsLogin(false)}>
                <Text style={[styles.tabText, !isLogin ? styles.activeTabText : styles.inactiveTabText]}>Sign Up</Text>
              </TouchableOpacity>
            </View>

            <ModernCard variant="elevated" padding="xl" style={styles.formCard}>
              <Formik
                initialValues={isLogin ? {email: '', password: ''} : {email: '', password: '', confirmPassword: ''}}
                onSubmit={handleSubmit}
                validationSchema={isLogin ? loginValidationSchema : signupValidationSchema}
                enableReinitialize>
                {formikProps => (
                  <>
                    <SlideIn direction="up" delay={100} duration={400}>
                      <View style={styles.inputContainer}>
                        <ModernTextInput
                          label={t('signin.email')}
                          placeholder="Enter your email"
                          value={formikProps.values.email}
                          onChangeText={formikProps.handleChange('email')}
                          errorText={formikProps.touched.email && formikProps.errors.email}
                          keyboardType="email-address"
                          autoCapitalize="none"
                          autoCorrect={false}
                          variant="outlined"
                          size="lg"
                        />
                      </View>
                    </SlideIn>

                    <SlideIn direction="up" delay={200} duration={400}>
                      <View style={styles.inputContainer}>
                        <ModernTextInput
                          label={t('signin.password')}
                          placeholder="Enter your password"
                          value={formikProps.values.password}
                          onChangeText={formikProps.handleChange('password')}
                          errorText={formikProps.touched.password && formikProps.errors.password}
                          isPassword
                          variant="outlined"
                          size="lg"
                        />
                      </View>
                    </SlideIn>

                    {!isLogin && (
                      <SlideIn direction="up" delay={300} duration={400}>
                        <View style={styles.inputContainer}>
                          <ModernTextInput
                            label="Confirm Password"
                            placeholder="Confirm your password"
                            value={formikProps.values.confirmPassword}
                            onChangeText={formikProps.handleChange('confirmPassword')}
                            errorText={formikProps.touched.confirmPassword && formikProps.errors.confirmPassword}
                            isPassword
                            variant="outlined"
                            size="lg"
                          />
                        </View>
                      </SlideIn>
                    )}

                    {isLogin && (
                      <View style={styles.forgotPasswordContainer}>
                        <TouchableOpacity onPress={() => navigation.navigate(SCREENS.FORGOT_PASSWORD)}>
                          <Text style={styles.forgotPasswordText}>{t('signin.forgot_pwd_header')}</Text>
                        </TouchableOpacity>
                      </View>
                    )}

                    <ModernButton
                      onPress={formikProps.handleSubmit}
                      title={isLogin ? t('signin.login') : 'Create Account'}
                      variant="primary"
                      size="lg"
                      fullWidth
                      loading={isLoading}
                      hapticFeedback
                    />

                    <View style={styles.dividerContainer}>
                      <View style={styles.divider} />
                      <Text style={styles.dividerText}>or</Text>
                      <View style={styles.divider} />
                    </View>

                    <SocialButtons />
                  </>
                )}
              </Formik>
            </ModernCard>
          </FadeIn>
        </View>
      </ScrollView>

      <LanguagesModal
        isVisible={isLanguagesModalVisible}
        onClose={() => setIsLanguagesModalVisible(false)}
        onSelect={() => setIsLanguagesModalVisible(false)}
      />

      {/* Post-login loading overlay */}
      {isPostLoginLoading && isLogin && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ModernSpinner size={40} variant="gradient" />
            <Text style={styles.loadingText}>{t('loading.fetching_data')}</Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default ModernAuth;
