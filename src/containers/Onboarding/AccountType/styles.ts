import {StyleSheet, Platform, Dimensions} from 'react-native';
const width = Dimensions.get('window').width;

const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: 30,
      width: '100%',
    },
    titleText: {
      textAlign: 'center',
      marginTop: Platform.OS === 'ios' ? 40 : 0,
      fontSize: 18,
      fontWeight: '500',
      color: colors.textPrimary,
    },
    personalButton: {
      marginTop: Platform.OS === 'ios' ? 24 : 12,
      paddingHorizontal: 14,
      paddingVertical: 8,
      borderRadius: 6,
    },
    image: {height: (width - 88) * 0.65, width: width - 88},
    subtitleWrapper: {
      marginTop: 4,
      justifyContent: 'flex-start',
    },
    subtitleMainText: {color: colors.textPrimary, fontWeight: '700', fontSize: 17},
    subtitleHelpText: {color: colors.textPrimary, fontWeight: '400', fontSize: 12},
    businessButton: {
      marginTop: 8,
      paddingHorizontal: 14,
      paddingVertical: 8,
      borderRadius: 6,
    },
    bottomButtonWrapper: {
      width: '100%',
      flex: 1,
      marginTop: 10,
      justifyContent: 'flex-end',
      // marginBottom: bottom + 20,
    },
    bottomButton: {
      width: '100%',
      height: 40,
      borderRadius: 6,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomButtonText: {fontSize: 15, fontWeight: '500', color: colors.white},
  });

export default getStyles;
