import {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import SelectTypeOfProfile from '~containers/Onboarding/Group';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import {useTranslation} from 'react-i18next';
import {GoBackHeader} from '~components/GoBackHeader';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {logScreenView} from '~Utils/firebaseAnalytics';
import React from 'react';
import {useTheme} from '~contexts/ThemeContext';

const CreateEventGroups = () => {
  const {colors} = useTheme();
  const {setGroups} = useEventCreationStore();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.CREATE_EVENT_GROUPS>>();

  useEffect(() => {
    logScreenView('Create Event Groups', 'CreateEventGroups');
  }, []);

  return (
    <>
      <GoBackHeader isGradientShow={false} />
      <View style={{flex: 1, backgroundColor: colors.background, paddingTop: 80}}>
        <View style={{width: '100%', alignItems: 'flex-start', paddingHorizontal: 16}}>
          <Text
            style={{
              textAlign: 'left',
              lineHeight: 41,
              fontSize: 34,
              color: colors.textPrimary,
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('events.event_groups')}
          </Text>
        </View>
        <SelectTypeOfProfile
          withoutLabel
          isModal
          onSubmit={values => {
            setGroups(values);
            navigation.navigate(
              SCREENS.CREATE_EVENT_SUBMITTING,
              route.params?.item ? {item: route.params?.item} : undefined,
            );
          }}
        />
      </View>
    </>
  );
};

export default CreateEventGroups;
