import React, {useState} from 'react';
import {View, Text, TouchableOpacity, Modal, ScrollView} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

type Props = {
  // startDate: Date;
  // endDate: Date;
  // onApply: (selectedStartDate: Date, selectedEndDate: Date) => void;      onApply
};

const ModalWithCalendar: React.FC<Props> = ({startDate, endDate}) => {
  const {colors} = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const generateDaysArray = (month: number, year: number) => {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    return Array.from({length: daysInMonth}, (_, i) => i + 1);
  };

  const handleDayPress = (day: number, month: number, year: number) => {
    const selectedDate = new Date(year, month, day);
    if (!selectedStartDate || (selectedStartDate && selectedEndDate)) {
      setSelectedStartDate(selectedDate);
      setSelectedEndDate(null);
    } else {
      setSelectedEndDate(selectedDate);
    }
  };

  const renderDays = (month: number, year: number) => {
    const daysArray = generateDaysArray(month, year);
    return (
      <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
        {daysArray.map(day => (
          <TouchableOpacity
            key={day}
            style={{
              width: 40,
              height: 40,
              justifyContent: 'center',
              alignItems: 'center',
              margin: 5,
              borderRadius: 20,
              borderWidth: 1,
              borderColor: colors.border,
              backgroundColor:
                selectedStartDate &&
                selectedStartDate.getDate() === day &&
                selectedStartDate.getMonth() === month &&
                selectedStartDate.getFullYear() === year
                  ? 'orange'
                  : selectedEndDate &&
                      selectedEndDate.getDate() === day &&
                      selectedEndDate.getMonth() === month &&
                      selectedEndDate.getFullYear() === year
                    ? 'orange'
                    : colors.white,
            }}
            onPress={() => handleDayPress(day, month, year)}>
            <Text style={{fontSize: 14}}>{day}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <TouchableOpacity onPress={() => setIsVisible(true)}>
        <Text>Open Calendar</Text>
      </TouchableOpacity>
      <Modal visible={isVisible} transparent={true} animationType="slide">
        <View style={{flex: 1, justifyContent: 'flex-end'}}>
          <View
            style={{
              backgroundColor: colors.white,
              padding: 20,
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              maxHeight: '50%',
            }}>
            <Text style={{fontSize: 18, fontWeight: 'bold', marginBottom: 10}}>Select event date</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {months.map((month, index) => (
                <View key={month}>
                  <Text style={{fontSize: 16, fontWeight: '600', marginVertical: 10}}>{month}</Text>
                  {renderDays(index, startDate.getFullYear())}
                </View>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={{
                backgroundColor: colors.secondary,
                borderRadius: 20,
                paddingVertical: 10,
                alignItems: 'center',
                marginTop: 20,
              }}
              onPress={() => {
                // onApply(selectedStartDate, selectedEndDate);
                console.log('selectedStartDate', selectedStartDate);
                console.log('selectedEndDate', selectedEndDate);
                setIsVisible(false);
              }}>
              <Text style={{color: colors.white, fontSize: 16, fontWeight: 'bold'}}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ModalWithCalendar;
