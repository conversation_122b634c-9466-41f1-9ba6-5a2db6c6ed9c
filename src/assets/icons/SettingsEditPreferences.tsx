import {Path, Rect, Svg} from 'react-native-svg';

const svgSize = 30;
const pathWidth = 24;
const pathHeight = 24;
const x = (svgSize - pathWidth) / 2;
const y = (svgSize - pathHeight - 5) / 2;

const SettingsEditPreferences = () => (
  <Svg width={svgSize} height={svgSize} viewBox="0 0 30 30" fill="none">
    <Rect width="30" height="30" rx="7" fill="#4A48AD" />
    <Path
      fill="white"
      d="M5 20.71V24h3.29l9.73-9.73-3.29-3.29L5 20.71zM24.71 9.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a1.003 1.003 0 0 0-1.42 0l-2.04 2.04 3.75 3.75 2.05-2.04z"
      x={x}
      y={y}
    />
  </Svg>
);

export default SettingsEditPreferences;
