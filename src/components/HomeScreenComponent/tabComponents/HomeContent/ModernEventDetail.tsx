import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Share as RNShare,
  Linking,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  FadeInDown,
  FadeInUp,
  SlideInRight,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment-timezone';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import {Event} from '~types/api/event';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius} from '~constants';
import {haptics} from '~utils/haptics';
import {useTranslation} from 'react-i18next';
import LikeButton from '~components/LikeButton';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const HEADER_HEIGHT = 300;

interface ModernEventDetailProps {
  event: Event;
  onClose: () => void;
  sheetPosition?: number;
}

const ModernEventDetail: React.FC<ModernEventDetailProps> = ({event, onClose, sheetPosition = 0}) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const {setIsTabBarDisabled} = useTabBar();
  const scrollY = useSharedValue(0);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  const styles = createStyles(colors);

  // Animated header style
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [1, 0], Extrapolate.CLAMP);

    const scale = interpolate(scrollY.value, [0, HEADER_HEIGHT], [1, 1.1], Extrapolate.CLAMP);

    return {
      opacity,
      transform: [{scale}],
    };
  });

  // Animated overlay style
  const overlayAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, HEADER_HEIGHT - 100], [0, 1], Extrapolate.CLAMP);

    return {
      opacity,
    };
  });

  const formatEventDate = () => {
    const startDate = moment.utc(event.start_date);
    const endDate = moment.utc(event.end_date);

    if (startDate.isSame(endDate, 'day')) {
      return {
        date: startDate.format('ddd, MMM DD'),
        time: `${startDate.format('h:mm A')} - ${endDate.format('h:mm A')}`,
      };
    }

    return {
      date: `${startDate.format('MMM DD')} - ${endDate.format('MMM DD')}`,
      time: startDate.format('h:mm A'),
    };
  };

  const {date, time} = formatEventDate();

  const handleShare = async () => {
    haptics.selection();
    try {
      const url = `https://partner.pyxi.ai/event/detail/${event.event_id}`;
      await RNShare.share({
        message: `Check out this event: ${event.name}\n${url}`,
        url,
      });
    } catch (error) {
      console.log('Error sharing:', error);
    }
  };

  const handleDirections = () => {
    haptics.buttonPress();
    const url = `https://maps.google.com/?q=${encodeURIComponent(event.address_name)}`;
    Linking.openURL(url);
  };

  const getEventTypeColor = () => {
    switch (event.event_type) {
      case 'business':
        return colors.secondary;
      case 'influencer':
        return '#F5A865';
      case 'community':
        return '#10B981';
      case 'pyxi_select':
        return '#FF6B6B';
      default:
        return colors.primary;
    }
  };

  const getEventTypeLabel = () => {
    switch (event.event_type) {
      case 'business':
        return 'Business Event';
      case 'influencer':
        return 'Influencer Event';
      case 'community':
        return 'Community Event';
      case 'pyxi_select':
        return 'Pyxi Select';
      default:
        return 'Event';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header Image */}
      <Animated.View style={[styles.headerContainer, headerAnimatedStyle]}>
        <FastImage source={{uri: event.image_url}} style={styles.headerImage} resizeMode="cover" />
        <View style={styles.headerOverlay} />

        {/* Header Actions */}
        <View style={[styles.headerActions, {top: top + 10}]}>
          <TouchableOpacity onPress={onClose} style={styles.actionButton} activeOpacity={0.8}>
            <Ionicons name="close" size={24} color={colors.white} />
          </TouchableOpacity>

          <View style={styles.rightActions}>
            <TouchableOpacity onPress={handleShare} style={styles.actionButton} activeOpacity={0.8}>
              <Ionicons name="share-outline" size={24} color={colors.white} />
            </TouchableOpacity>

            <View style={styles.actionButton}>
              <LikeButton liked={event.user_liked} eventId={event.event_id} />
            </View>
          </View>
        </View>

        {/* Event Type Badge */}
        <Animated.View
          style={[styles.eventTypeBadge, {backgroundColor: getEventTypeColor()}]}
          entering={FadeInDown.delay(200)}>
          <Text style={styles.eventTypeBadgeText}>{getEventTypeLabel()}</Text>
        </Animated.View>
      </Animated.View>

      {/* Sticky Header Overlay */}
      <Animated.View style={[styles.stickyHeader, overlayAnimatedStyle, {top}]}>
        <Text style={styles.stickyTitle} numberOfLines={1}>
          {event.name}
        </Text>
      </Animated.View>

      {/* Content */}
      <Animated.ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        onScroll={e => {
          scrollY.value = e.nativeEvent.contentOffset.y;
        }}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}>
        {/* Main Content Card */}
        <Animated.View style={styles.contentCard} entering={FadeInUp.delay(100)}>
          {/* Event Title */}
          <Text style={styles.eventTitle}>{event.name}</Text>

          {/* Host Info */}
          <Animated.View style={styles.hostSection} entering={SlideInRight.delay(200)}>
            <Text style={styles.hostLabel}>Hosted by</Text>
            <Text style={styles.hostName}>{event.host_name || 'Event Host'}</Text>
          </Animated.View>

          {/* Date & Time Section */}
          <Animated.View style={styles.infoSection} entering={FadeInDown.delay(300)}>
            <View style={styles.infoRow}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="event" size={24} color={getEventTypeColor()} />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Date & Time</Text>
                <Text style={styles.infoText}>{date}</Text>
                <Text style={styles.infoSubtext}>{time}</Text>
              </View>
            </View>
          </Animated.View>

          {/* Location Section */}
          <Animated.View style={styles.infoSection} entering={FadeInDown.delay(400)}>
            <View style={styles.infoRow}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="location-on" size={24} color={getEventTypeColor()} />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Location</Text>
                <Text style={styles.infoText}>{event.address_name}</Text>
                <TouchableOpacity onPress={handleDirections} style={styles.directionsButton}>
                  <Text style={[styles.directionsText, {color: getEventTypeColor()}]}>Get Directions</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>

          {/* Attendees Section */}
          <Animated.View style={styles.infoSection} entering={FadeInDown.delay(500)}>
            <View style={styles.infoRow}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="people" size={24} color={getEventTypeColor()} />
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Attendees</Text>
                <Text style={styles.infoText}>{event.total_attendee_count || 0} people attending</Text>
              </View>
            </View>
          </Animated.View>

          {/* Price Section */}
          {event.is_paid && (
            <Animated.View style={styles.infoSection} entering={FadeInDown.delay(600)}>
              <View style={styles.infoRow}>
                <View style={styles.iconContainer}>
                  <MaterialIcons name="attach-money" size={24} color={getEventTypeColor()} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoTitle}>Price</Text>
                  <Text style={styles.infoText}>Paid Event</Text>
                </View>
              </View>
            </Animated.View>
          )}

          {/* Description Section */}
          <Animated.View style={styles.descriptionSection} entering={FadeInDown.delay(700)}>
            <Text style={styles.sectionTitle}>About this event</Text>
            <Text style={styles.descriptionText} numberOfLines={isDescriptionExpanded ? undefined : 3}>
              {event.description || 'No description available.'}
            </Text>
            {event.description && event.description.length > 150 && (
              <TouchableOpacity
                onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                style={styles.expandButton}>
                <Text style={[styles.expandText, {color: getEventTypeColor()}]}>
                  {isDescriptionExpanded ? 'Show less' : 'Show more'}
                </Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={{height: 100}} />
      </Animated.ScrollView>

      {/* Join Button */}
      <Animated.View style={styles.joinButtonContainer} entering={FadeInUp.delay(800)}>
        <TouchableOpacity
          style={[styles.joinButton, {backgroundColor: getEventTypeColor()}]}
          onPress={() => {
            haptics.buttonPress();
            if (event.is_paid) {
              // Navigate to ticket purchase
              setIsTabBarDisabled(true);
              navigation.navigate(SCREENS.BUY_TICKET, {
                eventId: event.event_id,
                item: event,
              });
            } else {
              // Handle free event join logic
              console.log('Joining free event:', event.event_id);
            }
          }}
          activeOpacity={0.9}>
          <Text style={styles.joinButtonText}>{event.is_paid ? 'Get Tickets' : 'Join Event'}</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    headerContainer: {
      height: HEADER_HEIGHT,
      position: 'relative',
    },
    headerImage: {
      width: '100%',
      height: '100%',
    },
    headerOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    headerActions: {
      position: 'absolute',
      left: spacing.md,
      right: spacing.md,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    rightActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
    },
    actionButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      alignItems: 'center',
      justifyContent: 'center',
    },
    eventTypeBadge: {
      position: 'absolute',
      bottom: spacing.lg,
      left: spacing.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.full,
    },
    eventTypeBadgeText: {
      color: colors.white,
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    stickyHeader: {
      position: 'absolute',
      left: 0,
      right: 0,
      height: 60,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacing.lg,
      zIndex: 10,
    },
    stickyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingTop: HEADER_HEIGHT - 40,
    },
    contentCard: {
      backgroundColor: colors.surface,
      borderTopLeftRadius: borderRadius.xl,
      borderTopRightRadius: borderRadius.xl,
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.xl,
      marginTop: -40,
      minHeight: SCREEN_HEIGHT - HEADER_HEIGHT + 100,
    },
    eventTitle: {
      fontSize: typography.fontSize['3xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      lineHeight: typography.fontSize['3xl'] * 1.2,
      marginBottom: spacing.md,
    },
    hostSection: {
      marginBottom: spacing.xl,
    },
    hostLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    hostName: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
    },
    infoSection: {
      marginBottom: spacing.lg,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.gray100,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    infoContent: {
      flex: 1,
      paddingTop: spacing.xs,
    },
    infoTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
      marginBottom: spacing.xs,
    },
    infoText: {
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      lineHeight: typography.fontSize.base * 1.4,
    },
    infoSubtext: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    directionsButton: {
      marginTop: spacing.sm,
    },
    directionsText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    descriptionSection: {
      marginTop: spacing.xl,
      marginBottom: spacing.xl,
    },
    sectionTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      marginBottom: spacing.md,
    },
    descriptionText: {
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      lineHeight: typography.fontSize.base * 1.5,
    },
    expandButton: {
      marginTop: spacing.sm,
    },
    expandText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
    joinButtonContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    joinButton: {
      height: 56,
      borderRadius: borderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    joinButtonText: {
      color: colors.white,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
    },
  });

export default ModernEventDetail;
