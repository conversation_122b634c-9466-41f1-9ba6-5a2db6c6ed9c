import {FC} from 'react';
import {Path, Svg} from 'react-native-svg';

interface IProps {
  color?: string;
  isFilled: boolean;
}

const LikeIcon: FC<IProps> = ({color = '#F18B2E', isFilled}) => (
  <Svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="3" stroke-linecap="round">
    <Path
      d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
      stroke={isFilled ? color : 'white'}
      fill={isFilled ? color : undefined}
      strokeWidth={3}
    />
  </Svg>
);

export default LikeIcon;
