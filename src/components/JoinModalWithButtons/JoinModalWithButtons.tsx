import {Text, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal/dist/modal';
import getStyles from './styles';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  children: React.ReactNode;
  backgroundColor?: string;
  onCloseModal: () => void;
  modalIsVisible?: boolean;
  onPress: () => void;
  isLanguage?: string;
}

const JoinModalWithButtons = ({
  children,
  onCloseModal,
  onPress,
  modalIsVisible = false,
  backgroundColor = '#FFFFFF',
  isLanguage,
}: IProps) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const {t} = useTranslation();
  return (
    <Modal propagateSwipe={false} isVisible={modalIsVisible} swipeDirection={[]} style={[styles.modal]}>
      <SafeAreaView edges={['left', 'right']} style={styles.safeAreaContainer}>
        <View style={[styles.modalContent, {height: '60%', backgroundColor, paddingTop: 12}]}>
          <View
            style={{
              width: '100%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingHorizontal: 16,
            }}>
            <TouchableOpacity onPress={onCloseModal}>
              <Text style={styles.buttonText}>
                {isLanguage === 'ENG' ? 'Close' : isLanguage === 'GR' ? 'Κλείσιμο' : t('generic.close')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                onPress();
                onCloseModal();
              }}>
              <Text style={styles.buttonText}>
                {isLanguage === 'ENG' ? 'Done' : isLanguage === 'GR' ? 'Ολοκληρώθηκε' : t('generic.done')}
              </Text>
            </TouchableOpacity>
          </View>
          {children}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default JoinModalWithButtons;
