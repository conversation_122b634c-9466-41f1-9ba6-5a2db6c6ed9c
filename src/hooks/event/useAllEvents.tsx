import {GetEventsParams} from '~types/events';
import {useOptimizedEvents} from '~hooks/performance/useOptimizedEvents';

// Wrapper for backward compatibility - now uses optimized version
export const useAllEvents = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = true, // Changed default to true for better UX
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe,
  isNeighbourhood = false,
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  return useOptimizedEvents({
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    enabled,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood,
    // Enable performance optimizations
    prefetchNext: true,
    backgroundRefetch: true,
  });
};
