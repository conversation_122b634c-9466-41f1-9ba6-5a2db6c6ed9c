import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {Formik} from 'formik';
import moment from 'moment';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, ScrollView, Text, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {CalendarIcon, DropdownArrowIcon} from '~assets/icons';
import Button from '~components/Button';
import CustomBox from '~components/CustomBox';
import {CustomTextInput} from '~components/CustomTextInput';
import DateTimeModal from '~components/DateTimeModal';
import {DateTimePickerModal} from '~components/DateTimePickerModal';
import {GoBackHeader} from '~components/GoBackHeader';
import LocationModal, {Location} from '~components/LocationModal';
import GroupCapacityModal from '~components/ModalWithItems/GroupCapacityModal';
import {VisibilityModal} from '~components/ModalWithItems/VisibilityModal';
import {SCREENS} from '~constants';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useUpdateEventMutation} from '~hooks/event/useUpdateEvent';
import {useUpdateEventSubcategories} from '~hooks/event/useUpdateEventSubcategories';
import {useUpdateEventState} from '~providers/updateEvent/zustand';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import createStyles from './styles';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import FirebaseChatsService from '~services/FirebaseChats';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';

const DATE_FORMAT = 'DD/MM/YYYY';
export const TIME_FORMAT = 'h:mm A';

const EditEvent = () => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.EDIT_EVENT>>();
  const {t} = useTranslation();
  const {item} = route.params;
  const navigation = useNavigation<NavigationProps>();
  const {mutateAsync: updateEventMutation} = useUpdateEventMutation();
  const {mutateAsync: updateSubcategory} = useUpdateEventSubcategories();

  const {subcategory, setSubCategory} = useUpdateEventState();
  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {data: categoryData} = useGetEventSubcategories(item.event_id);

  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [isStartTimeModalOpen, setIsStartTimeModalOpen] = useState(false);
  const [isEndTimeModalOpen, setIsEndTimeModalOpen] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [isCapacityModalOpen, setIsCapacityModalOpen] = useState(false);
  const [isVisibilityModalOpen, setIsVisibilityModalOpen] = useState(false);
  const [isPayment, setIsPayment] = useState(false);

  const [location, setLocation] = useState<Location | null>({
    latitude: item.coords.lat,
    longitude: item.coords.long,
    address: item.address_name,
  });

  const [changeableStartTime, setChangeableStartTime] = useState<moment.Moment>(moment(item.start_date));
  const [changeableEndTime, setChangeableEndTime] = useState<moment.Moment>(moment(item.end_date));

  const validationSchema = yup.object().shape({
    name: yup.string().required(t('events.nameError')),
    description: yup.string().required(t('events.descriptionError')),
    eventAddress: yup.string().trim().required(t('events.eventAddressError')),
    locationIsChosen: yup.string().required(t('events.location_error')),
    startDate: yup.string().required(t('events.startDateError')),
    endDate: yup.string().required(t('events.endDateError')),
    visibility: yup.string().required(),
    groupCapacity: yup.string().required(),
    isForKids: yup.string(),
    is_paid: yup.string(),
    payment_url:
      isPayment == true
        ? yup.string().url(t('events.payment_url_error2')).required(t('events.payment_url_error'))
        : yup.string(),
  });

  useEffect(() => {
    console.log({
      category_id: null,
      subcategory_id: categoryData?.[0]?.subcategory_id || null,
      subcategory_name: categoryData?.[0]?.subcategory_name || null,
      image_url: item.image_url,
    });
    setSubCategory({
      category_id: null,
      subcategory_id: categoryData?.[0]?.subcategory_id || null,
      subcategory_name: categoryData?.[0]?.subcategory_name || null,
      image_url: item.image_url,
    });
  }, [categoryData, item.image_url, setSubCategory]);

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Edit Event', 'EditEvent');
  }, []);

  const onChangeIsPayment = () => {
    if (isPayment == true) {
      setIsPayment(false);
    } else {
      setIsPayment(true);
    }
  };

  const cancelEvent = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await cancelAEvent({event_id: item.event_id});
    setIsLoading(false);
    navigation.goBack();
  };

  const submitDelete = () =>
    Alert.alert('Are you sure you want to delete this event?', '', [
      {
        text: 'No',
        style: 'cancel',
      },
      {text: 'Yes', onPress: () => handleDeleteSubmitted(), style: 'destructive'},
    ]);

  const handleDeleteSubmitted = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await deleteEvent({event_id: item.event_id});
    try {
      await FirebaseChatsService.deleteGroupChat({event_id: item.event_id});
    } catch (error) {}
    navigation.pop(2);
  };

  // const handlePaymentUrlChange = (handleChange: any) => (text: any) => {
  //   if (
  //     text.startsWith('http://') ||
  //     text.startsWith('http:/') ||
  //     text.startsWith('http:') ||
  //     text.startsWith('https://') ||
  //     text.startsWith('https:/') ||
  //     text.startsWith('https:') ||
  //     text.startsWith('https:') ||
  //     text.startsWith('https') ||
  //     text.startsWith('http') ||
  //     text.startsWith('htt')
  //   ) {
  //     handleChange('payment_url')(text);
  //   } else {
  //     handleChange('payment_url')(`https://${text}`);
  //   }
  // };

  return (
    <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <GoBackHeader />
      <SafeAreaView style={styles.safeAreaView}>
        <Formik
          validationSchema={validationSchema}
          onSubmit={async ({
            name,
            description,
            startDate,
            startTime,
            endDate,
            endTime,
            eventAddress,
            groupCapacity,
            visibility,
            isForKids,
            is_paid,
            payment_url,
          }) => {
            setIsLoading(true);
            if (!location) {
              return;
            }
            const isSubcategoryUpdated =
              categoryData?.[0] &&
              subcategory?.[0] &&
              categoryData?.[0].subcategory_id !== subcategory?.[0].subcategory_id;

            const payload = {
              event_id: item.event_id,
              private: t('events.events_private') === visibility,
              end_date: moment
                .utc(`${endDate} ${endTime}`, `${DATE_FORMAT} ${TIME_FORMAT}`)
                .format('YYYY-MM-DDTHH:mm:ss'),
              start_date: moment
                .utc(`${startDate} ${startTime}`, `${DATE_FORMAT} ${TIME_FORMAT}`)
                .format('YYYY-MM-DDTHH:mm:ss'),
              number_slots: groupCapacity,
              name: name,
              coords: {
                lat: location.latitude,
                long: location.longitude,
              },
              is_exported: item.is_exported,
              description: description,
              event_type: item.event_type,
              image_url: isSubcategoryUpdated && subcategory[0].image_url ? subcategory[0].image_url : item.image_url,
              address_name: eventAddress,
              is_for_kids: !!isForKids,
              is_paid: !!is_paid,
              payment_url: payment_url,
            };

            await updateEventMutation(payload);

            if (isSubcategoryUpdated && subcategory[0].subcategory_id) {
              await updateSubcategory({
                event_id: item.event_id,
                ids: [subcategory[0].subcategory_id],
              });
            }
            setIsLoading(false);
            navigation.goBack();
          }}
          initialValues={{
            name: item?.name,
            description: item.description,
            startDate: moment(item.start_date).format(DATE_FORMAT),
            endDate: moment(item.end_date).format(DATE_FORMAT),
            startTime: moment(item.start_date).format(TIME_FORMAT),
            endTime: moment(item.end_date).format(TIME_FORMAT),
            eventAddress: item.address_name,
            locationIsChosen: 'true',
            visibility: item.private ? t('events.events_private') : t('events.events_public'),
            groupCapacity: item.number_slots || 5,
            isForKids: item?.is_for_kids ? 'true' : '',
            is_paid: item?.is_paid ? 'true' : '',
            payment_url: item?.payment_url,
          }}>
          {({values, handleChange, handleSubmit, errors}) => (
            <>
              <ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContentContainerStyle}
                bounces={false}>
                <Text style={styles.updateEventText}>{t('events.update_event')}</Text>
                <CustomTextInput
                  value={values.name}
                  onChangeValue={handleChange('name')}
                  description={t('events.event_name_placeholder')}
                  errorText={errors.name?.toString()}
                />
                <CustomTextInput
                  value={values.description}
                  onChangeValue={handleChange('description')}
                  description={t('events.event_description_placeholder')}
                  errorText={errors.description?.toString()}
                  isMultiline
                />
                <CustomBox
                  description={t('events.choose_category')}
                  value={subcategory?.[0]?.subcategory_name}
                  handlePress={() => navigation.navigate(SCREENS.EDIT_SUBCATEGORY)}
                  icon={<DropdownArrowIcon />}
                />
                <CustomBox
                  description={t('events.date_range')}
                  handlePress={() => setIsDateModalOpen(true)}
                  icon={<CalendarIcon />}
                  value={`${values.startDate} - ${values.endDate}`}
                  errorText={errors.startDate?.toString() || errors.endDate?.toString()}
                />
                <CustomBox
                  description={t('events.start_time')}
                  handlePress={() => setIsStartTimeModalOpen(true)}
                  icon={<CalendarIcon />}
                  value={values.startTime}
                  errorText={errors.startTime?.toString()}
                />
                <CustomBox
                  description={t('events.end_time')}
                  handlePress={() => setIsEndTimeModalOpen(true)}
                  icon={<CalendarIcon />}
                  value={values.endTime}
                  errorText={errors.endTime?.toString()}
                />
                <CustomTextInput
                  value={values.eventAddress}
                  onChangeValue={handleChange('eventAddress')}
                  description={t('events.event_address')}
                  errorText={errors.eventAddress?.toString()}
                />
                <CustomBox
                  description={t('generic.location')}
                  handlePress={() => setIsLocationModalOpen(true)}
                  value={location?.address ? location.address : t('generic.location')}
                  errorText={errors.locationIsChosen?.toString()}
                />
                <CustomBox
                  description={t('events.visibility')}
                  handlePress={() => setIsVisibilityModalOpen(true)}
                  value={values.visibility}
                  errorText={errors.visibility?.toString()}
                />
                <CustomBox
                  description={t('events.group_capacity')}
                  handlePress={() => setIsCapacityModalOpen(true)}
                  value={values.groupCapacity}
                  errorText={errors.groupCapacity?.toString()}
                />
                <CustomBox
                  description={t('events.children_event')}
                  handlePress={() => handleChange('isForKids')(values.isForKids ? '' : 'true')}
                  value={values.isForKids ? 'Yes' : 'No'}
                />

                <CustomBox
                  description={t('events.paidevent')}
                  handlePress={() => {
                    onChangeIsPayment();
                    handleChange('is_paid')(values.is_paid ? '' : 'true');
                  }}
                  value={values.is_paid ? 'Yes' : 'No'}
                />

                {values.is_paid && (
                  <>
                    <CustomTextInput
                      value={values.payment_url}
                      onChangeValue={handleChange('payment_url')}
                      description={t('events.payment_url')}
                      errorText={errors.payment_url?.toString()}
                    />
                    <Text
                      style={{
                        color: colors.black,
                        marginTop: 5,
                        fontSize: 12,
                        fontWeight: '400',
                        width: '100%',
                        lineHeight: 14,
                      }}>
                      for example : https://www.pyxi.ai/payment_url
                    </Text>
                  </>
                )}
              </ScrollView>
              <View style={styles.buttonContainer}>
                <Button
                  label={t('events.save_changes')}
                  isLoading={isLoading}
                  onPress={() => handleSubmit()}
                  containerStyle={styles.buttonContainerStyle}
                  textStyle={styles.buttonTextStyle}
                />
                <View style={styles.rowView}>
                  {!item.is_cancelled && (
                    <Button
                      label={t('events.cancel_event')}
                      isLoading={isLoading}
                      onPress={() => cancelEvent()}
                      containerStyle={styles.buttonContainer1}
                      textStyle={styles.buttonTextStyle}
                    />
                  )}
                  {!item.is_cancelled && <View style={{width: 10}} />}
                  <Button
                    label={t('events.delete_event')}
                    isLoading={isLoading}
                    onPress={() => submitDelete()}
                    containerStyle={styles.buttonContainer2}
                    textStyle={styles.buttonTextStyle}
                  />
                </View>
              </View>
              <DateTimePickerModal
                isVisible={isDateModalOpen}
                onClose={() => setIsDateModalOpen(false)}
                onSubmit={() => setIsDateModalOpen(false)}
                minDate={moment(values.startDate, DATE_FORMAT)}
                maxDate={moment(values.endDate, DATE_FORMAT)}
                handleChooseMinDate={date => date && handleChange('startDate')(moment(date).format(DATE_FORMAT))}
                handleChooseMaxDate={date => date && handleChange('endDate')(moment(date).format(DATE_FORMAT))}
              />
              <DateTimeModal
                isVisible={isStartTimeModalOpen}
                close={() => setIsStartTimeModalOpen(false)}
                onPress={() => {
                  if (changeableStartTime) {
                    handleChange('startTime')(changeableStartTime.format(TIME_FORMAT));
                  }
                  setIsStartTimeModalOpen(false);
                }}
                date={changeableStartTime?.toDate() || new Date()}
                handleChooseMaxDate={(value?: Date) => {
                  if (value) {
                    setChangeableStartTime(moment(value));
                    return;
                  }
                }}
                withoutMaxAvailableDate
                mode="time"
              />
              <DateTimeModal
                isVisible={isEndTimeModalOpen}
                close={() => setIsEndTimeModalOpen(false)}
                onPress={() => {
                  if (changeableEndTime) {
                    handleChange('endTime')(changeableEndTime.format(TIME_FORMAT));
                  }
                  setIsEndTimeModalOpen(false);
                }}
                date={changeableEndTime?.toDate() || new Date()}
                handleChooseMaxDate={(value?: Date) => {
                  if (value) {
                    setChangeableEndTime(moment(value));
                    return;
                  }
                }}
                withoutMaxAvailableDate
                mode="time"
              />

              <LocationModal
                isVisible={isLocationModalOpen}
                close={() => {
                  setIsLocationModalOpen(false);
                }}
                onLocationChange={value => {
                  console.log('value', value);
                  setLocation(value);
                  if (value) {
                    handleChange('locationIsChosen')('true');
                  } else {
                    handleChange('locationIsChosen')('');
                  }
                }}
              />

              <VisibilityModal
                isVisible={isVisibilityModalOpen}
                onPress={value => () => handleChange('visibility')(value)}
                chosenItem={values.visibility}
                onClose={() => setIsVisibilityModalOpen(false)}
              />
              <GroupCapacityModal
                isVisible={isCapacityModalOpen}
                onClose={() => setIsCapacityModalOpen(false)}
                onPress={value => {
                  handleChange('groupCapacity')(value.toString());
                  setIsCapacityModalOpen(false);
                }}
                initialCapacity={Number(values.groupCapacity)}
              />
            </>
          )}
        </Formik>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default EditEvent;
