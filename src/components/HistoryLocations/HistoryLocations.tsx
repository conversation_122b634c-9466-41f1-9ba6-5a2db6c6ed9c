import {Text, TouchableOpacity, View} from 'react-native';
import ItemFlagIcon from '../../assets/icons/ItemFlagIcon';
import {useTheme} from '~contexts/ThemeContext';

const HistoryLocations = ({onHistoryItemTapped}: any) => {
  const {colors} = useTheme();
  return (
    <>
      <View style={{marginBottom: 20, marginTop: 10}}>
        <Text numberOfLines={1} style={{fontSize: 16, fontWeight: '400', color: colors.statusBlue}}>
          History
        </Text>
      </View>
      <TouchableOpacity
        key={'address-from-profile'}
        style={{
          paddingBottom: 8,
          marginBottom: 10,
          borderBottomColor: colors.gray100 + 'CC',
          borderBottomWidth: 1,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
        }}
        onPress={() => {
          // onHistoryItemTapped({
          //   latitude: profile.location.coords.latitude,
          //   longitude: profile.location.coords.longitude,
          //   address: profile.address,
          // });
        }}>
        <ItemFlagIcon />
        <View style={{marginLeft: 8}}>
          <Text numberOfLines={1}>{'Mock my address'}</Text>
        </View>
      </TouchableOpacity>
      {/* ))} */}
    </>
  );
};

export default HistoryLocations;
