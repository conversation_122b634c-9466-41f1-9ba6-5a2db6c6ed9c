import {RouteProp, useFocusEffect, useNavigation, useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {useMemo, useState} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Image,
  TextInput,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import FailedPayment from '~components/FailedPayment/FailedPayment';
import {GoBackHeader} from '~components/GoBackHeader';
import ModernButton from '~components/ModernButton';
import {ModernPaymentCard, ModernPaymentSummary} from '~components/ModernPayment';
import {SCREENS} from '~constants';
import {spacing, borderRadius, typography} from '~constants/design';
import {useCreatePaymentIntent} from '~hooks/event/useCreatePaymentIntent';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {PaymentIntentResponse, Ticket} from '~types/api/event';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {LONG_DATE_FORMAT} from '~Utils/Time';
import {useStripe} from '@stripe/stripe-react-native';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {Business} from '~types/api/business';
import {User} from '~types/api/user';
import {ChatType} from '~types/chat';
import FirebaseChatsService from '~services/FirebaseChats';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useTheme} from '~contexts/ThemeContext';
import {ThemedStatusBar} from '~components/ThemedStatusBar';
import Animated, {FadeInDown, FadeInUp} from 'react-native-reanimated';

const DATE_FORMAT = 'ddd ' + LONG_DATE_FORMAT;
export interface TicketType {
  id: string;
  type: string;
  price: number;
  description: string;
  quantity?: number;
}

const BuyTicket: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.BUY_TICKET>>();
  const navigation = useNavigation<NavigationProps>();
  const {eventId} = route.params;

  const styles = StyleSheet.create({
    strickThorughText: {
      textDecorationLine: 'line-through',
      textDecorationColor: colors.error,
      color: colors.gray400,
    },
    applyButton: {
      marginLeft: 8,
      backgroundColor: colors.eventInfluencer,
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderRadius: 8,
    },
    applyButtonText: {
      color: colors.white,
      fontSize: 14,
      fontWeight: 'bold',
    },
    appliedText: {
      fontSize: 14,
      color: colors.statusGreen,
      marginBottom: 10,
    },
    promoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    promoInput: {
      height: 40,
      borderColor: colors.gray400,
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 8,
      backgroundColor: colors.white,
      flex: 1,
    },
    soldoutText: {
      alignSelf: 'center',
      color: colors.error,
      marginTop: 15,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    container: {flex: 1, backgroundColor: colors.white},
    eventDetails: {
      flexDirection: 'row',
      padding: 15,
      backgroundColor: colors.white,
      alignItems: 'center',
      borderBottomWidth: 1,
      borderColor: colors.border,
      marginTop: Platform.OS == 'ios' ? 20 : 50,
    },
    eventImage: {width: 100, height: 100, borderRadius: 10, marginRight: 10},
    eventInfo: {flex: 1},
    eventTitle: {fontSize: 18, fontWeight: 'bold', color: colors.textPrimary},
    eventLocation: {fontSize: 14, color: colors.textSecondary, marginTop: 5},
    eventTime: {fontSize: 12, color: colors.textSecondary, marginTop: 5},
    listContainer: {paddingHorizontal: 20, paddingTop: 10},
    ticketItemContainer: {
      marginBottom: 15,
      padding: 15,
      backgroundColor: colors.white,
      borderRadius: 10,
      shadowColor: colors.black,
      shadowOpacity: 0.1,
      shadowOffset: {width: 0, height: 2},
      shadowRadius: 5,
      elevation: 2,
      flex: 1,
    },
    ticketItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 5,
      flex: 1,
    },
    ticketType: {fontSize: 16, fontWeight: '600', flex: 1},
    quantityControls: {flexDirection: 'row', alignItems: 'center'},
    button: {
      width: 30,
      height: 30,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.gray400,
      borderRadius: 5,
    },
    buttonText: {fontSize: 18, fontWeight: 'bold'},
    quantity: {marginHorizontal: 10, fontSize: 16},
    footer: {
      padding: 20,
      borderTopWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.white,
    },
    totalText: {fontSize: 20, fontWeight: 'bold', marginBottom: 10},
    payButton: {
      backgroundColor: colors.eventInfluencer,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
    },
    payButtonText: {color: colors.white, fontSize: 18, fontWeight: 'bold'},
  });
  const {data: fetchedItem, refetch: refetchEvent, isLoading: isEventLoading} = useGetEventById(eventId);
  const [quantities, setQuantities] = useState<Record<string, number>>(
    (fetchedItem?.tickets || []).reduce((acc, ticket) => ({...acc, [ticket.name]: 0}), {}),
  );
  const [isModalVisible, setModalVisible] = useState(false);
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState('');
  const [promoDiscountPer, setPromoDiscountPer] = useState(0);
  const [promoId, setPromoId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const {mutateAsync: addPaymentIntent} = useCreatePaymentIntent();
  const {initPaymentSheet, presentPaymentSheet} = useStripe();
  const {data: userAccount} = useGetUserAccount(auth().currentUser?.uid);
  const {data: hostUser} = useGetUserAccount(fetchedItem?.host_id || '');
  const {data: hostBusiness} = useGetBusinessAccount(fetchedItem?.host_id || '');
  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus();
  const host = !(hostUser as unknown as {detail: string})?.detail ? hostUser : hostBusiness;

  const handleRetry = () => {
    setModalVisible(false);
    // Retry logic here
    console.log('Retry pressed');
  };
  const onApplyPromo = () => {
    if (calculateTotal() <= 0) {
      Alert.alert('Please add ticket first.');
      return;
    }
    if (promoCode.trim() === '') {
      Alert.alert('Please enter a valid promo code.');
      return;
    }
    // Example logic to validate promo code
    if (appliedPromo) {
      setAppliedPromo('');
      setPromoCode('');
      Alert.alert(`Promo code "${promoCode}" removed!`);
    } else {
      const promoCodeList = fetchedItem?.promo_codes;
      const isPromocodeApply = promoCodeList?.filter(item => item.name == promoCode);
      if (isPromocodeApply && isPromocodeApply?.length > 0) {
        setAppliedPromo(promoCode);
        setPromoId(isPromocodeApply[0].promo_code_id);
        setPromoDiscountPer(isPromocodeApply[0].discount_percentage);
        Alert.alert(`Promo code "${promoCode}" applied!`);
      } else {
        Alert.alert('Promo code is invalid!');
      }
    }
  };

  const handleClose = () => {
    setModalVisible(false);
  };

  useFocusEffect(
    React.useCallback(() => {
      refetchEvent();
    }, [refetchEvent]),
  );

  const handleIncrement = (id: string, capacity: number) => {
    setQuantities(prev => {
      const currentQuantity = prev[id] || 0;
      if (currentQuantity + 1 > capacity) {
        return prev;
      }
      return {...prev, [id]: currentQuantity + 1};
    });
  };

  const handleDecrement = (id: string) => {
    setQuantities(prev => ({...prev, [id]: Math.max(prev[id] - 1, 0)}));
  };

  const calculateTotal = (): number => {
    return (fetchedItem?.tickets || []).reduce((total, ticket) => {
      const price = ticket.discounted_price ? ticket.discounted_price : ticket.price;
      return total + quantities[ticket.name] * price;
    }, 0);
  };

  const calculateQunatity = (): number => {
    return (fetchedItem?.tickets || []).reduce((total, ticket) => {
      return total + quantities[ticket.name];
    }, 0);
  };

  const calculatePercentage = (percentage: number): number => {
    const total = calculateTotal();
    return total - (total * percentage) / 100;
  };

  const initializePaymentSheet = async (data: PaymentIntentResponse) => {
    const {error} = await initPaymentSheet({
      merchantDisplayName: 'Pyxi',
      paymentIntentClientSecret: data.client_secret, // retrieve this from your server
      returnURL: 'http://partner.pyxi.ai/payment-return',
    });

    if (error) {
      console.error('Error initializing Payment Sheet:', error);
      return false;
    }

    console.log('Payment Sheet initialized successfully');
    return true;
  };

  const openPaymentSheet = async (selectedTickets: any, data: PaymentIntentResponse) => {
    const {error} = await presentPaymentSheet();

    if (error) {
      setIsLoading(false);
      Alert.alert('Payment failed', error.message);
      console.error('Error presenting Payment Sheet:', error.message);
    } else {
      setIsLoading(false);
      navigation.replace(SCREENS.PAYMENT_SUCCESS, {
        eventId: eventId,
        eventData: fetchedItem,
        selectedTickets,
        order_id: data.order_id,
      });
      console.log('Payment successful!');
    }
  };

  const onJoinClick = async () => {
    const selectedTickets = (fetchedItem?.tickets || [])
      .filter(ticket => quantities[ticket.name] > 0)
      .map(ticket => ({
        ...ticket,
        quantity: quantities[ticket.name],
        count: quantities[ticket.name],
      }));
    setIsLoading(true);
    try {
      const data: any = await addEventUserStatus({
        event_id: eventId.toString(),
        count: calculateQunatity().toString(),
      });
      if (data && data?.status == 'accepted') {
        navigation.replace(SCREENS.PAYMENT_SUCCESS, {
          eventId: eventId,
          eventData: fetchedItem,
          selectedTickets,
          order_id: data.order?.order_id,
        });
      } else if (data && data?.status == 'pending') {
        Notifier.showNotification({
          title: 'Your request to join the event has been sent to the organiser. Please wait for approval.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
        navigation.goBack();
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  const onPayClick = async () => {
    const selectedTickets = (fetchedItem?.tickets || [])
      .filter(ticket => quantities[ticket.name] > 0)
      .map(ticket => ({
        ...ticket,
        quantity: quantities[ticket.name],
        count: quantities[ticket.name],
      }));

    const body: any = {
      tickets: selectedTickets,
      eventId: eventId,
    };
    if (promoId) {
      body.promo_code_id = promoId;
    }
    try {
      setIsLoading(true);
      const data = await addPaymentIntent(body);
      const isInitialized = await initializePaymentSheet(data);
      if (isInitialized) {
        await openPaymentSheet(selectedTickets, data);
      }
      setIsLoading(false);
      return;
    } catch (error: any) {
      console.log(error, 'Failed to initialize payment!');
      console.log(error.message, 'Failed to initialize payment!');

      Alert.alert('Failed to initialize payment!');
      setIsLoading(false);
    }
  };
  const goBackHandler = () => {
    navigation.goBack();
  };

  const ticketAvailable = useMemo(() => {
    const ticketList = fetchedItem?.tickets?.filter(item => {
      return item.capacity - item.sold > 0;
    });
    return ticketList?.length;
  }, [fetchedItem]);

  const onChatClick = async () => {
    if (host && fetchedItem) {
      setIsLoading(true);
      const chatId = await FirebaseChatsService.createOrganisationChat({
        user_id1: auth().currentUser!.uid,
        user_id2: host?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: (host as Business)?.name
          ? (host as Business)?.name
          : (host as unknown as User)?.last_name
            ? `${(host as unknown as User)?.first_name} ${(host as unknown as User)?.last_name || ''}`
            : (host as unknown as User)?.first_name || '',
        user_image: userAccount?.photo + '',
        event: fetchedItem,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
      setIsLoading(false);
    }
  };

  const renderTicketItem = ({item}: {item: Ticket}) => (
    <View style={styles.ticketItemContainer}>
      <View style={styles.ticketItem}>
        <Text style={styles.ticketType}>
          {item.name}
          {item.price > 0 && (
            <>
              {' - '}
              <Text
                style={
                  item.discounted_price && item.price !== item.discounted_price
                    ? styles.strickThorughText
                    : {color: colors.black}
                }>
                {item.currency} {item.price}
              </Text>
            </>
          )}{' '}
          {item.discounted_price > 0 && item.price !== item.discounted_price && (
            <Text style={{color: colors.black}}>
              {item.currency} {item.discounted_price}
            </Text>
          )}
        </Text>
        {item.capacity - item.sold > 0 ? (
          <View style={styles.quantityControls}>
            <TouchableOpacity style={styles.button} onPress={() => handleDecrement(item.name)}>
              <Text style={styles.buttonText}>-</Text>
            </TouchableOpacity>
            <Text style={styles.quantity}>{quantities[item.name]}</Text>
            <TouchableOpacity
              style={styles.button}
              onPress={() => handleIncrement(item.name, item.capacity - item.sold)}>
              <Text style={styles.buttonText}>+</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <Text style={styles.soldoutText}>Sold out</Text>
        )}
      </View>
      <Text style={styles.eventTime}>{item.description}</Text>
      <Text style={styles.eventTime}>{`Available tickets: ${item.capacity - item.sold}`}</Text>
    </View>
  );

  const getCurrency = () => {
    if (fetchedItem?.tickets && fetchedItem?.tickets.length > 0) {
      return fetchedItem?.tickets[0].currency;
    }
    return '$';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedStatusBar />
      <GoBackHeader customCallback={goBackHandler} />
      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        {/* Event Details */}

        <View style={styles.eventDetails}>
          <Image source={{uri: fetchedItem?.image_url}} style={styles.eventImage} />
          <View style={styles.eventInfo}>
            <Text style={styles.eventTitle}>{fetchedItem?.name}</Text>
            <Text style={styles.eventLocation}>{fetchedItem?.coord_address}</Text>
            <Text style={styles.eventTime}>
              {fetchedItem?.start_date ? moment.utc(fetchedItem?.start_date).format(DATE_FORMAT) : 'N/A'} -{'\n'}
              {fetchedItem?.end_date ? moment.utc(fetchedItem?.end_date).format(DATE_FORMAT) : 'N/A'}
            </Text>
          </View>
        </View>

        {/* Tickets Section */}
        <FlatList
          data={fetchedItem?.tickets}
          keyExtractor={item => item.name}
          renderItem={renderTicketItem}
          contentContainerStyle={styles.listContainer}
        />

        {/* Footer with Total and Pay Button */}
        {ticketAvailable ? (
          <View style={styles.footer}>
            {fetchedItem?.is_paid && (
              <View style={styles.promoRow}>
                <TextInput
                  style={styles.promoInput}
                  placeholder="Enter Promo Code"
                  value={promoCode}
                  onChangeText={setPromoCode}
                />
                <TouchableOpacity
                  style={[styles.applyButton, appliedPromo ? {backgroundColor: colors.error} : {}]}
                  onPress={onApplyPromo}>
                  <Text style={styles.applyButtonText}>{appliedPromo ? 'Remove' : 'Apply'}</Text>
                </TouchableOpacity>
              </View>
            )}
            {appliedPromo && (
              <Text style={styles.appliedText}>
                {appliedPromo ? `Applied Promo: ${appliedPromo}, ${promoDiscountPer}% discount applied!` : ''}
              </Text>
            )}
            {fetchedItem?.is_paid && (
              <Text onPress={() => setModalVisible(true)} style={styles.totalText}>
                Total:{' '}
                <Text style={appliedPromo ? styles.strickThorughText : {}}>
                  {getCurrency()} {calculateTotal()}
                </Text>{' '}
                {appliedPromo && (
                  <Text>
                    {getCurrency()} {calculatePercentage(promoDiscountPer)}
                  </Text>
                )}
              </Text>
            )}
            {fetchedItem?.is_paid ? (
              <TouchableOpacity
                disabled={calculateTotal() == 0}
                style={[
                  styles.payButton,
                  {backgroundColor: calculateTotal() == 0 ? colors.gray400 : colors.eventInfluencer},
                ]}
                onPress={onPayClick}>
                {isLoading ? <ActivityIndicator color={colors.white} /> : <Text style={styles.payButtonText}>Pay</Text>}
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                disabled={calculateQunatity() == 0}
                style={[
                  styles.payButton,
                  {backgroundColor: calculateQunatity() == 0 ? colors.gray400 : colors.eventInfluencer},
                ]}
                onPress={onJoinClick}>
                {isLoading ? <ActivityIndicator color={colors.white} /> : <Text style={styles.payButtonText}>Buy</Text>}
              </TouchableOpacity>
            )}
          </View>
        ) : (
          !isEventLoading && (
            <View style={styles.footer}>
              <Text style={styles.soldoutText}>This event is sold out. Stay tuned for upcoming events!</Text>
              {false && (
                <TouchableOpacity
                  style={[styles.payButton, {backgroundColor: colors.statusBlue, marginTop: 10}]}
                  onPress={onChatClick}>
                  {isLoading ? (
                    <ActivityIndicator color={colors.white} />
                  ) : (
                    <Text style={styles.payButtonText}>Chat With Organiser</Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          )
        )}
        <FailedPayment isVisible={isModalVisible} onRetry={handleRetry} onClose={handleClose} />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default BuyTicket;
