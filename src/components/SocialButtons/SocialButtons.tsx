import React from 'react';
import {Platform, Text, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {GoogleIcon, AppleIcon} from '~assets/icons';
import FirebaseAuth from '~services/FirebaseAuthService';
import {AppleButton} from '@invertase/react-native-apple-authentication';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography, shadows} from '~constants/design';

const SocialButtons = () => {
  const {colors} = useTheme();
  const navigation = useNavigation();

  const styles = {
    container: {
      gap: spacing.md,
    },
    socialButton: {
      width: '100%',
      height: 56,
      borderRadius: borderRadius.xl,
      borderWidth: 0.5,
      borderColor: colors.border,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      flexDirection: 'row' as const,
      backgroundColor: colors.surface,
      ...shadows.none,
      position: 'relative' as const,
    },
    socialButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      flex: 1,
    },
    iconContainer: {
      position: 'absolute' as const,
      left: spacing.lg,
      width: 24,
      height: 24,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.socialButton} onPress={FirebaseAuth.signInWithGoogle} activeOpacity={0.8}>
        <View style={styles.iconContainer}>
          <GoogleIcon />
        </View>
        <Text style={styles.socialButtonText}>Continue with Google</Text>
      </TouchableOpacity>
      {Platform.OS === 'ios' && (
        <AppleButton
          buttonStyle={AppleButton.Style.BLACK}
          buttonType={AppleButton.Type.CONTINUE}
          style={{
            width: '100%',
            height: 56,
            borderRadius: borderRadius.xl,
          }}
          onPress={FirebaseAuth.signInWithApple}
        />
      )}
    </View>
  );
};

export default SocialButtons;
