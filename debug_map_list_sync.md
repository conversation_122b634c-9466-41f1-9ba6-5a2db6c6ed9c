# Map and List View Synchronization Debug Guide

## Issue Fixed
The map and list views were not synchronized because:

1. **Neighbourhood Events Bug**: When `isNeighbourhood` was true, the distance filter was set to an extremely large value (`1000000000000000` km), effectively fetching events from anywhere in the world instead of respecting the user's radius setting.

2. **Missing Client-Side Filtering**: There was no client-side safety net to filter out events that were beyond the specified radius.

## Changes Made

### 1. Fixed Neighbourhood Events Distance Logic
**File**: `src/hooks/performance/useOptimizedEvents.tsx`
- Changed from unlimited distance to `Math.max(distance_km, 500)` km maximum
- This ensures neighbourhood events respect the user's radius but with a minimum of 500km

### 2. Added Client-Side Distance Filtering
**File**: `src/components/HomeScreenComponent/tabComponents/HomeContent/HomeContent.tsx`
- Added haversine distance calculation to filter events on the client side
- Only applies to non-neighbourhood events to avoid conflicts
- Removes events that are beyond the user's specified radius

### 3. Enhanced Debug Logging
Added comprehensive logging to track:
- Event fetching parameters
- Number of events fetched vs filtered
- Distance calculations for sample events
- Radius changes
- Position updates

## How to Test

### 1. Check Console Logs
Look for these debug messages in your React Native debugger:

```
🔍 Fetching events with params: {tab: "discover", distance_km: 100, ...}
📍 Fetched X events for discover tab with 100km radius
🗂️ HomeContent filtered events: X events (isNeighbourhood: false, radius: 100km)
📍 First 3 events with calculated distances: [...]
🎯 Radius changed from 100km to 50km
📍 Current position updated: {latitude: ..., longitude: ..., address: "..."}
```

### 2. Test Scenarios

#### Scenario A: Normal Events (Non-Neighbourhood)
1. Set radius to 100km in settings
2. Switch between map and list view
3. **Expected**: Both views should show the same events
4. **Check logs**: Should see client-side filtering if server returns events beyond radius

#### Scenario B: Neighbourhood Events
1. Go to "Neighbourhood Groups" tab
2. Set radius to 100km
3. **Expected**: May show events up to 500km (minimum for neighbourhood events)
4. **Check logs**: Should see `isNeighbourhood: true` in logs

#### Scenario C: Radius Changes
1. Change radius from 100km to 50km
2. **Expected**: Both map and list should update to show fewer events
3. **Check logs**: Should see radius change log and filtering logs

### 3. Verify Distance Calculations
The logs will show calculated distances for the first 3 events. Verify that:
- All events are within your specified radius
- If any events are outside the radius, they should be filtered out client-side

## Warning Signs to Look For

### ⚠️ Events Outside Radius
If you see this warning, it means the server is returning events beyond your radius:
```
⚠️ Found X events outside 100km radius: [...]
```

### 🔍 Client-Side Filtering Active
If you see this message, the client-side filter is working:
```
🔍 Client-side distance filter: 150 → 75 events (removed 75 events beyond 100km)
```

## If Issues Persist

1. **Clear App Cache**: The radius setting is persisted, so clear app data if needed
2. **Check Location Permissions**: Ensure the app has location access
3. **Verify Current Position**: Check that `currentPositionState` is properly set
4. **Test Different Tabs**: The issue might be specific to certain event types

## Technical Details

### Distance Calculation
Uses the haversine formula to calculate great-circle distances between two points on Earth:
```typescript
const distance = haversine(
  {latitude: userLat, longitude: userLng},
  {latitude: eventLat, longitude: eventLng},
  {unit: 'km'}
);
```

### Filtering Logic
1. **Server-side**: API respects `distance_km` parameter
2. **Client-side**: Additional filtering as safety net
3. **Time-based**: Applied before distance filtering
4. **Free events**: Applied after distance filtering

The client-side filtering ensures that even if the server returns events outside the radius, they will be filtered out before displaying in both map and list views.
