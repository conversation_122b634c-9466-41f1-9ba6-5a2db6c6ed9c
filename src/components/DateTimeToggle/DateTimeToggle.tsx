import {Image, Text, TouchableOpacity, View} from 'react-native';
import {DateIcon, TimeIcon} from '../../assets/icons';
import {Switch} from 'react-native-switch';
import {FC, useCallback, useEffect, useState} from 'react';
import createStyles from './styles';
import DateTimeModal from '../DateTimeModal';
import moment from 'moment-timezone';
import React from 'react';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isDate?: boolean;
  isTime?: boolean;
  label: string;
  date: moment.Moment;
  time: moment.Moment;
  onChangeDate: (date: string) => void;
  onChangeTime: (date: string) => void;
  errorText?: string;
  minAvailableDate?: Date;
}

export const DATE_FORMAT = 'DD/MM/YYYY';
export const TIME_FORMAT = 'h:mm A';

const DateTimeToggle: FC<IProps> = ({
  isDate,
  isTime,
  date,
  time,
  label,
  onChangeDate,
  onChangeTime,
  errorText,
  minAvailableDate = moment().toDate(),
}) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const [isActive, setIsActive] = useState(false);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [changeableDate, setChangeableDate] = useState<moment.Moment>(date);
  const [changeableTime, setChangeableTime] = useState<moment.Moment>(time);
  const [pickTime, setPickTime] = useState(false);

  useEffect(() => {
    setChangeableTime(time);
  }, [time]);

  useEffect(() => {
    setChangeableDate(date);
  }, [date]);

  useEffect(() => {
    if (isDateModalOpen === false && !isDate) {
      setIsActive(false);
    }
  }, [isDateModalOpen, isDate]);

  const handleChooseDate = useCallback(
    (value?: Date) => {
      console.log(date, pickTime, 'onChangeStart');

      if (value) {
        if (pickTime) {
          setChangeableTime(moment(value));
        } else {
          setChangeableDate(moment(value));
        }
        return;
      }
    },
    [pickTime],
  );

  const handleChange = (val: moment.Moment) => {
    if (pickTime) {
      onChangeTime(val.format(TIME_FORMAT));
    } else {
      onChangeDate(val.format(DATE_FORMAT));
    }
  };

  return (
    <>
      <View style={styles.container}>
        <View
          style={{
            ...styles.rightContainer,
            borderBottomWidth: isTime ? 0 : 0.5,
            borderBottomColor: colors.gray400,
          }}>
          <View style={{flex: 1}}>
            <Text style={styles.title}>{label}</Text>
            {!!errorText && <Text style={styles.dateTime}>{errorText}</Text>}
          </View>
          {isDate && (
            <TouchableOpacity
              onPress={() => {
                setPickTime(false);
                setIsDateModalOpen(true);
              }}
              style={styles.pickerContainer}>
              <Text style={[styles.dateTime, {color: colors.textSecondary}]}>{date.format('MMM, DD, YYYY')}</Text>
            </TouchableOpacity>
          )}
          {isTime && (
            <TouchableOpacity
              onPress={() => {
                setPickTime(true);
                setIsDateModalOpen(true);
              }}
              style={[
                styles.pickerContainer,
                {
                  marginLeft: 2,
                  borderBottomRightRadius: 5,
                  borderTopRightRadius: 5,
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                },
              ]}>
              <Text style={[styles.dateTime, {color: colors.textSecondary, marginLeft: 8}]}>
                {time.format('hh:mm A')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
      <DateTimeModal
        isVisible={isDateModalOpen}
        close={() => setIsDateModalOpen(false)}
        onPress={() => {
          if (pickTime) {
            handleChange(changeableTime);
          } else {
            handleChange(changeableDate);
          }
          setIsDateModalOpen(false);
        }}
        date={pickTime ? changeableTime.toDate() : changeableDate?.toDate()}
        handleChooseMaxDate={handleChooseDate}
        mixAvailableDate={pickTime ? undefined : minAvailableDate}
        withoutMaxAvailableDate
        mode={pickTime ? 'time' : 'date'}
      />
    </>
  );
};

export default DateTimeToggle;
