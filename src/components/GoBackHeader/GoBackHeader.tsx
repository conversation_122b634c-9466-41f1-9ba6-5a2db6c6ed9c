import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {Platform, Pressable, Share, StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {FadeInLeft, FadeInRight} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ChevronIcon, ShareIcon} from '~assets/icons';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  customCallback?: () => void;
  withShare?: boolean;
  customText?: string;
  isGradientShow?: boolean;
}
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const GoBackHeader = ({customCallback, withShare = false, customText, isGradientShow = true}: IProps) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const defaultCallback = () => {
    navigation.goBack();
  };

  return (
    <View
      style={{
        position: 'absolute',
        width: '100%',
        zIndex: 999,
        paddingTop: top + 5,
        paddingHorizontal: 16,
        paddingBottom: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      {isGradientShow && (
        <LinearGradient
          start={{x: 0.5, y: 1}}
          end={{x: 0.5, y: 0}}
          colors={[
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
            colors.overlayBackground,
          ]}
          style={StyleSheet.absoluteFillObject}
        />
      )}
      <AnimatedPressable
        entering={FadeInLeft.delay(340)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: Platform.OS === 'android' ? 30 : 0,
          backgroundColor: customText ? colors.overlayBackground : 'transparent',
          paddingHorizontal: customText ? 8 : 0,
          paddingVertical: customText ? 4 : 0,
          borderRadius: customText ? 6 : 0,
        }}
        onPress={customCallback || defaultCallback}>
        <ChevronIcon />
        <Text style={{color: colors.primary, fontWeight: '600'}}>{customText || t('generic.back')}</Text>
      </AnimatedPressable>
      {withShare && (
        <AnimatedPressable
          entering={FadeInRight.delay(340)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: Platform.OS === 'android' ? 30 : 0,
          }}
          onPress={async () => {
            let link = `https://pyxida.page.link/?link=${encodeURIComponent(
              `https://www.pyxi.ai?event_id=${'123'}`,
            )}&apn=com.pyxida.pyxida&isi=1663673322&ibi=com.pyxida.pyxida`;
            await Share.share({
              message: `Your friend Who is inviting you to join club on Pyxi! \n ${link}`,
            });
          }}>
          <ShareIcon />
        </AnimatedPressable>
      )}
    </View>
  );
};

export default GoBackHeader;
