import {NativeScrollEvent, NativeSyntheticEvent, Platform, Text, View, TouchableOpacity} from 'react-native';
import {Event} from '~types/api/event';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {useHomeStore} from '~providers/home/<USER>';
import {EVENTS_TIMEFRAME, EVENT_AGE_GROUP, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {useTranslation} from 'react-i18next';
import {responsive, spacing} from '~constants';
import ModernEventsList from '../components/ModernEventsList';
import {useTheme} from '~contexts/ThemeContext';

interface UserEventsProps {
  scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any;
  selectedTimeframe: {id: number; title: string};
  globalInputValue: string;
  isMapView?: boolean;
  setIsMapView?: (value: boolean) => void;
}

const UserEvents: React.FC<UserEventsProps> = ({
  scrollHandler,
  filterEvents,
  selectedTimeframe,
  globalInputValue,
  isMapView,
  setIsMapView,
}) => {
  const {radius, isForKids} = useHomeStore();
  const {t} = useTranslation();
  const {colors} = useTheme();
  const {data, isLoading, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage, refetch} = useAllEvents({
    distance_km: radius,
    tab: TABS.PERSONAL_EVENTS,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
    filter_type: selectedTimeframe.title === t('home.upcoming_events') ? undefined : selectedTimeframe.title,
  });
  const {top} = useSafeAreaInsets();
  const {width, height} = useSafeAreaFrame();
  const [refreshing, setRefreshing] = useState(false);

  const eventsList = getListFromPages<Event>(data);

  useEffect(() => {
    refetch();
  }, [refetch, radius, isForKids, selectedTimeframe]);

  const renderFooter = () => {
    return isFetchingNextPage ? <></> : null;
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const contentLayout = useMemo(() => {
    const platformLayout = Platform.OS === 'ios' ? 115 : 125;
    return platformLayout + top;
  }, [top]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      refetch();
      setRefreshing(false);
    }, 2000);
  }, []);

  const filteredEvents = useMemo(() => {
    if (!eventsList) {
      return [];
    }
    let events = eventsList;

    // Apply time-based filtering first
    if (events && filterEvents) {
      events = filterEvents(events, selectedTimeframe);
    }

    // Then apply search filtering if needed
    if (globalInputValue && events) {
      events = events.filter(event => event.name.toLowerCase().includes(globalInputValue.toLowerCase()));
    }

    return events || [];
  }, [eventsList, selectedTimeframe, globalInputValue, filterEvents]);

  useEffect(() => {
    if (!isLoading && !isFetching && !isFetchingNextPage && filteredEvents.length * 170 < height) {
      handleLoadMore();
    }
  }, [isLoading, isFetching, isFetchingNextPage, filteredEvents, handleLoadMore, height]);

  // Handle empty state with modern design
  const renderEmptyState = () => {
    if (!(isLoading || isFetching || isFetchingNextPage) && !eventsList?.length) {
      return (
        <View
          style={{
            marginTop: contentLayout,
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: spacing.xl,
          }}>
          <View style={{alignItems: 'center', maxWidth: 300}}>
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: colors.gray100,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: spacing.xl,
              }}>
              <Text style={{fontSize: 32}}>🎉</Text>
            </View>

            <Text
              style={{
                textAlign: 'center',
                fontSize: 20,
                fontWeight: '700',
                marginBottom: spacing.md,
                color: colors.textPrimary,
              }}>
              {t('home.empty_list_events')}
            </Text>

            <Text
              style={{
                textAlign: 'center',
                fontSize: 16,
                color: colors.textSecondary,
                marginBottom: spacing.xl,
                lineHeight: 24,
              }}>
              Start exploring events in your area or create your own!
            </Text>

            <TouchableOpacity
              style={{
                backgroundColor: colors.primary,
                paddingHorizontal: spacing.xl,
                paddingVertical: spacing.md,
                borderRadius: 12,
                shadowColor: colors.primary,
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}
              onPress={() => {
                // Navigate to discover/all events tab
                // This would typically navigate to the main events tab
              }}>
              <Text
                style={{
                  color: colors.white,
                  fontSize: 16,
                  fontWeight: '600',
                }}>
                Discover Events
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  };

  // Show message when map view is requested but not supported
  if (isMapView) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing.xl,
          marginTop: contentLayout,
          backgroundColor: colors.background,
        }}>
        <View style={{alignItems: 'center', maxWidth: 300}}>
          <View
            style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: colors.gray100,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: spacing.xl,
            }}>
            <Text style={{fontSize: 32}}>🗺️</Text>
          </View>

          <Text
            style={{
              fontSize: 20,
              fontWeight: '700',
              textAlign: 'center',
              marginBottom: spacing.md,
              color: colors.textPrimary,
            }}>
            Map View Not Available
          </Text>
          <Text
            style={{
              fontSize: 16,
              textAlign: 'center',
              color: colors.textSecondary,
              lineHeight: 24,
              marginBottom: spacing.xl,
            }}>
            Map view is not supported for this filter. Please switch to list view to see events.
          </Text>

          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: spacing.xl,
              paddingVertical: spacing.md,
              borderRadius: 12,
              shadowColor: colors.primary,
              shadowOffset: {width: 0, height: 4},
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 8,
            }}
            onPress={() => setIsMapView && setIsMapView(false)}>
            <Text
              style={{
                color: colors.white,
                fontSize: 16,
                fontWeight: '600',
              }}>
              Switch to List View
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Show empty state if no events
  const emptyStateComponent = renderEmptyState();
  if (emptyStateComponent) {
    return emptyStateComponent;
  }

  if (isLoading || !eventsList?.length) {
    return <View style={{flex: 1, backgroundColor: colors.background}} />;
  }

  return (
    <View
      style={{
        flex: 1,
        marginTop: contentLayout,
        backgroundColor: colors.background,
      }}>
      <ModernEventsList
        data={filteredEvents}
        isLoading={isLoading}
        isFetching={isFetching}
        isFetchingNextPage={isFetchingNextPage}
        refreshing={refreshing}
        onRefresh={onRefresh}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        isMyEvents={false}
        emptyStateTitle={t('home.empty_list_events')}
        emptyStateSubtitle="Start exploring events in your area or create your own!"
        estimatedItemSize={280}
      />
    </View>
  );
};

export default UserEvents;
