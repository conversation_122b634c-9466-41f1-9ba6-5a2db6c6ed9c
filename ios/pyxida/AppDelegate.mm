#import "AppDelegate.h"
#import <Firebase.h>
#import <GoogleMaps/GoogleMaps.h>
#import <React/RCTBundleURLProvider.h>
#import "RNCConfig.h"
#import <React/RCTLinkingManager.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  NSString *GOOGLE_API_KEY = [RNCConfig envFor:@"GOOGLE_API_KEY"];
  [FIRApp configure];
  [RNFBDynamicLinksAppDelegateInterceptor sharedInstance];
  [GMSServices provideAPIKey:GOOGLE_API_KEY];
  self.moduleName = @"pyxida";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  self.initialProps = @{};

  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

// Linking API
- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity
 restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
 return [RCTLinkingManager application:application
                  continueUserActivity:userActivity
                    restorationHandler:restorationHandler];
}

 - (BOOL)application:(UIApplication *)application
openURL:(NSURL *)url
options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {

BOOL handled2 = [RCTLinkingManager application:application openURL:url
sourceApplication:options[UIApplicationOpenURLOptionsSourceApplicationKey] annotation:options[UIApplicationOpenURLOptionsAnnotationKey]];
// Add any custom logic here.
return (handled2);
}

// Explicitly define remote notification delegates to ensure compatibility with some third-party libraries
// - (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
// {
//   return [super application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
// }

// // Explicitly define remote notification delegates to ensure compatibility with some third-party libraries
// - (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
// {
//   return [super application:application didFailToRegisterForRemoteNotificationsWithError:error];
// }

// // Explicitly define remote notification delegates to ensure compatibility with some third-party libraries
// - (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
// {
//   return [super application:application didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
// }

@end
