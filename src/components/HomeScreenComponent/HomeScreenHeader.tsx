import {Text, Pressable, TouchableOpacity, View, Image, DeviceEventEmitter} from 'react-native';
import {FloatingPlusIcon, NavigationIcon, SmallChevronIcon} from '~assets/icons';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import Animated from 'react-native-reanimated';
import {useGetUserGroups} from '~hooks/user/useGetUserGroups';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import LocationModal, {Location} from '~components/LocationModal';
import {useEffect, useState} from 'react';
import {useMapsContext} from '~providers/maps/zustand';
import {useTranslation} from 'react-i18next';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {HeaderDropdown} from '~components/HeaderDropdown';
import {RadiusModal} from '~components/RadiusModal';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {Business} from '~types/api/business';
import {User} from '~types/api/user';
import {useHomeStore} from '~providers/home/<USER>';
import useHomeScreenAnimation from './useHomeScreenAnimation';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  setSelectedTab?: any;
}

const HomeScreenHeader = ({setSelectedTab}: IProps) => {
  const {colors} = useTheme();
  const {currentPositionState, userLocation, setCurrentPositionState} = useMapsContext();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {data: userAccountData} = useGetUserAccount(auth().currentUser!.uid);
  const {data: businessAccountData} = useGetBusinessAccount(auth().currentUser!.uid);
  const {data: userGroups} = useGetUserGroups();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const [locationModalIsVisible, setLocationModalIsVisible] = useState(false);
  const {t} = useTranslation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const {getCurrentPosition} = useGetCurrentPosition();
  const [isRadiusModal, setIsRadiusModal] = useState(false);
  const {isForKids, toggleIsForKids} = useHomeStore();
  const [isFiltered, setIsFiltered] = useState(false);
  const {scrollHandler, scrollAnimatedStyle, addEventButtonStyle} = useHomeScreenAnimation();

  const data = userType === 'personal' ? userAccountData : businessAccountData;

  const isCurrentUserLocation = currentPositionState?.address === userLocation?.locationName;

  const handleUseCurrentLocation = () => {
    getCurrentPosition(true);
  };

  const handleLocationChange = async (location: Location | null) => {
    if (!location) {
      return;
    }
    setCurrentPositionState(location);
    DeviceEventEmitter.emit('homeLocationChange', {lat: location.latitude, long: location.longitude});

    try {
      if (businessAccountData?.uid) {
        const payload = {uid: auth().currentUser!.uid, coords: {lat: location.latitude, long: location.longitude}};
        updateBusinessMutation(payload);
        return;
      }

      await updateUserMutation({
        coords_real: {lat: location.latitude, long: location.longitude},
      });
    } catch (error) {
      console.log(error);
    }
    if (setSelectedTab) {
      setSelectedTab();
    }
  };

  const handleRadiusPress = () => {
    setTimeout(() => {
      setIsRadiusModal(true);
      setIsFiltered(true);
    }, 500);
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        marginBottom: 8,
        width: '100%',
        zIndex: 100000,
      }}>
      <View style={{flex: 1, alignItems: 'center', flexDirection: 'row', paddingRight: 25}}>
        <TouchableOpacity
          style={{
            padding: 5,
            borderRadius: 50,
            marginRight: 12,
            backgroundColor: isCurrentUserLocation ? colors.statusGreen : colors.border,
          }}
          onPress={handleUseCurrentLocation}>
          <NavigationIcon color={isCurrentUserLocation ? colors.white : colors.textPrimary} />
        </TouchableOpacity>
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'row'}}>
          <TouchableOpacity
            style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}
            onPress={() => {
              setLocationModalIsVisible(true);
            }}>
            <Text style={{fontWeight: '500', fontSize: 15, lineHeight: 19, marginRight: 6, color: colors.black}}>
              {currentPositionState?.address
                ? currentPositionState?.address?.slice(0, 17) + '...'
                : t('generic.location')}
            </Text>
            <View style={{marginTop: 1}}>
              <SmallChevronIcon />
            </View>
          </TouchableOpacity>
        </View>
        {/* <HeaderDropdown
          isChildrenEvents={isForKids}
          handleRadiusPress={handleRadiusPress}
          handleChildrenPress={toggleIsForKids}
          isFiltered={isFiltered}
        /> */}
      </View>
      <Pressable
        style={e => {
          return {
            minHeight: 35,
            maxHeight: 38,
            paddingVertical: 7,
            paddingHorizontal: 0,
            borderRadius: 100,
            backgroundColor: e.pressed ? colors.warning : colors.secondary,
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'center',
          };
        }}
        onPress={() => {
          setIsTabBarDisabled(true);
          navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE);
        }}>
        <Animated.View
          style={[
            {height: '100%', maxHeight: 30, justifyContent: 'center', alignItems: 'center', flexDirection: 'row'},
            addEventButtonStyle,
          ]}>
          <FloatingPlusIcon />
          <Text style={{fontSize: 14, textAlign: 'center', fontWeight: '400', color: colors.white, marginLeft: 3}}>
            {t('home.create_event')}
          </Text>
        </Animated.View>
      </Pressable>
      {/* <TouchableOpacity
        onPress={() => {
          if (data && userGroups) {
            setIsTabBarDisabled(true);
            navigation.navigate(SCREENS.PERSONAL_INFO, {
              user: {
                tag: 'main-screen-photo-tag',
                source: data?.photo,
                name: `${(data as User).first_name || (data as Business).name} ${(data as User).last_name || ''}`,
                description: data.description,
                user_id: data.uid,
              },
            });
          }
        }}>
        <Image
          // sharedTransitionTag="main-screen-photo-tag"
          source={{uri: data?.photo}}
          style={{
            backgroundColor: colors.primary,
            width: 40,
            height: 40,
            borderRadius: 10,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      </TouchableOpacity> */}

      <LocationModal
        isVisible={locationModalIsVisible}
        close={() => {
          setLocationModalIsVisible(false);
        }}
        onLocationChange={handleLocationChange}
        isFromHomeScreen
      />
      <RadiusModal modalIsVisible={isRadiusModal} onClose={() => setIsRadiusModal(false)} />
    </View>
  );
};

export default HomeScreenHeader;
