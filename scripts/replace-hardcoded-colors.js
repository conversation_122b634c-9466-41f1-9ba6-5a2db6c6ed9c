#!/usr/bin/env node

/**
 * Script to help replace hardcoded colors with theme-aware colors
 * Usage: node scripts/replace-hardcoded-colors.js
 */

const fs = require('fs');
const path = require('path');

// Color mapping from hardcoded values to theme colors
const COLOR_MAPPING = {
  // Common hardcoded colors to theme equivalents
  '#FCF9ED': 'colors.background',
  '#FAFAFA': 'colors.gray100',
  '#F2F2F6': 'colors.inputBackground',
  '#E0E0E0': 'colors.separatorLine',
  '#D1D1D6': 'colors.border',
  '#CDCDCD': 'colors.border',
  '#B0B0B0': 'colors.placeholderText',
  '#8E8E93': 'colors.statusGray',
  '#A1A1A1': 'colors.gray400',
  '#323232': 'colors.textSecondary',
  '#1D1E20': 'colors.textPrimary',
  '#000000': 'colors.black',
  '#000': 'colors.black',
  '#FFFFFF': 'colors.white',
  '#fff': 'colors.white',

  // App specific colors
  '#4A48AD': 'colors.secondary',
  '#2E2EAB': 'colors.statusPurple',
  '#F5A865': 'colors.eventInfluencer',
  '#E69A45': 'colors.warning',
  '#FF9500': 'colors.primary',
  '#FFB84D': 'colors.primary', // dark mode variant
  '#34C759': 'colors.statusGreen',
  '#10B981': 'colors.eventCommunity',
  '#FF6B6B': 'colors.eventPyxiSelect',
  '#EF4444': 'colors.error',
  '#007AFF': 'colors.statusBlue',

  // Additional gray shades
  '#F5F5F5': 'colors.gray100',
  '#f5f5f5': 'colors.gray100',
  '#f9f9f9': 'colors.gray100',
  '#f8f8f8': 'colors.gray100',
  '#f7f7f7': 'colors.gray100',
  '#f4f4f4': 'colors.gray100',
  '#f2f2f2': 'colors.gray100',
  '#efefef': 'colors.gray100',
  '#eee': 'colors.gray100',
  '#E5E5EA': 'colors.border',
  '#ddd': 'colors.border',
  '#dedede': 'colors.border',
  '#dfdfdf': 'colors.border',
  '#d3d3d3': 'colors.border',
  '#cfcfcf': 'colors.border',
  '#ccc': 'colors.gray400',
  '#BCBCBC': 'colors.gray400',
  '#AEAEAE': 'colors.gray400',
  '#aeaeae': 'colors.gray400',
  '#909090': 'colors.gray400',
  '#888888': 'colors.textSecondary',
  '#888': 'colors.textSecondary',
  '#777777': 'colors.textSecondary',
  '#777': 'colors.textSecondary',
  '#666': 'colors.textSecondary',
  '#555': 'colors.textSecondary',
  '#444': 'colors.textSecondary',
  '#333333': 'colors.textPrimary',
  '#333': 'colors.textPrimary',

  // Additional specific colors
  '#F1F1F3': 'colors.gray100',
  '#F2F2F7': 'colors.gray100',
  '#E9E9EB': 'colors.gray100',
  '#C7C7CC': 'colors.border',
  '#B7B7B9': 'colors.gray400',
  '#757575': 'colors.textSecondary',
  '#636366': 'colors.textSecondary',
  '#3C3C43': 'colors.textPrimary',
  '#202020': 'colors.textPrimary',

  // Overlay and modal colors
  'rgba(0, 0, 0, 0.5)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.1)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.7)': 'colors.overlayBackground', // dark mode
  'rgba(0, 0, 0, 0.3)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.4)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.6)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.75)': 'colors.overlayBackground',
  'rgba(0, 0, 0, 0.8)': 'colors.overlayBackground',
  'rgba(0,0,0,.1)': 'colors.overlayBackground',
  'rgba(0,0,0,.2)': 'colors.overlayBackground',
  'rgba(0,0,0,.3)': 'colors.overlayBackground',
  'rgba(0,0,0,.4)': 'colors.overlayBackground',
  'rgba(0,0,0,.5)': 'colors.overlayBackground',
  'rgba(0,0,0,0.3)': 'colors.overlayBackground',
  'rgba(0,0,0,0.4)': 'colors.overlayBackground',
  'rgba(0,0,0,0.5)': 'colors.overlayBackground',
  'rgba(0,0,0,0.6)': 'colors.overlayBackground',
  'rgba(0,0,0,0.75)': 'colors.overlayBackground',

  // Status and state colors
  red: 'colors.error',
  '"red"': 'colors.error',
  "'red'": 'colors.error',
  black: 'colors.black',
  '"black"': 'colors.black',
  "'black'": 'colors.black',
  white: 'colors.white',
  '"white"': 'colors.white',
  "'white'": 'colors.white',
  gray: 'colors.gray400',
  '"gray"': 'colors.gray400',
  "'gray'": 'colors.gray400',
  grey: 'colors.gray400',
  '"grey"': 'colors.gray400',
  "'grey'": 'colors.gray400',
  green: 'colors.statusGreen',
  '"green"': 'colors.statusGreen',
  "'green'": 'colors.statusGreen',
  blue: 'colors.statusBlue',
  '"blue"': 'colors.statusBlue',
  "'blue'": 'colors.statusBlue',

  // Additional unmapped colors found in scan
  '#00BD90': 'colors.statusGreen',
  '#0645AD': 'colors.statusBlue',
  '#0C79FE': 'colors.statusBlue',
  '#222325': 'colors.textPrimary',
  '#2980b9': 'colors.statusBlue',
  '#2AB04D': 'colors.statusGreen',
  '#30DB5B': 'colors.statusGreen',
  '#3D3DE5': 'colors.statusBlue',
  '#4A5EB0': 'colors.secondary',
  '#4B5565': 'colors.textSecondary',
  '#4CAF50': 'colors.statusGreen',
  '#5246D3': 'colors.secondary',
  '#5C5955': 'colors.textSecondary',
  '#5D544B': 'colors.textSecondary',
  '#5a67d8': 'colors.secondary',
  '#5f50ad': 'colors.secondary',
  '#697586': 'colors.textSecondary',
  '#6c757d': 'colors.textSecondary',
  '#718096': 'colors.textSecondary',
  '#74747B': 'colors.textSecondary',
  '#767469': 'colors.textSecondary',
  '#7f7f7f': 'colors.textSecondary',
  '#848484': 'colors.textSecondary',
  '#8E8E92': 'colors.placeholderText',
  '#BDC3C7': 'colors.border',
  '#E3E8EF': 'colors.gray100',
  '#E4E4EB': 'colors.gray100',
  '#EFE9CD': 'colors.background',
  '#F18B2E': 'colors.warning',
  '#F44336': 'colors.error',
  '#FEFBEE': 'colors.background',
  '#FF3B30': 'colors.error',
  '#FF9430': 'colors.primary',
  '#FFC107': 'colors.warning',
  '#FFFEFB': 'colors.background',
  '#aeacae': 'colors.gray400',
  '#ba8638': 'colors.warning',
  '#cc690e': 'colors.warning',
  '#efeff0': 'colors.gray100',
  '#f5a965': 'colors.eventInfluencer',
  '#f7f9fc': 'colors.background',
  '#f9a826': 'colors.warning',
  '#fefcf4': 'colors.background',
  '#ffff': 'colors.white',
  '#ffffff': 'colors.white',

  // RGB colors
  'rgb(199,199,204)': 'colors.border',
  'rgb(46,46,171)': 'colors.secondary',

  // Additional RGBA colors
  'rgba(0, 0, 0, 0.15)': 'colors.overlayBackground',
  'rgba(0,0,0,.001)': 'colors.overlayBackground',
  'rgba(0,0,0,.35)': 'colors.overlayBackground',
  'rgba(0,0,0,.45)': 'colors.overlayBackground',
  'rgba(104, 163, 87, 0.9)': 'colors.statusGreen + "E6"',
  'rgba(107, 114, 128, 0.1)': 'colors.gray400 + "1A"',
  'rgba(118, 118, 128, 0.12)': 'colors.gray400 + "1F"',
  'rgba(120, 120, 128, 0.16)': 'colors.gray400 + "29"',
  'rgba(199,199,204,0.4)': 'colors.border + "66"',
  'rgba(207,207,207,0.3)': 'colors.border + "4D"',
  'rgba(207,207,207,0.5)': 'colors.border + "80"',
  'rgba(228, 230, 235, 0.8)': 'colors.gray100 + "CC"',
  'rgba(245, 168, 101, 0.6)': 'colors.eventInfluencer + "99"',
  'rgba(245, 168, 101, 0.9)': 'colors.eventInfluencer + "E6"',
  'rgba(249, 68, 73, 0.9)': 'colors.error + "E6"',
  'rgba(255, 255, 255, 0.1)': 'colors.white + "1A"',
  'rgba(255, 255, 255, 0.15)': 'colors.white + "26"',
  'rgba(255, 255, 255, 0.2)': 'colors.white + "33"',
  'rgba(255, 255, 255, 0.3)': 'colors.white + "4D"',
  'rgba(255, 255, 255, 0.7)': 'colors.white + "B3"',
  'rgba(255,255,255, 0.8)': 'colors.white + "CC"',
  'rgba(46,46,171, 0.3)': 'colors.secondary + "4D"',
  'rgba(46,46,171, 1)': 'colors.secondary',
  'rgba(46,46,171,0.8)': 'colors.secondary + "CC"',
  'rgba(51, 51, 51, 1)': 'colors.textPrimary',
  'rgba(60, 60, 67, 0.6)': 'colors.textSecondary + "99"',
  'rgba(89, 114, 218, 1)': 'colors.statusBlue',
};

// File extensions to process
const EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to process
const DIRECTORIES = ['src/components', 'src/containers', 'src/screens'];

function findHardcodedColors(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const colorRegex =
    /#[0-9a-fA-F]{3,6}|rgba?\([^)]+\)|'(?:red|blue|green|white|black|gray|grey)'|"(?:red|blue|green|white|black|gray|grey)"/g;
  const matches = content.match(colorRegex) || [];
  return [...new Set(matches)]; // Remove duplicates
}

function replaceColorsInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Check if file already imports useTheme
  const hasThemeImport = content.includes('useTheme') && content.includes('~contexts/ThemeContext');

  for (const [hardcodedColor, themeColor] of Object.entries(COLOR_MAPPING)) {
    // Handle quoted strings in style objects
    const quotedRegex = new RegExp(`['"]${hardcodedColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
    if (content.match(quotedRegex)) {
      content = content.replace(quotedRegex, themeColor);
      hasChanges = true;
    }

    // Handle JSX attributes (e.g., color="value" should become color={colors.value})
    const jsxAttrRegex = new RegExp(`(\\w+)=(['"])${hardcodedColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\2`, 'g');
    if (content.match(jsxAttrRegex)) {
      content = content.replace(jsxAttrRegex, `$1={${themeColor}}`);
      hasChanges = true;
    }
  }

  // Add theme import if changes were made and import doesn't exist
  if (hasChanges && !hasThemeImport) {
    // Find existing imports from ~contexts/ThemeContext
    const themeContextImportRegex = /import\s+{([^}]+)}\s+from\s+['"]~contexts\/ThemeContext['"];?/;
    const match = content.match(themeContextImportRegex);

    if (match) {
      // Add useTheme to existing import
      const existingImports = match[1];
      if (!existingImports.includes('useTheme')) {
        const newImports = existingImports.includes(',')
          ? `${existingImports}, useTheme`
          : `${existingImports}, useTheme`;
        content = content.replace(match[0], `import {${newImports}} from '~contexts/ThemeContext';`);
      }
    } else {
      // Add new import after other imports
      const importRegex = /import[^;]+;/g;
      const imports = content.match(importRegex) || [];
      if (imports.length > 0) {
        const lastImport = imports[imports.length - 1];
        const lastImportIndex = content.lastIndexOf(lastImport);
        const insertIndex = lastImportIndex + lastImport.length;
        content =
          content.slice(0, insertIndex) +
          "\nimport {useTheme} from '~contexts/ThemeContext';" +
          content.slice(insertIndex);
      }
    }

    // Add useTheme hook in component
    const componentRegex = /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*{/;
    const componentMatch = content.match(componentRegex);
    if (componentMatch) {
      const hookInsertPoint = componentMatch.index + componentMatch[0].length;
      if (!content.includes('const {colors} = useTheme();')) {
        content =
          content.slice(0, hookInsertPoint) + '\n  const {colors} = useTheme();' + content.slice(hookInsertPoint);
      }
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
  }

  return hasChanges;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath, {withFileTypes: true});

  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);

    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (EXTENSIONS.some(ext => file.name.endsWith(ext))) {
      try {
        replaceColorsInFile(fullPath);
      } catch (error) {
        console.error(`❌ Error processing ${fullPath}:`, error.message);
      }
    }
  }
}

function scanForColors() {
  console.log('🔍 Scanning for hardcoded colors...\n');

  const allColors = new Set();

  for (const dir of DIRECTORIES) {
    if (fs.existsSync(dir)) {
      const files = getAllFiles(dir);
      for (const file of files) {
        if (EXTENSIONS.some(ext => file.endsWith(ext))) {
          const colors = findHardcodedColors(file);
          colors.forEach(color => allColors.add(color));
        }
      }
    }
  }

  console.log('Found hardcoded colors:');
  [...allColors].sort().forEach(color => {
    const mapped = COLOR_MAPPING[color] ? `→ ${COLOR_MAPPING[color]}` : '❌ NOT MAPPED';
    console.log(`  ${color} ${mapped}`);
  });

  console.log(`\nTotal unique colors found: ${allColors.size}`);
  const mappedCount = [...allColors].filter(color => COLOR_MAPPING[color]).length;
  console.log(`Mapped colors: ${mappedCount}/${allColors.size}`);
}

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

// Main execution
const command = process.argv[2];

if (command === 'scan') {
  scanForColors();
} else if (command === 'replace') {
  console.log('🎨 Starting color replacement...\n');

  for (const dir of DIRECTORIES) {
    console.log(`Processing directory: ${dir}`);
    processDirectory(dir);
  }

  console.log('\n✨ Color replacement completed!');
} else {
  console.log(`
Usage:
  node scripts/replace-hardcoded-colors.js scan     - Scan for hardcoded colors
  node scripts/replace-hardcoded-colors.js replace  - Replace hardcoded colors with theme colors
  `);
}
