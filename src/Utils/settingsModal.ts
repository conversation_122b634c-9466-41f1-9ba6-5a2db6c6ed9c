import {Alert, Linking} from 'react-native';
import i18n from '~i18n';

const openSetting = () => {
  Linking.openSettings().catch(() => {
    Alert.alert(i18n.t('other.unableToOpenSettings'));
  });
};

const handleGoToSettingsImage = () => {
  return Alert.alert(i18n.t('other.settings'), i18n.t('other.alertDescription'), [
    {
      text: i18n.t('other.goToSettings'),
      onPress: openSetting,
    },
    {
      text: i18n.t('other.dontUsePhotos'),
      onPress: () => {},
    },
  ]);
};

export default handleGoToSettingsImage;
