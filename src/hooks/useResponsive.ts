import {useState, useEffect} from 'react';
import {Dimensions, ScaledSize} from 'react-native';
import {responsive, breakpoints} from '~constants/design';

interface ResponsiveHook {
  width: number;
  height: number;
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  isPortrait: boolean;
  isLandscape: boolean;
  scale: (size: number) => number;
  getValue: <T>(values: {xs?: T; sm?: T; md?: T; lg?: T; xl?: T}, fallback: T) => T;
}

export const useResponsive = (): ResponsiveHook => {
  const [dimensions, setDimensions] = useState(() => Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({window}: {window: ScaledSize}) => {
      setDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  const {width, height} = dimensions;

  return {
    width,
    height,
    isSmall: width < breakpoints.sm,
    isMedium: width >= breakpoints.sm && width < breakpoints.lg,
    isLarge: width >= breakpoints.lg,
    isPortrait: height > width,
    isLandscape: width > height,
    scale: responsive.scale,
    getValue: responsive.getValue,
  };
};

export default useResponsive;
