import {Path, Svg} from 'react-native-svg';

const MyEvents = ({color}: {color: string}) => (
  <Svg width="25" height="25" viewBox="0 0 22 23" fill="none">
    <Path
      d="M16.225 0C17.1731 0 18.0825 0.393771 18.7529 1.09469C19.4234 1.79561 19.8 2.74625 19.8 3.7375V10.3753C19.2856 10.0309 18.7311 9.75693 18.15 9.55995V6.325H1.65V16.9625C1.65 18.0734 2.5124 18.975 3.575 18.975H9.1443C9.3346 19.5891 9.5975 20.1675 9.9242 20.7H3.575C2.62685 20.7 1.71754 20.3062 1.04709 19.6053C0.376651 18.9044 0 17.9537 0 16.9625V3.7375C0 2.74625 0.376651 1.79561 1.04709 1.09469C1.71754 0.393771 2.62685 0 3.575 0H16.225ZM16.225 1.725H3.575C3.06446 1.725 2.57483 1.93703 2.21382 2.31445C1.85281 2.69186 1.65 3.20375 1.65 3.7375V4.6H18.15V3.7375C18.15 3.20375 17.9472 2.69186 17.5862 2.31445C17.2252 1.93703 16.7355 1.725 16.225 1.725ZM22 16.675C22 18.3525 21.3626 19.9613 20.228 21.1474C19.0934 22.3336 17.5546 23 15.95 23C14.3454 23 12.8066 22.3336 11.672 21.1474C10.5374 19.9613 9.9 18.3525 9.9 16.675C9.9 14.9975 10.5374 13.3887 11.672 12.2025C12.8066 11.0164 14.3454 10.35 15.95 10.35C17.5546 10.35 19.0934 11.0164 20.228 12.2025C21.3626 13.3887 22 14.9975 22 16.675ZM19.6394 13.9679C19.5883 13.9144 19.5276 13.8719 19.4608 13.8429C19.394 13.8139 19.3223 13.799 19.25 13.799C19.1777 13.799 19.106 13.8139 19.0392 13.8429C18.9724 13.8719 18.9117 13.9144 18.8606 13.9679L14.85 18.1619L13.0394 16.2679C12.9361 16.1599 12.7961 16.0993 12.65 16.0993C12.5039 16.0993 12.3639 16.1599 12.2606 16.2679C12.1573 16.3759 12.0993 16.5223 12.0993 16.675C12.0993 16.8277 12.1573 16.9741 12.2606 17.0821L14.4606 19.3821C14.5117 19.4356 14.5724 19.4781 14.6392 19.5071C14.706 19.5361 14.7777 19.551 14.85 19.551C14.9223 19.551 14.994 19.5361 15.0608 19.5071C15.1276 19.4781 15.1883 19.4356 15.2394 19.3821L19.6394 14.7821C19.6906 14.7287 19.7313 14.6652 19.759 14.5954C19.7867 14.5255 19.801 14.4506 19.801 14.375C19.801 14.2994 19.7867 14.2245 19.759 14.1546C19.7313 14.0848 19.6906 14.0213 19.6394 13.9679Z"
      fill={color}
    />
  </Svg>
);

export default MyEvents;
