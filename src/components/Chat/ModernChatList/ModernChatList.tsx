import React, {useState, useCallback} from 'react';
import {View, Text, RefreshControl} from 'react-native';
import {FlashList} from '@shopify/flash-list';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography} from '~constants/design';
import {haptics} from '~utils/haptics';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import ModernChatRow from '../ModernChatRow/ModernChatRow';
import {ChatIcon} from '~assets/icons';
import {ChatTypeWithKey, ConciergeChatTypeWithKey} from '~types/chat';

interface ModernChatListProps {
  chats: (ChatTypeWithKey | ConciergeChatTypeWithKey)[];
  isLoading?: boolean;
  onRefresh?: () => Promise<void>;
  emptyStateTitle?: string;
  emptyStateSubtitle?: string;
}

// Use regular FlashList for now to avoid animation component issues
// const AnimatedFlashList = Animated.createAnimatedComponent(FlashList);

const ModernChatList: React.FC<ModernChatListProps> = ({
  chats,
  isLoading = false,
  onRefresh,
  emptyStateTitle = 'No conversations yet',
  emptyStateSubtitle = 'Start a conversation by messaging someone',
}) => {
  const {colors} = useTheme();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use chats directly without filtering
  const filteredChats = chats;

  const handleRefresh = useCallback(async () => {
    if (!onRefresh) {
      return;
    }

    setIsRefreshing(true);
    haptics.light();

    try {
      await onRefresh();
    } catch (error) {
      console.error('Failed to refresh chats:', error);
      haptics.error();
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh]);

  const renderEmptyState = () => (
    <FadeIn delay={200} duration={400}>
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing.xl,
          paddingVertical: spacing.xxxxl,
        }}>
        <View
          style={{
            width: 80,
            height: 80,
            borderRadius: borderRadius.full,
            backgroundColor: colors.gray100,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: spacing.xl,
          }}>
          <ChatIcon color={colors.textSecondary} />
        </View>

        <Text
          style={{
            fontSize: typography.fontSize.xl,
            fontWeight: typography.fontWeight.semibold,
            color: colors.textPrimary,
            textAlign: 'center',
            marginBottom: spacing.sm,
          }}>
          {emptyStateTitle}
        </Text>

        <Text
          style={{
            fontSize: typography.fontSize.base,
            color: colors.textSecondary,
            textAlign: 'center',
            lineHeight: typography.fontSize.base * typography.lineHeight.relaxed,
          }}>
          {emptyStateSubtitle}
        </Text>
      </View>
    </FadeIn>
  );

  const renderChatItem = useCallback(
    ({item, index}: {item: ChatTypeWithKey | ConciergeChatTypeWithKey; index: number}) => {
      return <ModernChatRow chat={item as ChatTypeWithKey} index={index} />;
    },
    [],
  );

  const keyExtractor = useCallback((item: ChatTypeWithKey | ConciergeChatTypeWithKey) => {
    return `chat_${item.key}`;
  }, []);

  if (isLoading && chats.length === 0) {
    return (
      <View style={{flex: 1, backgroundColor: colors.background}}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Text
            style={{
              fontSize: typography.fontSize.base,
              color: colors.textSecondary,
              fontWeight: typography.fontWeight.medium,
            }}>
            Loading conversations...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <FlashList
        data={filteredChats}
        renderItem={renderChatItem}
        keyExtractor={keyExtractor}
        estimatedItemSize={88}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: spacing.xxxxl,
        }}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={colors.primary}
              colors={[colors.primary]}
              progressBackgroundColor={colors.surface}
            />
          ) : undefined
        }
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

export default ModernChatList;
