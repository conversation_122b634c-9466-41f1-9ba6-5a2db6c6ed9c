import {useEffect, useRef} from 'react';
import {Dimensions, Keyboard, NativeScrollEvent, NativeSyntheticEvent} from 'react-native';
import {Gesture} from 'react-native-gesture-handler';
import {
  Extrapolate,
  interpolate,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  useAnimatedReaction,
  runOnJS,
  cancelAnimation,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
const {height} = Dimensions.get('window');
export const OPEN_BOTTOMSHEET_HEIGHT = height * 0.5;
export const CLOSED_BOTTOMSHEET_HEIGHT = height * 0.8;

const useBottomSheetAnimations = (onPositionChange?: (position: 'open' | 'closed' | 'moving') => void) => {
  const {colors} = useTheme();
  const moving = useSharedValue(false);
  const prevY = useSharedValue<number>(CLOSED_BOTTOMSHEET_HEIGHT);
  const transY = useSharedValue<number>(CLOSED_BOTTOMSHEET_HEIGHT);
  const movedY = useSharedValue(0);
  const scrollY = useSharedValue(0);
  const isMountedRef = useRef(true);

  // Wrap onPositionChange in runOnJS to call it on the JS thread
  const handlePositionChange = (position: 'open' | 'closed' | 'moving') => {
    'worklet';
    if (onPositionChange) {
      runOnJS(onPositionChange)(position);
    }
  };

  // Detect position changes using useAnimatedReaction
  useAnimatedReaction(
    () => ({
      transY: transY.value,
      moving: moving.value,
    }),
    (current, previous) => {
      if (current.transY !== previous?.transY) {
        if (!current.moving) {
          if (Math.abs(current.transY - OPEN_BOTTOMSHEET_HEIGHT) < 1) {
            handlePositionChange('open');
          } else if (Math.abs(current.transY - CLOSED_BOTTOMSHEET_HEIGHT) < 1) {
            handlePositionChange('closed');
          }
        } else {
          handlePositionChange('moving');
        }
      }
    },
    [onPositionChange],
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      // Cancel all animations
      try {
        cancelAnimation(moving);
        cancelAnimation(prevY);
        cancelAnimation(transY);
        cancelAnimation(movedY);
        cancelAnimation(scrollY);
      } catch (error) {
        if (__DEV__) {
          console.warn('BottomSheet animation cleanup warning:', error);
        }
      }
    };
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      if (isMountedRef.current) {
        transY.value = withTiming(OPEN_BOTTOMSHEET_HEIGHT, {duration: 200}, () => {
          if (isMountedRef.current) {
            prevY.value = OPEN_BOTTOMSHEET_HEIGHT;
          }
        });
      }
    });

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  const scrollRef = useRef(null);
  const onScrollHandler = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    return (scrollY.value = Math.round(e.nativeEvent.contentOffset.y));
  };

  const gesture = Gesture.Pan()
    .onBegin(() => {
      moving.value = true;
    })
    .onUpdate(e => {
      if (
        (scrollY.value === 0 || prevY.value === CLOSED_BOTTOMSHEET_HEIGHT) &&
        prevY.value !== CLOSED_BOTTOMSHEET_HEIGHT
      ) {
        transY.value = prevY.value + e.translationY - movedY.value;
      } else {
        movedY.value = e.translationY;
      }
    })
    .onEnd(e => {
      if ((e.velocityY > 500 || e.translationY > 100) && scrollY.value === 0) {
        // Dismiss keyboard when closing bottom sheet
        runOnJS(Keyboard.dismiss)();
        transY.value = withTiming(CLOSED_BOTTOMSHEET_HEIGHT, {duration: 200});
        prevY.value = CLOSED_BOTTOMSHEET_HEIGHT;
      } else if (e.velocityY < -500 || e.translationY < -100) {
        transY.value = withTiming(OPEN_BOTTOMSHEET_HEIGHT, {duration: 200});
        prevY.value = OPEN_BOTTOMSHEET_HEIGHT;
      } else {
        transY.value = withTiming(prevY.value, {duration: 200});
      }
    })
    .onFinalize(() => {
      moving.value = false;
      movedY.value = 0;
    })
    .simultaneousWithExternalGesture(scrollRef);

  const animatedStyles = {
    sheet: useAnimatedStyle(() => ({
      transform: [
        {
          translateY: interpolate(
            transY.value,
            [0, OPEN_BOTTOMSHEET_HEIGHT, CLOSED_BOTTOMSHEET_HEIGHT, height],
            [
              OPEN_BOTTOMSHEET_HEIGHT,
              OPEN_BOTTOMSHEET_HEIGHT,
              CLOSED_BOTTOMSHEET_HEIGHT,
              CLOSED_BOTTOMSHEET_HEIGHT + 20,
            ],
            Extrapolate.CLAMP,
          ),
        },
      ],
      shadowOpacity: 0.15,
      backgroundColor: colors.white,
      borderRadius: 24,
      paddingTop: 12,
      paddingHorizontal: 12,
    })),
    scrollViewProps: useAnimatedProps(() => ({
      scrollEnabled: prevY.value === OPEN_BOTTOMSHEET_HEIGHT,
      bounces: scrollY.value > 0 || !moving.value,
    })),
  };

  return {onScrollHandler, gesture, animatedStyles, scrollRef};
};

export default useBottomSheetAnimations;
