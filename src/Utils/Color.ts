export const colors = [
  '#F45E23',
  '#F4C023',
  '#81B92B',
  '#0F0F6F',
  '#EB4D38',
  '#7E2A9C',
  '#C42F75',
  '#1F669D',
  '#2E2EAB',
  '#007AFF',
  '#2AB04D',
  '#58B2AD',
  '#F45E23',
  '#F4C023',
  '#81B92B',
  '#0F0F6F',
  '#EB4D38',
  '#7E2A9C',
  '#C42F75',
  '#1F669D',
  '#2E2EAB',
  '#007AFF',
  '#2AB04D',
  '#58B2AD',
];

export function shuffleArray(array: string[]) {
  let currentIndex = array.length,
    randomIndex;

  // While there remain elements to shuffle...
  while (currentIndex !== 0) {
    // Pick a remaining element...
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element.
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }

  return array;
}

export const randomizedColors = shuffleArray(colors);
