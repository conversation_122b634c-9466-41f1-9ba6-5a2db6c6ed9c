import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    buttonText: {
      fontWeight: '500',
      fontSize: 15,
      lineHeight: 19,
      marginHorizontal: 6,
      color: colors.black,
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    chevronContainer: {
      marginTop: 1,
    },
    listContainer: {
      position: 'absolute',
      right: 25,
      overflow: 'hidden',
      backgroundColor: colors.white,
      borderRadius: 10,
      borderColor: colors.textSecondary + '99',
      // width: 170,
    },
    dropdownItemContainer: {
      padding: 10,
      flexDirection: 'row',
      alignItems: 'center',
      overflow: 'hidden',
      justifyContent: 'space-between',
    },
    dropdownText: {
      fontWeight: '500',
      fontSize: 15,
      color: colors.warning,
      marginRight: 15,
    },
    dropdownRadius: {
      fontSize: 15,
      fontWeight: '400',
      color: colors.textSecondary,
    },
    flex: {
      flex: 1,
    },
  });

export default getStyles;
