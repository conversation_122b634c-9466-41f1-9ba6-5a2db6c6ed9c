import React, {useEffect, useRef} from 'react';
import {View, Text, Animated, Easing, useWindowDimensions} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation} from '@react-navigation/native';
import {PyxiLabel} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';
import {ThemedStatusBar} from '~components/ThemedStatusBar';
import {spacing, typography, shadows} from '~constants/design';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';

const ModernSplash: React.FC = () => {
  const {colors} = useTheme();
  const {height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();

  // Animation values
  const logoScale = useRef(new Animated.Value(0.5)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  // Magical particle animations
  const particle1 = useRef(new Animated.Value(0)).current;
  const particle2 = useRef(new Animated.Value(0)).current;
  const particle3 = useRef(new Animated.Value(0)).current;
  const particle4 = useRef(new Animated.Value(0)).current;
  const particle5 = useRef(new Animated.Value(0)).current;

  // Floating elements
  const floatingElement1 = useRef(new Animated.Value(0)).current;
  const floatingElement2 = useRef(new Animated.Value(0)).current;
  const floatingElement3 = useRef(new Animated.Value(0)).current;

  // Shimmer effect
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start animations sequence
    const animationSequence = Animated.sequence([
      // Background fade in
      Animated.timing(backgroundOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      // Logo scale and fade in
      Animated.parallel([
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 800,
          easing: Easing.out(Easing.back(1.2)),
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      // Text fade in
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]);

    // Pulse animation for logo
    const pulseLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.05,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]),
    );

    // Start animations
    animationSequence.start();

    // Start pulse after initial animation
    setTimeout(() => {
      pulseLoop.start();
    }, 1300);

    // Navigate to intro after 3 seconds
    const timer = setTimeout(() => {
      navigation.replace(SCREENS.MODERN_INTRO);
    }, 3000);

    return () => {
      clearTimeout(timer);
      pulseLoop.stop();
    };
  }, []);

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    gradientBackground: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      paddingHorizontal: spacing.xl,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: spacing.xl,
    },
    logoWrapper: {
      transform: [{scale: Animated.multiply(logoScale, pulseAnimation)}],
    },
    textContainer: {
      alignItems: 'center' as const,
    },
    appName: {
      fontSize: typography.fontSize['4xl'],
      fontWeight: typography.fontWeight.black,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      marginBottom: spacing.sm,
      letterSpacing: -0.5,
    },
    tagline: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      textAlign: 'center' as const,
      letterSpacing: 0.2,
    },
    loadingContainer: {
      position: 'absolute' as const,
      bottom: spacing['4xl'],
      alignSelf: 'center' as const,
    },
    loadingDots: {
      flexDirection: 'row' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: colors.primary,
      marginHorizontal: spacing.xs,
    },
  };

  // Animated loading dots
  const LoadingDots = () => {
    const dot1 = useRef(new Animated.Value(0.3)).current;
    const dot2 = useRef(new Animated.Value(0.3)).current;
    const dot3 = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
      const animateDots = () => {
        const createDotAnimation = (dot: Animated.Value, delay: number) =>
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dot, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0.3,
              duration: 400,
              useNativeDriver: true,
            }),
          ]);

        Animated.loop(
          Animated.parallel([
            createDotAnimation(dot1, 0),
            createDotAnimation(dot2, 200),
            createDotAnimation(dot3, 400),
          ]),
        ).start();
      };

      const timer = setTimeout(animateDots, 1500);
      return () => clearTimeout(timer);
    }, []);

    return (
      <View style={styles.loadingDots}>
        <Animated.View style={[styles.dot, {opacity: dot1}]} />
        <Animated.View style={[styles.dot, {opacity: dot2}]} />
        <Animated.View style={[styles.dot, {opacity: dot3}]} />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={[]}>
      <ThemedStatusBar translucent />

      {/* Animated Background */}
      <Animated.View style={[styles.gradientBackground, {opacity: backgroundOpacity}]}>
        <LinearGradient
          colors={[colors.primary, colors.primaryLight, colors.warning, colors.warningLight]}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={styles.gradientBackground}
        />
      </Animated.View>

      {/* Content */}
      <View style={styles.contentContainer}>
        {/* Logo */}
        <Animated.View style={[styles.logoContainer, {opacity: logoOpacity}]}>
          <Animated.View style={styles.logoWrapper}>
            <PyxiLabel />
          </Animated.View>
        </Animated.View>

        {/* Text */}
        <Animated.View style={[styles.textContainer, {opacity: textOpacity}]}>
          <Text style={styles.appName}>Pyxi</Text>
          <Text style={styles.tagline}>Discover • Connect • Experience</Text>
        </Animated.View>
      </View>

      {/* Loading Indicator */}
      <View style={styles.loadingContainer}>
        <LoadingDots />
      </View>
    </SafeAreaView>
  );
};

export default ModernSplash;
