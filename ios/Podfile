# Podfile

platform :ios, '15.0'

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p', 'require.resolve("react-native/scripts/react_native_pods.rb", {paths: [process.argv[1]]}, )', __dir__]).strip

# react-native-permissions
require Pod::Executable.execute_command('node', ['-p', 'require.resolve("react-native-permissions/scripts/setup.rb", {paths: [process.argv[1]]}, )', __dir__]).strip

workspace 'Example.xcworkspace'

# Setup Permissions for react-native-permissions
setup_permissions(['Calendars', 'CalendarsWriteOnly', 'Camera'])

prepare_react_native_project!
use_frameworks!
use_frameworks! :linkage => :static
$RNFirebaseAsStaticFramework = true
$RNFirebaseAnalyticsWithoutAdIdSupport = true

# Exclude `react-native-flipper` if NO_FLIPPER is set
# Comment out Flipper configuration to disable it
# flipper_config = FlipperConfiguration.enabled
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'pyxida' do
  config = use_native_modules!

  # Facebook SDK
  pod 'FBSDKCoreKit', '~> 13.0'
  pod 'FBSDKLoginKit', '~> 13.0'
  pod 'GoogleMaps'
  # React Native Maps dependencies
  pod 'Google-Maps-iOS-Utils', '4.2.2'
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true


  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true,
    :fabric_enabled => flags[:fabric_enabled],
    # Comment out Flipper configuration to disable it
    # :flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # **Add the EXCLUDED_ARCHS fix here**
    installer.pods_project.build_configurations.each do |config|
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
    end
    react_native_post_install(installer, config[:reactNativePath], :mac_catalyst_enabled => false)

    bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
   def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
     framework_path = File.join(Dir.pwd, framework_relative_path)
     command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
     puts "Stripping bitcode: #{command}"
     system(command)
   end

   framework_paths = [
     "Pods/LogRocket/LogRocket.xcframework/ios-arm64/LogRocket.framework/LogRocket",
     "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/Versions/Current/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
     "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-maccatalyst/hermes.framework/hermes",

     "Pods/FBAEMKit/XCFrameworks/FBAEMKit.xcframework/ios-arm64/FBAEMKit.framework/FBAEMKit",
     "Pods/FBSDKCoreKit/XCFrameworks/FBSDKCoreKit.xcframework/ios-arm64/FBSDKCoreKit.framework/FBSDKCoreKit",
     "Pods/FBSDKCoreKit_Basics/XCFrameworks/FBSDKCoreKit_Basics.xcframework/ios-arm64/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
     "Pods/FBSDKLoginKit/XCFrameworks/FBSDKLoginKit.xcframework/ios-arm64/FBSDKLoginKit.framework/FBSDKLoginKit",
   ]

   framework_paths.each do |framework_relative_path|
     strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
   end
    # Update target version for each library to 13.4
    installer.pods_project.targets.each do |target|
      if target.name == 'BoringSSL-GRPC'
        target.source_build_phase.files.each do |file|
          if file.settings && file.settings['COMPILER_FLAGS']
            flags = file.settings['COMPILER_FLAGS'].split
            flags.reject! { |flag| flag == '-GCC_WARN_INHIBIT_ALL_WARNINGS' }
            file.settings['COMPILER_FLAGS'] = flags.join(' ')
          end
        end
      end
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
      end
    end

    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end

target 'OneSignalNotificationServiceExtension' do
  pod 'OneSignalXCFramework', '>= 3.0.0', '< 4.0'
end
