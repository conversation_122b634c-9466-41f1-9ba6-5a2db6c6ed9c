import axios from 'axios';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StyleSheet} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import DeviceCountry, {TYPE_CONFIGURATION} from 'react-native-device-country';
import {PromoCodes, Ticket} from '~types/api/event';
import {useTheme} from '~contexts/ThemeContext';

export interface TicketType {
  name: string;
  price: string;
  discountedPrice: string;
  numberOfTickets: string;
  description: string;
  currency?: string;
}

export interface PromoCode {
  name: string;
  percentDiscount: string;
  description: string;
}

export interface FormData {
  ticketTypes: TicketType[];
  promoCodes: PromoCode[];
}

export type TicketFormHandle = {
  validateForm: () => boolean;
  getFormData: () => FormData;
};

export type FormProps = {
  tickets: Ticket[];
  promoCode: PromoCodes[];
};

const TicketForm = forwardRef<TicketFormHandle, FormProps>(({tickets, promoCode}, ref) => {
  const {colors} = useTheme();
  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([
    {name: '', price: '', discountedPrice: '', numberOfTickets: '', description: ''},
  ]);
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [currency, setCurrency] = useState<string>('');

  useEffect(() => {
    if (tickets && tickets.length > 0) {
      const types: TicketType[] = tickets.map(ticket => {
        const item: TicketType = {
          name: ticket.name,
          price: ticket.price + '',
          discountedPrice: ticket.discounted_price + '',
          numberOfTickets: ticket.capacity + '',
          description: ticket.description,
        };
        return item;
      });
      setTicketTypes(types);
    }
  }, [tickets]);

  useEffect(() => {
    if (promoCode && promoCode.length > 0) {
      const types: PromoCode[] = promoCode.map(promo => {
        const item: PromoCode = {
          name: promo.name,
          percentDiscount: promo.discount_percentage + '',
          description: promo.description,
        };
        return item;
      });
      setPromoCodes(types);
    }
  }, [promoCode]);

  useEffect(() => {
    const fetchCurrency = async () => {
      try {
        DeviceCountry.getCountryCode(TYPE_CONFIGURATION)
          .then(result => {
            console.log(result, 'result.code');
            // Map country code to currency
            const countryToCurrency = {
              US: 'USD',
              IN: 'INR',
              GB: 'GBP',
              AL: 'ALL', // Albania
              AD: 'EUR', // Andorra
              AT: 'EUR', // Austria
              BY: 'BYN', // Belarus
              BE: 'EUR', // Belgium
              BA: 'BAM', // Bosnia and Herzegovina
              BG: 'BGN', // Bulgaria
              HR: 'EUR', // Croatia
              CY: 'EUR', // Cyprus
              CZ: 'CZK', // Czech Republic
              DK: 'DKK', // Denmark
              EE: 'EUR', // Estonia
              FI: 'EUR', // Finland
              FR: 'EUR', // France
              GE: 'GEL', // Georgia
              DE: 'EUR', // Germany
              GR: 'EUR', // Greece
              HU: 'HUF', // Hungary
              IS: 'ISK', // Iceland
              IE: 'EUR', // Ireland
              IT: 'EUR', // Italy
              LV: 'EUR', // Latvia
              LI: 'CHF', // Liechtenstein
              LT: 'EUR', // Lithuania
              LU: 'EUR', // Luxembourg
              MT: 'EUR', // Malta
              MD: 'MDL', // Moldova
              MC: 'EUR', // Monaco
              ME: 'EUR', // Montenegro
              NL: 'EUR', // Netherlands
              MK: 'MKD', // North Macedonia
              NO: 'NOK', // Norway
              PL: 'PLN', // Poland
              PT: 'EUR', // Portugal
              RO: 'RON', // Romania
              RU: 'RUB', // Russia
              SM: 'EUR', // San Marino
              RS: 'RSD', // Serbia
              SK: 'EUR', // Slovakia
              SI: 'EUR', // Slovenia
              ES: 'EUR', // Spain
              SE: 'SEK', // Sweden
              CH: 'CHF', // Switzerland
              TR: 'TRY', // Turkey
              UA: 'UAH', // Ukraine
              VA: 'EUR', // Vatican City
              // Add more mappings as needed
            };

            //@ts-ignore
            setCurrency(countryToCurrency[result.code] || 'USD'); // Default to USD if not found
            // {"code": "BY", "type": "telephony"}
          })
          .catch(e => {
            console.log(e);
            setCurrency('USD'); // Fallback to USD
          });
      } catch (error) {
        console.log('Error fetching location:', error);
        setCurrency('USD'); // Fallback to USD
      }
    };

    fetchCurrency();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      validateForm: () => {
        return validateFields();
      },
      getFormData: () => {
        console.log('getfo', {ticketTypes, promoCodes});

        return {ticketTypes: ticketTypes, promoCodes: promoCodes};
      },
    }),
    [ticketTypes, promoCodes],
  );

  const validateFields = () => {
    let newErrors: {[key: string]: string} = {};

    ticketTypes.forEach((ticket, index) => {
      if (!ticket.name.trim()) {
        newErrors[`ticket-name-${index}`] = 'Name is required';
      }
      if (!ticket.numberOfTickets.trim()) {
        newErrors[`ticket-numberOfTickets-${index}`] = 'Number of tickets is required';
      }
      if (!ticket.price.trim()) {
        newErrors[`ticket-price-${index}`] = 'Price is required';
      }
    });

    promoCodes.forEach((promo, index) => {
      if (!promo.name.trim()) {
        newErrors[`promo-name-${index}`] = 'Promo code is required';
      }
      if (!promo.percentDiscount.trim()) {
        newErrors[`promo-percentDiscount-${index}`] = 'Discount percentage is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTicketChange = (index: number, field: keyof TicketType, value: string) => {
    const updatedTickets = [...ticketTypes];
    updatedTickets[index][field] = value;
    updatedTickets[index].currency = currency;
    setTicketTypes(updatedTickets);

    let newErrors = {...errors};
    if (value.trim()) {
      delete newErrors[`ticket-${field}-${index}`];
    } else {
      const fieldName =
        field === 'numberOfTickets'
          ? 'Number of tickets'
          : field === 'name'
            ? 'Name'
            : field === 'price'
              ? 'Price'
              : field === 'discountedPrice'
                ? 'Discounted price'
                : field === 'description'
                  ? 'Description'
                  : field;
      newErrors[`ticket-${field}-${index}`] = `${fieldName} is required`;
    }
    setErrors(newErrors);
  };

  const handlePromoChange = (index: number, field: keyof PromoCode, value: string) => {
    const updatedPromoCodes = [...promoCodes];
    updatedPromoCodes[index][field] = value;
    setPromoCodes(updatedPromoCodes);

    let newErrors = {...errors};
    if (value.trim()) {
      delete newErrors[`promo-${field}-${index}`];
    } else {
      const fieldName =
        field === 'percentDiscount'
          ? 'Discount percentage'
          : field === 'name'
            ? 'Promo code'
            : field === 'description'
              ? 'Description'
              : field;
      newErrors[`promo-${field}-${index}`] = `${fieldName} is required`;
    }
    setErrors(newErrors);
  };

  const addTicketType = () => {
    setTicketTypes([...ticketTypes, {name: '', price: '', discountedPrice: '', numberOfTickets: '', description: ''}]);
  };

  const removeTicketType = (index: number) => {
    if (ticketTypes.length > 1) {
      setTicketTypes(ticketTypes.filter((_, i) => i !== index));
    }
  };

  const addPromoCode = () => {
    setPromoCodes([...promoCodes, {name: '', percentDiscount: '', description: ''}]);
  };

  const removePromoCode = (index: number) => {
    setPromoCodes(promoCodes.filter((_, i) => i !== index));
  };

  const styles = StyleSheet.create({
    currencyText: {
      marginRight: 5,
      fontSize: 14,
      fontWeight: '500',
      color: colors.textSecondary,
    },
    errorText: {
      color: colors.error,
      fontSize: 12,
      marginTop: 4,
    },
    errorInput: {
      borderColor: colors.error,
      borderWidth: 1,
    },
    headerComponent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    container: {
      paddingVertical: 20,
    },
    header: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    card: {
      paddingVertical: 8,
      borderRadius: 8,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    input: {
      padding: 10,
      borderRadius: 6,
      backgroundColor: colors.background,
    },
    halfWidth: {
      width: '48%',
    },
    deleteIcon: {
      alignSelf: 'flex-end',
      marginTop: 5,
    },
    addButton: {
      alignItems: 'center',
    },
    submitButton: {
      backgroundColor: colors.statusBlue,
      padding: 12,
      borderRadius: 6,
      alignItems: 'center',
      marginTop: 20,
    },
    submitText: {
      color: colors.white,
      fontSize: 16,
    },
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.headerComponent}>
        <Text style={styles.header}>Ticket Types</Text>
        <TouchableOpacity onPress={addTicketType} style={styles.addButton}>
          <Ionicons name="add-circle" size={24} color={colors.eventInfluencer} />
        </TouchableOpacity>
      </View>
      {ticketTypes.map((ticket, index) => (
        <View key={index} style={styles.card}>
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <TextInput
                placeholder="Name Of Ticket*"
                value={ticket.name}
                onChangeText={value => handleTicketChange(index, 'name', value)}
                style={styles.input}
              />
              {errors[`ticket-name-${index}`] && <Text style={styles.errorText}>{errors[`ticket-name-${index}`]}</Text>}
            </View>
            <View style={styles.halfWidth}>
              <TextInput
                placeholder="Number of Tickets*"
                value={ticket.numberOfTickets}
                keyboardType="numeric"
                onChangeText={value => handleTicketChange(index, 'numberOfTickets', value)}
                style={styles.input}
              />
              {errors[`ticket-numberOfTickets-${index}`] && (
                <Text style={styles.errorText}>{errors[`ticket-numberOfTickets-${index}`]}</Text>
              )}
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={[styles.input, {flexDirection: 'row', alignItems: 'center'}]}>
                <Text style={styles.currencyText}>{currency}</Text>
                <TextInput
                  placeholder="Price*"
                  value={ticket.price}
                  keyboardType="numeric"
                  onChangeText={value => handleTicketChange(index, 'price', value)}
                  style={{flex: 1}}
                />
              </View>
              {errors[`ticket-price-${index}`] && (
                <Text style={styles.errorText}>{errors[`ticket-price-${index}`]}</Text>
              )}
            </View>
            <View style={styles.halfWidth}>
              <View style={[styles.input, {flexDirection: 'row', alignItems: 'center'}]}>
                <Text style={styles.currencyText}>{currency}</Text>
                <TextInput
                  placeholder="Discounted Price"
                  value={ticket.discountedPrice}
                  keyboardType="numeric"
                  onChangeText={value => handleTicketChange(index, 'discountedPrice', value)}
                  style={{flex: 1}}
                />
              </View>
              {errors[`ticket-discountedPrice-${index}`] && (
                <Text style={styles.errorText}>{errors[`ticket-discountedPrice-${index}`]}</Text>
              )}
            </View>
          </View>
          <TextInput
            placeholder="Description"
            value={ticket.description}
            onChangeText={value => handleTicketChange(index, 'description', value)}
            style={styles.input}
          />
          {ticketTypes.length > 1 && (
            <TouchableOpacity onPress={() => removeTicketType(index)} style={styles.deleteIcon}>
              <Ionicons name="trash" size={24} color={colors.error} />
            </TouchableOpacity>
          )}
        </View>
      ))}
      <View style={[styles.headerComponent, {marginTop: 14}]}>
        <Text style={styles.header}>Promo Codes</Text>
        <TouchableOpacity onPress={addPromoCode} style={styles.addButton}>
          <Ionicons name="add-circle" size={24} color={colors.eventInfluencer} />
        </TouchableOpacity>
      </View>
      {promoCodes.map((promo, index) => (
        <View key={index} style={styles.card}>
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <TextInput
                placeholder="Promo Code*"
                value={promo.name}
                onChangeText={value => handlePromoChange(index, 'name', value)}
                style={styles.input}
              />
              {errors[`promo-name-${index}`] && <Text style={styles.errorText}>{errors[`promo-name-${index}`]}</Text>}
            </View>
            <View style={styles.halfWidth}>
              <TextInput
                placeholder="Discount Percentage*"
                value={promo.percentDiscount}
                keyboardType="numeric"
                onChangeText={value => handlePromoChange(index, 'percentDiscount', value)}
                style={styles.input}
              />
              {errors[`promo-percentDiscount-${index}`] && (
                <Text style={styles.errorText}>{errors[`promo-percentDiscount-${index}`]}</Text>
              )}
            </View>
          </View>
          <TextInput
            placeholder="Description"
            value={promo.description}
            onChangeText={value => handlePromoChange(index, 'description', value)}
            style={styles.input}
          />
          <TouchableOpacity onPress={() => removePromoCode(index)} style={styles.deleteIcon}>
            <Ionicons name="trash" size={24} color={colors.error} />
          </TouchableOpacity>
        </View>
      ))}
    </ScrollView>
  );
});

export default TicketForm;
