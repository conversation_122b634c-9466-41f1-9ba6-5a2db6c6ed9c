import {useCallback, useRef, useState, useEffect} from 'react';
import {QueryClient} from 'react-query';

// Enhanced debounce hook with immediate execution option
export const useSmartDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  options: {
    immediate?: boolean; // Execute immediately on first call
    maxWait?: number; // Maximum time to wait before forcing execution
    leading?: boolean; // Execute on leading edge
    trailing?: boolean; // Execute on trailing edge
  } = {},
) => {
  const {immediate = false, maxWait, leading = false, trailing = true} = options;

  const timeoutRef = useRef<NodeJS.Timeout>();
  const maxTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTimeRef = useRef<number>(0);
  const lastExecTimeRef = useRef<number>(0);
  const argsRef = useRef<Parameters<T>>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      lastCallTimeRef.current = now;
      argsRef.current = args;

      const execute = () => {
        lastExecTimeRef.current = Date.now();
        callback(...args);
      };

      // Clear existing timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Leading edge execution
      if (leading && now - lastExecTimeRef.current >= delay) {
        execute();
        return;
      }

      // Immediate execution on first call
      if (immediate && lastExecTimeRef.current === 0) {
        execute();
        return;
      }

      // Set up trailing edge execution
      if (trailing) {
        timeoutRef.current = setTimeout(execute, delay);
      }

      // Max wait enforcement
      if (maxWait && !maxTimeoutRef.current) {
        maxTimeoutRef.current = setTimeout(() => {
          if (argsRef.current) {
            execute();
          }
          maxTimeoutRef.current = undefined;
        }, maxWait);
      }
    },
    [callback, delay, immediate, maxWait, leading, trailing],
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

// Search result caching with LRU eviction
class SearchCache {
  private cache = new Map<string, {data: any; timestamp: number; hits: number}>();
  private maxSize = 50;
  private maxAge = 1000 * 60 * 10; // 10 minutes

  set(key: string, data: any) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hits: 0,
    });
  }

  get(key: string) {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // Update hit count and move to end (LRU)
    entry.hits++;
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

// Global search cache instance
export const searchCache = new SearchCache();

// Request deduplication for identical API calls
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();

  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // If request is already pending, return the existing promise
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!;
    }

    // Create new request
    const promise = requestFn().finally(() => {
      // Clean up after request completes
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  clear() {
    this.pendingRequests.clear();
  }
}

export const requestDeduplicator = new RequestDeduplicator();

// Background prefetching based on user behavior
export const usePredictivePrefetch = (queryClient: QueryClient) => {
  const prefetchTimeoutRef = useRef<NodeJS.Timeout>();

  const schedulePrefetech = useCallback((prefetchFn: () => Promise<void>, delay = 2000) => {
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
    }

    prefetchTimeoutRef.current = setTimeout(async () => {
      try {
        await prefetchFn();
      } catch (error) {
        console.warn('Prefetch failed:', error);
      }
    }, delay);
  }, []);

  useEffect(() => {
    return () => {
      if (prefetchTimeoutRef.current) {
        clearTimeout(prefetchTimeoutRef.current);
      }
    };
  }, []);

  return {schedulePrefetech};
};

// Optimistic search suggestions
export const useOptimisticSearch = () => {
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const updateSuggestions = useCallback((newSuggestions: any[], isOptimistic = false) => {
    setSuggestions(prev => {
      if (isOptimistic) {
        // Merge optimistic results with existing ones
        const merged = [...newSuggestions, ...prev];
        return merged.slice(0, 10); // Limit to 10 suggestions
      }
      return newSuggestions;
    });
  }, []);

  return {
    suggestions,
    isLoading,
    setIsLoading,
    updateSuggestions,
  };
};
