import React from 'react';
import {TouchableOpacity, Text, ViewStyle, TextStyle, GestureResponderEvent} from 'react-native';
import ModernSpinner from '~components/ModernSpinner';
import {typography, spacing, borderRadius, shadows} from '~constants/design';
import {haptics} from '~utils/haptics';
import {useTheme} from '~contexts/ThemeContext';

interface ModernButtonProps {
  title: string;
  onPress: (event?: GestureResponderEvent) => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  hapticFeedback?: boolean;
  hapticType?: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error';
}

const ModernButton: React.FC<ModernButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  hapticFeedback = true,
  hapticType = 'medium',
}) => {
  const {colors} = useTheme();

  const getButtonStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: borderRadius.lg,
      ...shadows.none,
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: spacing.md,
        paddingVertical: spacing.xs,
        minHeight: 36,
      },
      md: {
        paddingHorizontal: spacing.lg,
        paddingVertical: spacing.sm,
        minHeight: 44,
      },
      lg: {
        paddingHorizontal: spacing.xl,
        paddingVertical: spacing.md,
        minHeight: 52,
      },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: colors.primary,
        borderWidth: 0,
      },
      secondary: {
        backgroundColor: colors.secondary,
        borderWidth: 0,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 0.5,
        borderColor: colors.primary,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        ...shadows.none,
      },
      danger: {
        backgroundColor: colors.error,
        borderWidth: 0,
      },
    };

    // Disabled styles
    const disabledStyles: ViewStyle = disabled
      ? {
          opacity: 0.6,
          ...shadows.none,
        }
      : {};

    // Full width styles
    const widthStyles: ViewStyle = fullWidth ? {width: '100%'} : {};

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...disabledStyles,
      ...widthStyles,
    };
  };

  const getTextStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      fontWeight: typography.fontWeight.semibold,
      textAlign: 'center',
    };

    // Size styles
    const sizeStyles: Record<string, TextStyle> = {
      sm: {
        fontSize: typography.fontSize.sm,
        lineHeight: typography.fontSize.sm * typography.lineHeight.normal,
      },
      md: {
        fontSize: typography.fontSize.base,
        lineHeight: typography.fontSize.base * typography.lineHeight.normal,
      },
      lg: {
        fontSize: typography.fontSize.lg,
        lineHeight: typography.fontSize.lg * typography.lineHeight.normal,
      },
    };

    // Variant styles
    const variantStyles: Record<string, TextStyle> = {
      primary: {
        color: colors.white,
      },
      secondary: {
        color: colors.white,
      },
      outline: {
        color: colors.primary,
      },
      ghost: {
        color: colors.primary,
      },
      danger: {
        color: colors.white,
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ModernSpinner
          size={18}
          color={variant === 'outline' || variant === 'ghost' ? colors.primary : colors.white}
          variant="circular"
        />
      );
    }

    const textElement = (
      <Text style={[getTextStyles(), textStyle]} numberOfLines={1}>
        {title}
      </Text>
    );

    if (!icon) {
      return textElement;
    }

    return (
      <>
        {iconPosition === 'left' && icon}
        {iconPosition === 'left' && <Text style={{width: spacing.xs}} />}
        {textElement}
        {iconPosition === 'right' && <Text style={{width: spacing.xs}} />}
        {iconPosition === 'right' && icon}
      </>
    );
  };

  const handlePress = (event?: GestureResponderEvent) => {
    if (hapticFeedback && !disabled && !loading) {
      switch (hapticType) {
        case 'light':
          haptics.light();
          break;
        case 'heavy':
          haptics.heavy();
          break;
        case 'success':
          haptics.success();
          break;
        case 'warning':
          haptics.warning();
          break;
        case 'error':
          haptics.error();
          break;
        case 'medium':
        default:
          haptics.medium();
          break;
      }
    }
    onPress(event);
  };

  return (
    <TouchableOpacity
      style={[getButtonStyles(), style]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}>
      {renderContent()}
    </TouchableOpacity>
  );
};

export default ModernButton;
