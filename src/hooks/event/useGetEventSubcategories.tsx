import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetEventCategoriesResponse} from '~types/api/event';

export function useGetEventSubcategories(event_id?: number) {
  return useQuery<GetEventCategoriesResponse, Error>(
    ['userEventCategories', event_id],
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}events/${event_id}/subcategories`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to create the event');
      }

      const data = await response.json();

      return data as GetEventCategoriesResponse;
    },
    {
      enabled: !!event_id,
    },
  );
}
