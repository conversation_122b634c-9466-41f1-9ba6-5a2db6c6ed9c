import AsyncStorage from '@react-native-async-storage/async-storage';
import OneSignal from 'react-native-onesignal';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';
import {immer} from 'zustand/middleware/immer';

interface NotificationState {
  isNotificationsEnabled: boolean;
}

interface NotificationsActions {
  setIsNotificationsEnabled: (value: boolean) => void;
}

interface LanguageState {
  language: string;
}

interface LanguageActions {
  setLanguage: (value: string) => void;
}

const useNotifications = create<NotificationState & NotificationsActions>()(
  persist(
    immer(set => ({
      isNotificationsEnabled: true,
      setIsNotificationsEnabled: async (value: boolean) => {
        OneSignal.disablePush(!value);
        set({
          isNotificationsEnabled: value,
        });
      },
    })),
    {
      name: 'notifications-storage',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

const useLanguage = create<LanguageState & LanguageActions>()(
  persist(
    immer(set => ({
      language: 'en',
      setLanguage: (value: string) =>
        set({
          language: value,
        }),
    })),
    {name: 'language-storage', storage: createJSONStorage(() => AsyncStorage)},
  ),
);

export {useNotifications, useLanguage};
