import {FC, useEffect, useState} from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import {Switch} from 'react-native-switch';
import {LocationBoxIcon} from '~/assets/icons';
import LocationModal, {Location} from '~/components/LocationModal';
import createStyles from './styles';
import {useTranslation} from 'react-i18next';
import React from 'react';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  location: Location | null;
  onChangeLocation: (value: Location | null) => void;
  errorText: string | undefined;
}

const LocationToggle: FC<IProps> = ({location, onChangeLocation, errorText}) => {
  const {t} = useTranslation();
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const [isActive, setIsActive] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);

  console.log(location, 'locationlocationlocation');

  useEffect(() => {
    if (location && location.address) {
      setIsActive(true);
    }
  }, [location]);

  const onChange = () => {
    if (isActive) {
      onChangeLocation(null);
    } else {
      setIsLocationModalOpen(true);
    }
    setIsActive(prevState => !prevState);
  };

  useEffect(() => {
    if (!isLocationModalOpen && !location) {
      setIsActive(false);
    }
  }, [isLocationModalOpen]);

  return (
    <>
      <TouchableOpacity style={styles.container} onPress={() => setIsLocationModalOpen(true)}>
        <Image
          style={[styles.pinImageSize, {tintColor: isActive ? colors.eventInfluencer : colors.gray400}]}
          resizeMode={'contain'}
          source={require('../../assets/images/pinSelf.png')}
        />
        <View
          style={{
            ...styles.rightContainer,
          }}>
          <View style={{flex: 1}}>
            {!location?.address && <Text style={styles.title}>{t('generic.location')}</Text>}
            {errorText && (
              <TouchableOpacity onPress={() => setIsLocationModalOpen(true)}>
                <Text style={styles.location}>{errorText}</Text>
              </TouchableOpacity>
            )}
            {location?.address && (
              <Text style={[styles.location, {color: colors.statusBlue}]}>{location?.address}</Text>
            )}
          </View>
          {false && (
            <Switch
              value={isActive}
              onValueChange={onChange}
              backgroundActive={colors.statusPurple}
              backgroundInactive={colors.gray400 + '29'}
              renderActiveText={false}
              renderInActiveText={false}
              circleBorderWidth={0}
              circleSize={27}
              switchWidthMultiplier={2.2}
              barHeight={31}
            />
          )}
        </View>
      </TouchableOpacity>
      <LocationModal
        isVisible={isLocationModalOpen}
        close={() => {
          setIsLocationModalOpen(false);
        }}
        onLocationChange={onChangeLocation}
        withAccurateAddress={true}
      />
    </>
  );
};

export default LocationToggle;
