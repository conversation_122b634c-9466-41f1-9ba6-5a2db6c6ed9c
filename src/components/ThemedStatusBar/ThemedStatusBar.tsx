import React from 'react';
import {StatusBar, Platform} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface ThemedStatusBarProps {
  translucent?: boolean;
  hidden?: boolean;
}

export const ThemedStatusBar: React.FC<ThemedStatusBarProps> = ({translucent = false, hidden = false}) => {
  const {isDarkMode, colors} = useTheme();

  return (
    <StatusBar
      barStyle={isDarkMode ? 'light-content' : 'dark-content'}
      backgroundColor={Platform.OS === 'android' ? colors.background : undefined}
      translucent={translucent}
      hidden={hidden}
      animated={true}
    />
  );
};

export default ThemedStatusBar;
