import React from 'react';
import {useTranslation} from 'react-i18next';
import {Linking, Platform, Pressable, Text, View, ScrollView} from 'react-native';
import Animated, {FadeInDown} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import JoinModalWithButtons from '../../JoinModalWithButtons';
import {detailStyles} from './styles';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  name: string;
  description: string;
  isVisible: boolean;
  close: () => void;
  onPress: () => () => void;
  payment_url: string;
}

const ModalWithJoin = ({name, description, isVisible, close, onPress, payment_url}: IProps) => {
  const {colors} = useTheme();
  const styles = detailStyles(colors);
  const {t} = useTranslation();
  const isAndroid = Platform.OS === 'android';
  const {bottom} = useSafeAreaInsets();

  const handleSubmit = async () => {
    await Linking.openURL(payment_url);
  };

  return (
    <JoinModalWithButtons
      onCloseModal={close}
      modalIsVisible={isVisible}
      onPress={onPress}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingTop: 10,
          overflow: 'hidden',
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
        }}>
        <View
          style={{
            width: '100%',
            paddingHorizontal: 16,
            flex: 1,
          }}>
          <Animated.Text style={styles.header} entering={FadeInDown.delay(200)}>
            {name}
          </Animated.Text>

          <Pressable style={{flex: 1}}>
            <ScrollView style={{flex: 1, maxHeight: 100}} contentContainerStyle={{flexGrow: 1}}>
              <Animated.Text style={styles.description} entering={FadeInDown.delay(300)}>
                {description}
              </Animated.Text>
            </ScrollView>
          </Pressable>
        </View>

        <View
          style={{
            position: 'absolute',
            width: '100%',
            bottom: 30,
            paddingHorizontal: 16,
            overflow: 'hidden',
          }}>
          <Text style={{fontSize: 14, fontWeight: '600'}}>
            You've joined! Please proceed to buy your ticket and check out some cool mates below to enjoy the event
            with!
          </Text>

          <Button
            onPress={() => handleSubmit()}
            label={t('events.pay_here')}
            containerStyle={{
              width: '100%',
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: colors.primary,
              borderRadius: 6,
              marginTop: 24,
              marginBottom: 24,
            }}
            textStyle={{color: colors.white, fontSize: 15, fontWeight: '500'}}
          />
        </View>
      </View>
    </JoinModalWithButtons>
  );
};

export default ModalWithJoin;
