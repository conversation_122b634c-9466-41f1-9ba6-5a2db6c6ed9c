import DateTimePicker from 'react-native-date-picker';
import moment from 'moment-timezone';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {runOnJS} from 'react-native-reanimated';
import AndroidDatePicker from './AndroidDatePicker';
import {Platform} from 'react-native';

interface IProps {
  handleChooseMinDate?: (value?: Date) => void;
  handleChooseMaxDate?: (value?: Date) => void;
  minDate?: moment.Moment | null;
  maxDate?: moment.Moment | null;
  type: 'from' | 'to';
}

const isAndroid = Platform.OS === 'android';

const DatePicker = ({type = 'from', handleChooseMinDate, handleChooseMaxDate, minDate, maxDate}: IProps) => {
  const [date, setDate] = useState(moment().toDate());
  useEffect(() => {
    if (type === 'to' && maxDate) {
      setDate(maxDate?.clone().toDate());
    }
    if (type === 'from' && minDate) {
      setDate(minDate?.clone().toDate());
    }
  }, [maxDate, minDate, type]);

  const handleChangeDate = useCallback(
    (changedDate?: Date) => {
      if (changedDate) {
        if (type === 'to') {
          handleChooseMaxDate && runOnJS(handleChooseMaxDate)(changedDate);
        }
        if (type === 'from') {
          handleChooseMinDate && runOnJS(handleChooseMinDate)(changedDate);
        }
      }
    },
    [handleChooseMaxDate, handleChooseMinDate, type],
  );

  const maximumDate = useMemo(() => (type === 'from' ? maxDate?.toDate() : undefined), [maxDate, type]);
  const minimumDate = useMemo(() => (type === 'to' ? minDate?.toDate() : undefined), [minDate, type]);

  return (
    <>
      {isAndroid ? (
        <AndroidDatePicker
          date={date || moment().toDate()}
          onDateChange={handleChangeDate}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
        />
      ) : (
        <DateTimePicker
          locale="en-US"
          date={date || moment().toDate()}
          onDateChange={handleChangeDate}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
          mode="date"
        />
      )}
    </>
  );
};

export default DatePicker;
