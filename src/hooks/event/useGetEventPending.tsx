import axios from 'axios';
import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {SUBSCRIPTION_STATUS, User} from '~types/api/user';

// this query is copy of useGetEventAttendees. We have 2 same queries because in case you go from
// details screen to requiring confirmation you can see pending users as in host field
export function useGetEventPending({event_id}: {event_id: number}) {
  return useQuery<{user: User; event_id: number; status: string; name: string; email: string}[] | undefined, Error>(
    ['eventPending', event_id],
    async () => {
      if (!event_id) {
        return undefined;
      }
      const token = await FirebaseAuth.getAuthToken();

      const config = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      };

      const params = {
        subscription_status: SUBSCRIPTION_STATUS.PENDING,
      };

      const response = await axios.get(`${Config.BASE_API_URL}events/${event_id}/subscriptions`, {params, ...config});
      const response1 = await axios.get(`${Config.BASE_API_URL}events/${event_id}/external/subscriptions`, {
        params,
        ...config,
      });

      return [...response.data, ...response1.data];
    },
    {enabled: true, cacheTime: 0},
  );
}
