import React, {FC, useCallback, useEffect, useMemo} from 'react';
import {View, Animated, Easing} from 'react-native';
import Svg, {Path} from 'react-native-svg';
import {useTheme} from '~contexts/ThemeContext';
import {lightTheme} from '~constants/colors';

interface IProps {
  size?: number;
  spinnerColor?: string;
  spinnerBackgroundColor?: string;
}

const Spinner = ({
  size = 60,
  spinnerColor = lightTheme.gray100,
  spinnerBackgroundColor = lightTheme.primary,
}: IProps) => {
  const {colors} = useTheme();
  const spinValue = useMemo(() => new Animated.Value(0), []);

  const spin = useCallback(() => {
    spinValue.setValue(0);
    Animated.timing(spinValue, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: true,
    }).start(() => spin());
  }, [spinValue]);

  useEffect(() => {
    spin();
  }, [spin]);

  const rotate = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  return (
    <View
      style={{
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Animated.View style={{transform: [{rotate}]}}>
        <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
          <Path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" fill={spinnerColor} />
          <Path
            d="M10.72,19.9a8,8,0,0,1-6.5-9.79A7.77,7.77,0,0,1,10.4,4.16a8,8,0,0,1,9.49,6.52A1.54,1.54,0,0,0,21.38,12h.13a1.37,1.37,0,0,0,1.38-1.54,11,11,0,1,0-12.7,12.39A1.54,1.54,0,0,0,12,21.34h0A1.47,1.47,0,0,0,10.72,19.9Z"
            fill={spinnerBackgroundColor}
          />
        </Svg>
      </Animated.View>
    </View>
  );
};

export default Spinner;
