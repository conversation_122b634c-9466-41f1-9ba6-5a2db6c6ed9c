#!/usr/bin/env node

/**
 * <PERSON>ript to fix style files that have incorrect useTheme imports
 * These files export functions that take colors as parameters, so they shouldn't import useTheme
 */

const fs = require('fs');
const path = require('path');

// File extensions to process
const EXTENSIONS = ['.ts'];

// Directories to process
const DIRECTORIES = ['src/components', 'src/containers'];

function fixStyleFile(filePath) {
  if (!filePath.endsWith('styles.ts')) {
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Check if this is a style file that exports a function (getStyles or createStyles)
  const isStyleFunction = content.includes('getStyles') || content.includes('createStyles');
  const hasUseThemeImport =
    content.includes("import {useTheme} from '~contexts/ThemeContext'") ||
    content.includes("import { useTheme } from '~contexts/ThemeContext'");

  if (isStyleFunction && hasUseThemeImport) {
    console.log(`Fixing style file: ${filePath}`);

    // Remove the useTheme import line
    content = content.replace(/import\s*{\s*useTheme\s*}\s*from\s*['"]~contexts\/ThemeContext['"];\s*\n?/g, '');

    // Also remove any standalone useTheme import
    content = content.replace(
      /import\s*{\s*[^}]*useTheme[^}]*\s*}\s*from\s*['"]~contexts\/ThemeContext['"];\s*\n?/g,
      match => {
        // If there are other imports in the same line, keep them
        const imports = match.match(/{\s*([^}]+)\s*}/)[1];
        const importList = imports
          .split(',')
          .map(imp => imp.trim())
          .filter(imp => imp !== 'useTheme');

        if (importList.length > 0) {
          return `import {${importList.join(', ')}} from '~contexts/ThemeContext';\n`;
        } else {
          return '';
        }
      },
    );

    // Clean up any double newlines
    content = content.replace(/\n\n\n+/g, '\n\n');

    hasChanges = true;
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
  }

  return hasChanges;
}

function getAllFiles(dirPath) {
  const files = [];

  function traverse(currentPath) {
    const items = fs.readdirSync(currentPath, {withFileTypes: true});

    for (const item of items) {
      const fullPath = path.join(currentPath, item.name);

      if (item.isDirectory()) {
        traverse(fullPath);
      } else if (EXTENSIONS.some(ext => item.name.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }

  traverse(dirPath);
  return files;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }

  const files = getAllFiles(dirPath);
  let fixedCount = 0;

  for (const file of files) {
    try {
      if (fixStyleFile(file)) {
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }

  console.log(`Fixed ${fixedCount} files in ${dirPath}`);
}

// Main execution
console.log('🔧 Fixing style file imports...\n');

for (const dir of DIRECTORIES) {
  console.log(`Processing directory: ${dir}`);
  processDirectory(dir);
}

console.log('\n✨ Style file import fixing completed!');
