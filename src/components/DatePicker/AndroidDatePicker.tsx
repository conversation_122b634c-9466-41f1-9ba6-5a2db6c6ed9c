import moment from 'moment-timezone';
import {FC, useEffect, useState} from 'react';
import {DATE_FORMAT} from '~components/DateTimeToggle/DateTimeToggle';
import {CustomTextInput} from '~components/CustomTextInput';
import {Text} from 'react-native';
import {useTranslation} from 'react-i18next';

interface IProps {
  date: Date;
  onDateChange: (value: Date) => void;
  maximumDate?: Date;
  minimumDate?: Date;
}

const DATE_FORMAT_REGEX = /^\d{2}\/\d{2}\/\d{4}$/;

const AndroidDatePicker: FC<IProps> = ({date, minimumDate, maximumDate, onDateChange}) => {
  const [changeableDate, setChangeableDate] = useState(moment(date).format(DATE_FORMAT));
  const [changeableDateError, setChangeableDateError] = useState('');

  const {t} = useTranslation();

  useEffect(() => {
    setChangeableDate(moment(date).format(DATE_FORMAT));
    setChangeableDateError('');
  }, [date]);

  const handleChange = (value: string) => {
    setChangeableDate(value);
    const valueMoment = moment(value, DATE_FORMAT);
    const isDateFormat = DATE_FORMAT_REGEX.test(value);
    const isMaxDate = !maximumDate || valueMoment.isBefore(maximumDate);
    const isMinDate = !minimumDate || valueMoment.isAfter(minimumDate);

    // we check isDateFormat if the string has format as we expect
    // we check valueMoment.isValid() to be sure that date is correct
    // we check isMaxDate to be sure that the date is before the max available date
    // we check isMinDate to be sure that the date is before the min available date
    if (isDateFormat && valueMoment.isValid() && isMaxDate && isMinDate) {
      onDateChange(moment(value, DATE_FORMAT).toDate());
      setChangeableDateError('');
    } else {
      setChangeableDateError(t('generic.invalid_date_format'));
    }
  };

  return (
    <>
      <CustomTextInput value={changeableDate} errorText={changeableDateError} onChangeValue={handleChange} />
      <Text style={{fontSize: 14}}>{t('generic.enter_date_format')}</Text>
    </>
  );
};

export default AndroidDatePicker;
