import React, {useState} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {authScreens} from '../screens';
import {Platform, StatusBar, Text, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';

const Stack = createNativeStackNavigator();

const AuthScreens = () => {
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);

  const {top} = useSafeAreaInsets();
  const {
    i18n: {language},
  } = useTranslation();
  return (
    <Stack.Navigator initialRouteName="SCREEN:MAGICAL_SPLASH">
      {authScreens.map(screen => {
        return (
          <Stack.Screen
            key={screen.name}
            name={screen.name}
            component={screen.component}
            options={{
              headerShown: false,
            }}
          />
        );
      })}
    </Stack.Navigator>
  );
};

export default AuthScreens;
