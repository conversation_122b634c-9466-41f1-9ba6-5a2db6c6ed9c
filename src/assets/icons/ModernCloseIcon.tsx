import React from 'react';
import Svg, {Path, Circle, Defs, LinearGradient, Stop} from 'react-native-svg';
import {lightTheme} from '~constants/colors';
import {useTheme} from '~contexts/ThemeContext';

interface ModernCloseIconProps {
  size?: number;
  color?: string;
  backgroundColor?: string;
  variant?: 'filled' | 'outlined' | 'minimal';
}

const ModernCloseIcon: React.FC<ModernCloseIconProps> = ({
  size = 24,
  color = lightTheme.gray600,
  backgroundColor = lightTheme.gray100,
  variant = 'filled',
}) => {
  const {colors} = useTheme();
  const renderMinimal = () => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M18 6L6 18M6 6L18 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );

  const renderOutlined = () => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="1.5" fill="none" />
      <Path d="M15 9L9 15M9 9L15 15" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );

  const renderFilled = () => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Defs>
        <LinearGradient id="closeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={backgroundColor} stopOpacity="1" />
          <Stop offset="100%" stopColor={backgroundColor} stopOpacity="0.8" />
        </LinearGradient>
      </Defs>

      <Circle cx="12" cy="12" r="10" fill="url(#closeGradient)" stroke={colors.border} strokeWidth="0.5" />

      <Path d="M15 9L9 15M9 9L15 15" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );

  switch (variant) {
    case 'minimal':
      return renderMinimal();
    case 'outlined':
      return renderOutlined();
    case 'filled':
    default:
      return renderFilled();
  }
};

export default ModernCloseIcon;
