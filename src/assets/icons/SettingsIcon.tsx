import {ColorValue} from 'react-native';
import Svg, {Path} from 'react-native-svg';

const SettingsIcon = ({color}: {color?: ColorValue}) => (
  <Svg width="25" height="25" viewBox="0 0 25 25" fill="none">
    <Path
      d="M11.3768 24.8951H13.5225C14.2162 24.8951 14.7354 24.4769 14.8833 23.8012L15.4737 21.3096C15.878 21.1741 16.2705 21.0173 16.613 20.853L18.7966 22.1894C19.3701 22.548 20.0393 22.4885 20.5202 22.0118L22.0256 20.516C22.5065 20.0351 22.5715 19.3287 22.1894 18.7573L20.8572 16.597C21.0312 16.2375 21.1858 15.8662 21.3 15.4929L23.8151 14.8929C24.4908 14.7471 24.8993 14.2258 24.8993 13.5321V11.4258C24.8993 10.7437 24.4908 10.2246 23.8151 10.0746L21.3255 9.47672C21.1901 9.05531 21.0258 8.67234 20.8786 8.35124L22.215 6.15163C22.5874 5.5739 22.5396 4.90265 22.0448 4.42594L20.5139 2.91633C20.0255 2.47148 19.4234 2.39062 18.8402 2.71523L16.613 4.09102C16.2801 3.915 15.8972 3.76031 15.4758 3.62273L14.8833 1.09593C14.7354 0.420232 14.2162 0 13.5225 0H11.3768C10.683 0 10.1639 0.420232 10.0085 1.09804L9.41601 3.59929C9.01171 3.73476 8.61702 3.89156 8.26499 4.07719L6.05905 2.71523C5.47382 2.39062 4.86632 2.45977 4.37367 2.92054L2.85445 4.42594C2.35969 4.90265 2.30438 5.5739 2.6864 6.15163L4.01109 8.35124C3.87352 8.67234 3.70922 9.05531 3.57586 9.47672L1.08632 10.0746C0.408513 10.2246 0 10.7437 0 11.4258V13.5321C0 14.2258 0.408513 14.7471 1.08632 14.8929L3.59929 15.4929C3.71555 15.8662 3.86812 16.2375 4.03242 16.597L2.70984 18.7573C2.3182 19.3287 2.39484 20.0351 2.87578 20.516L4.37156 22.0118C4.85039 22.4885 5.53124 22.548 6.10475 22.1894L8.27671 20.853C8.63085 21.0173 9.01171 21.1741 9.41601 21.3096L10.0085 23.8012C10.1639 24.4769 10.683 24.8951 11.3768 24.8951ZM12.4507 16.5516C10.1962 16.5516 8.34983 14.6934 8.34983 12.439C8.34983 10.1962 10.1962 8.34983 12.4507 8.34983C14.703 8.34983 16.5494 10.1962 16.5494 12.439C16.5494 14.6934 14.703 16.5516 12.4507 16.5516Z"
      fill={color}
    />
  </Svg>
);
export default SettingsIcon;
