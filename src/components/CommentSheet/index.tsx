import {
  Dimensions,
  FlatList,
  Image,
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {BottomSheetFlatList, BottomSheetModal} from '@gorhom/bottom-sheet';
import {RefObject, useCallback, useMemo, useState} from 'react';
import CloseIcon from '../../assets/images/closeicon.png';
import BackIcon from '../../assets/images/back.png';
import MessageIcon from '../../assets/images/message.png';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Comment} from '~types/api/event';
import moment from 'moment';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  commentSheetRef: RefObject<BottomSheetModal>;
  commentList: Comment[];
  onAddComment: (comment: string) => void;
  photo?: string;
  isUserEvent: boolean;
}

const CommentSheet = ({commentSheetRef, commentList, onAddComment, photo, isUserEvent}: IProps) => {
  const {colors} = useTheme();
  const snapPoints = useMemo(() => ['75%'], []);
  const insects = useSafeAreaInsets();
  const [isReply, setIsReply] = useState(false);
  const [commentText, setCommentText] = useState('');

  const styles = StyleSheet.create({
    sendTextStyle: {
      fontWeight: 'bold',
      color: colors.eventInfluencer,
      fontSize: 16,
      marginEnd: 20,
    },
    replyList: {
      marginStart: 50,
    },
    headerRowView: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    replytextview: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.eventInfluencer,
      marginTop: 10,
    },
    commentInfoView: {
      flex: 1,
    },
    commentText: {
      fontSize: 12,
      flex: 1,
      marginTop: 4,
    },
    threedotview: {
      height: 20,
      width: 20,
      marginEnd: 10,
      tintColor: colors.gray400,
    },
    nameview: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    imgandtextcontainer: {
      flexDirection: 'row',
    },
    commentmaincontainer: {
      width: '100%',
      alignSelf: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: 20,
    },
    addtextstyle: {
      fontSize: 12,
      fontWeight: '600',
      flex: 1,
      marginEnd: 15,
      color: colors.textSecondary,
      backgroundColor: colors.gray100,
      padding: 10,
      borderRadius: 10,
    },
    imgview: {
      height: 25,
      width: 25,
      backgroundColor: colors.gray400,
      borderRadius: 12.5,
      marginHorizontal: 15,
    },
    commentcontainer: {
      alignItems: 'center',
      flexDirection: 'row',
      marginBottom: 5,
      marginTop: 10,
      width: '100%',
    },
    lineview: {
      width: '100%',
      height: 1,
      backgroundColor: colors.border,
      marginTop: 10,
    },
    closeicon: {
      height: 15,
      width: 15,
    },
    commenttext: {
      fontSize: 15,
      fontWeight: 'bold',
      color: colors.black,
    },
    commenttextcontainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '90%',
      paddingVertical: 7,
      alignSelf: 'center',
    },
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: colors.gray400,
    },
    contentContainer: {
      flex: 1,
    },
  });

  const onReplyClick = () => {
    setIsReply(true);
  };

  const onBackPress = () => {
    setIsReply(false);
  };

  const onClose = () => {
    commentSheetRef.current?.close();
  };

  const handleSheetChanges = useCallback((index: number) => {
    console.log('handleSheetChanges', index);
  }, []);

  const renderCommentItem = ({item}: {item: Comment}) => (
    <View style={styles.commentmaincontainer}>
      <Image style={styles.imgview} source={{uri: item.user.photo}} />
      <View style={styles.commentInfoView}>
        <View style={styles.imgandtextcontainer}>
          <Text style={styles.nameview}>{item.user.name}</Text>
          <Text style={[styles.nameview, {marginLeft: 10}]}>{moment(item.created_at).fromNow()}</Text>
        </View>
        <Text style={styles.commentText}>{item.comment}</Text>
        {false && (
          <TouchableOpacity onPress={onReplyClick}>
            <Text style={styles.replytextview}>{item.created_at}</Text>
          </TouchableOpacity>
        )}
      </View>
      {false && (
        <TouchableOpacity onPress={onReplyClick}>
          <Image style={styles.threedotview} source={MessageIcon} />
        </TouchableOpacity>
      )}
    </View>
  );
  const renderReplyCommentItem = ({item}: {item: Comment}) => (
    <View style={styles.commentmaincontainer}>
      <Image style={[styles.imgview, {marginHorizontal: 0, marginEnd: 15}]} source={{uri: ''}} />
      <View style={styles.commentInfoView}>
        <View style={styles.imgandtextcontainer}>
          <Text style={styles.nameview}>{item.user.name}</Text>
          <Text style={styles.nameview}>{item.created_at}</Text>
        </View>
        <Text style={styles.replytextview}>{item.created_at}</Text>
      </View>
    </View>
  );

  const getKeyboardOffset = () => {
    const deviceHeight = Dimensions.get('window').height;
    return deviceHeight * 0.65 * 0.45;
  };

  return (
    <BottomSheetModal ref={commentSheetRef} snapPoints={snapPoints} onChange={handleSheetChanges}>
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={getKeyboardOffset()}
        style={[styles.contentContainer, {marginBottom: insects.bottom}]}>
        <View style={styles.commenttextcontainer}>
          <View style={styles.headerRowView}>
            {isReply && (
              <TouchableOpacity onPress={onBackPress}>
                <Image source={BackIcon} style={[styles.closeicon, {marginEnd: 10}]} />
              </TouchableOpacity>
            )}
            <Text style={styles.commenttext}>{isReply ? 'Replies' : 'Updates'}</Text>
            <Text style={{fontSize: 11, color: colors.gray400, flex: 1, marginHorizontal: 8}}>
              {'only the host can post updates. Please contact the host directly if you have a question'}
            </Text>
          </View>
          <TouchableOpacity onPress={onClose}>
            <Image style={styles.closeicon} source={CloseIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.lineview} />
        <View style={{flex: 1}}>
          {isReply ? (
            <>
              <View style={[styles.commentmaincontainer, {backgroundColor: colors.gray100, paddingBottom: 10}]}>
                <Image style={styles.imgview} source={{uri: ''}} />
                <View style={styles.commentInfoView}>
                  <View style={styles.imgandtextcontainer}>
                    <Text style={styles.nameview}>{commentList[0].user.name}</Text>
                    <Text style={styles.nameview}>{commentList[0].created_at}</Text>
                  </View>
                  <Text style={[styles.commentText, {flex: undefined}]}>{commentList[0].comment}</Text>
                </View>
              </View>
              <BottomSheetFlatList style={styles.replyList} data={commentList} renderItem={renderReplyCommentItem} />
            </>
          ) : (
            <BottomSheetFlatList data={commentList} renderItem={renderCommentItem} />
          )}
        </View>
        {isUserEvent && <View style={styles.lineview} />}
        {isUserEvent && (
          <View style={styles.commentcontainer}>
            <Image style={styles.imgview} source={{uri: photo}} />
            <TextInput
              style={styles.addtextstyle}
              placeholder="Add an update..."
              value={commentText}
              onChangeText={setCommentText}
            />
            {commentText.length > 0 && (
              <TouchableOpacity
                onPress={() => {
                  setCommentText('');
                  onAddComment(commentText);
                }}>
                <Text style={styles.sendTextStyle}>Send</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </KeyboardAvoidingView>
    </BottomSheetModal>
  );
};

export default CommentSheet;
