diff --git a/node_modules/react-native-animated-nav-tab-bar/dist/lib/index.js b/node_modules/react-native-animated-nav-tab-bar/dist/lib/index.js
index 719c891..9349bb1 100644
--- a/node_modules/react-native-animated-nav-tab-bar/dist/lib/index.js
+++ b/node_modules/react-native-animated-nav-tab-bar/dist/lib/index.js
@@ -226,6 +226,7 @@ var templateObject_1, templateObject_2, templateObject_3, templateObject_4, temp
 var TabBarElement = (function (_a) {
     var state = _a.state, navigation = _a.navigation, descriptors = _a.descriptors, appearance = _a.appearance, tabBarOptions = _a.tabBarOptions, lazy = _a.lazy;
     // Appearance options destruction
+    var isDisabled = appearance?.isDisabled
     var topPadding = appearance.topPadding, bottomPadding = appearance.bottomPadding, horizontalPadding = appearance.horizontalPadding, tabBarBackground = appearance.tabBarBackground, activeTabBackgrounds = appearance.activeTabBackgrounds, activeColors = appearance.activeColors, floating = appearance.floating, dotCornerRadius = appearance.dotCornerRadius, whenActiveShow = appearance.whenActiveShow, whenInactiveShow = appearance.whenInactiveShow, dotSize = appearance.dotSize, shadow = appearance.shadow, tabButtonLayout = appearance.tabButtonLayout;
     var activeTintColor = tabBarOptions.activeTintColor, inactiveTintColor = tabBarOptions.inactiveTintColor, activeBackgroundColor = tabBarOptions.activeBackgroundColor, tabStyle = tabBarOptions.tabStyle, labelStyle = tabBarOptions.labelStyle;
     // State
@@ -477,7 +478,7 @@ var TabBarElement = (function (_a) {
                 return (React__default["default"].createElement(ResourceSavingScene, { key: route.key, isVisible: isFocused, style: reactNative.StyleSheet.absoluteFill },
                     React__default["default"].createElement(reactNative.View, { accessibilityElementsHidden: !isFocused, importantForAccessibility: isFocused ? "auto" : "no-hide-descendants", style: { flex: 1 } }, descriptor.render())));
             }))),
-        tabBarVisible && (React__default["default"].createElement(reactNative.View, { pointerEvents: "box-none", style: floating && overlayStyle },
+        !isDisabled && (React__default["default"].createElement(reactNative.View, { pointerEvents: "box-none", style: floating && overlayStyle },
             React__default["default"].createElement(BottomTabBarWrapper, { style: tabStyle, floating: floating, topPadding: topPadding, bottomPadding: bottomPadding, horizontalPadding: horizontalPadding, tabBarBackground: tabBarBackground, shadow: shadow },
                 state.routes.map(createTab),
                 React__default["default"].createElement(Dot, { dotCornerRadius: dotCornerRadius, topPadding: topPadding, activeTabBackground: activeTabBackground, style: reactNative.I18nManager.isRTL
diff --git a/node_modules/react-native-animated-nav-tab-bar/dist/types/src/types.d.ts b/node_modules/react-native-animated-nav-tab-bar/dist/types/src/types.d.ts
index bebc6bb..e2d3a54 100644
--- a/node_modules/react-native-animated-nav-tab-bar/dist/types/src/types.d.ts
+++ b/node_modules/react-native-animated-nav-tab-bar/dist/types/src/types.d.ts
@@ -14,6 +14,7 @@ export declare enum TabButtonLayout {
     HORIZONTAL = "horizontal"
 }
 export interface IAppearanceOptions {
+    isDisabled: boolean;
     topPadding: number;
     bottomPadding: number;
     horizontalPadding: number;
