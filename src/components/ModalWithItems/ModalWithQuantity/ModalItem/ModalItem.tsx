import {Text, TouchableOpacity} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  item: {
    quantity: number;
    isActive: boolean;
  };
  onPress: () => void;
}

const ModalItem = ({item, onPress}: IProps) => {
  const {colors} = useTheme();
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        borderRadius: 5,
        height: '25%',
        marginVertical: 6,
        width: '30%',
        borderWidth: 1,
        borderColor: item.isActive ? colors.primary : colors.border,
        flexDirection: 'row',
        paddingHorizontal: 16,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <Text style={{color: colors.textPrimary, fontWeight: '500', fontSize: 15}}>{item.quantity}</Text>
    </TouchableOpacity>
  );
};

export default ModalItem;
