import {useInfiniteQuery, useQueryClient} from 'react-query';
import {useCallback, useEffect, useMemo} from 'react';
import axios from 'axios';
import Config from 'react-native-config';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetEventsParams} from '~types/events';
import {queryKeys, prefetchHelpers} from '~utils/performance/queryConfig';
import {requestDeduplicator} from '~utils/performance/searchOptimization';
const NUMBER_OF_EVENTS_PER_PAGE = 10000;

interface UseOptimizedEventsParams extends Omit<GetEventsParams, 'offset' | 'limit'> {
  enabled?: boolean;
  prefetchNext?: boolean; // Automatically prefetch next page
  backgroundRefetch?: boolean; // Enable background refetching
}

export const useOptimizedEvents = (params: UseOptimizedEventsParams) => {
  const {
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    enabled = true,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood = false,
    prefetchNext = true,
    backgroundRefetch = true,
  } = params;

  const queryClient = useQueryClient();

  // Create stable query key
  const queryKey = useMemo(() => {
    const filters = {
      tab: isNeighbourhood ? 'neighbourhood_event' : tab,
      order_by,
      order_dir,
      distance_km: isNeighbourhood ? Math.max(distance_km, 500) : distance_km, // Cap neighbourhood events to max 500km instead of unlimited
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe,
    };
    return queryKeys.events.list(filters);
  }, [
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood,
  ]);

  // Optimized fetch function with deduplication
  const fetchEvents = useCallback(
    async ({pageParam = 0}) => {
      const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
      const limit = NUMBER_OF_EVENTS_PER_PAGE;

      const requestParams: GetEventsParams = {
        limit,
        offset,
        tab: isNeighbourhood ? 'neighbourhood_event' : tab,
        order_by,
        order_dir,
        distance_km: isNeighbourhood ? Math.max(distance_km, 500) : distance_km, // Cap neighbourhood events to max 500km instead of unlimited
        event_age_group,
        q,
        filter_my_event_type,
        filter_type,
        user_id,
        timeframe,
      };

      // Create deduplication key
      const dedupeKey = `events-${JSON.stringify(requestParams)}`;

      return requestDeduplicator.deduplicate(dedupeKey, async () => {
        const token = await FirebaseAuth.getAuthToken();
        const config = {
          headers: {Authorization: token, Accept: 'application/json'},
        };

        // Debug logging for event fetching
        console.log('🔍 Fetching events with params:', {
          tab: requestParams.tab,
          distance_km: requestParams.distance_km,
          isNeighbourhood,
          offset: requestParams.offset,
          limit: requestParams.limit,
        });

        const response = await axios.get(Config.BASE_API_URL + 'events/', {
          params: requestParams,
          ...config,
        });

        // Debug logging for response
        console.log(
          `📍 Fetched ${response.data.items?.length || 0} events for ${requestParams.tab} tab with ${requestParams.distance_km}km radius`,
        );

        return response.data;
      });
    },
    [
      tab,
      order_by,
      order_dir,
      distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe,
      isNeighbourhood,
    ],
  );

  // Enhanced infinite query with performance optimizations
  const query = useInfiniteQuery(queryKey, fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const nextPage = lastPage.items.length === NUMBER_OF_EVENTS_PER_PAGE ? pages.length : undefined;
      return nextPage;
    },
    enabled,

    // Performance optimizations
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    staleTime: 1000 * 60 * 5, // 5 minutes stale time
    keepPreviousData: true,
    refetchOnWindowFocus: backgroundRefetch,
    refetchOnReconnect: true,

    // Optimize re-renders
    notifyOnChangeProps: 'tracked',

    // Background refetching for fresh data
    refetchInterval: backgroundRefetch ? 1000 * 60 * 5 : false, // 5 minutes
    refetchIntervalInBackground: false,
  });

  // Automatic next page prefetching
  useEffect(() => {
    if (prefetchNext && query.data && query.hasNextPage && !query.isFetchingNextPage) {
      const lastPage = query.data.pages[query.data.pages.length - 1];
      if (lastPage?.items?.length === NUMBER_OF_EVENTS_PER_PAGE) {
        // Prefetch next page after a short delay
        const timer = setTimeout(() => {
          query.fetchNextPage();
        }, 1000);
        return () => clearTimeout(timer);
      }
    }
  }, [query.data, query.hasNextPage, query.isFetchingNextPage, prefetchNext, query.fetchNextPage]);

  // Background prefetching of related data
  useEffect(() => {
    if (query.data && query.data.pages.length > 0) {
      const events = query.data.pages.flatMap(page => page.items);

      // Prefetch event details for first few events
      events.slice(0, 3).forEach(event => {
        if (event.event_id) {
          queryClient.prefetchQuery({
            queryKey: queryKeys.events.detail(event.event_id),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${Config.BASE_API_URL}events/${event.event_id}`, {
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 10, // 10 minutes for event details
          });
        }
      });
    }
  }, [query.data, queryClient]);

  // Optimized refetch function
  const optimizedRefetch = useCallback(async () => {
    // Clear cache for this specific query
    await queryClient.invalidateQueries(queryKey);
    return query.refetch();
  }, [queryClient, queryKey, query.refetch]);

  // Optimized remove function
  const optimizedRemove = useCallback(() => {
    queryClient.removeQueries(queryKey);
  }, [queryClient, queryKey]);

  return {
    ...query,
    refetch: optimizedRefetch,
    remove: optimizedRemove,

    // Additional performance metrics
    cacheHit: query.isFetched && !query.isFetching,
    isStale: query.isStale,
  };
};
