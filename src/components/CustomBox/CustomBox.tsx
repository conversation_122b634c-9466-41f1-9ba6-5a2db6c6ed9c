import {DimensionValue, Text, TouchableOpacity, View} from 'react-native';
import createStyles from './styles';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  defaultValue?: string;
  handlePress: () => void;
  description?: string;
  errorText?: any;
  value: string | number | null;
  buttonWidth?: DimensionValue;
  icon?: JSX.Element;
  withoutError?: boolean;
}

const CustomBox = ({
  defaultValue,
  handlePress,
  description,
  errorText,
  buttonWidth = '100%',
  value,
  icon,
  withoutError = false,
}: IProps) => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  return (
    <View style={{width: buttonWidth}}>
      {description && (
        <Text style={{marginTop: 8, fontSize: 12, color: colors.statusGray, fontWeight: '500'}}>{description}</Text>
      )}
      <TouchableOpacity onPress={handlePress} style={{width: '100%'}}>
        <View
          style={[
            styles.container,
            {
              borderColor: errorText ? colors.error : colors.border,
            },
          ]}>
          <View style={styles.wrapper}>
            <Text style={{color: !value ? colors.gray400 : colors.black}} numberOfLines={1}>
              {value || defaultValue}
            </Text>
          </View>
          {icon && <View style={{justifyContent: 'center'}}>{icon}</View>}
        </View>
      </TouchableOpacity>
      {!withoutError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorText}</Text>
        </View>
      )}
    </View>
  );
};

export default CustomBox;
