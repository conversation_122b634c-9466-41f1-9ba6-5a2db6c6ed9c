import {StyleSheet} from 'react-native';
const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingRight: 16,
      marginTop: 10,
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderWidth: 1,
      borderRadius: 5,
    },
    hiddenLabel: {
      opacity: 0,
    },
    wrapper: {
      paddingLeft: 15,
      width: '90%',
      alignItems: 'flex-start',
      justifyContent: 'center',
      height: 40,
    },
    errorContainer: {paddingTop: 4},
    errorText: {fontSize: 12, fontWeight: '400', color: colors.error, width: '100%'},
  });

export default createStyles;
