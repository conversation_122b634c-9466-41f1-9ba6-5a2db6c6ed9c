import {View, Platform} from 'react-native';
import ModalWithButtons from '../../ModalWithButtons';
import {useEffect, useState} from 'react';
import ModalItem from '../ModalItem';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {Gender} from '~types/api/user';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  close: () => void;
  onPress: (value: string) => () => void;
  chosenGender: string;
}

const ModalWithGenders = ({isVisible, close, onPress, chosenGender}: IProps) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const isAndroid = Platform.OS === 'android';
  const {bottom} = useSafeAreaInsets();
  const [items, setItems] = useState([
    {title: t('onboarding.man'), isActive: false, type: Gender.MAN},
    {title: t('onboarding.woman'), isActive: false, type: Gender.WOMAN},
    {title: t('onboarding.other'), isActive: false, type: Gender.OTHER},
    {title: t('onboarding.prefer'), isActive: false, type: Gender.PREFER_NOT_TO_SAY},
  ]);

  const handleItemClick = (index: number) => {
    const updatedItems = items.map((item, i) => ({
      ...item,
      isActive: i === index,
    }));
    setItems(updatedItems);
  };

  useEffect(() => {
    if (chosenGender) {
      setItems(prevItems => [
        ...prevItems.map(prevItem => (prevItem.type === chosenGender ? {...prevItem, isActive: true} : prevItem)),
      ]);
    }
  }, [chosenGender]);
  return (
    <ModalWithButtons
      onCloseModal={close}
      modalIsVisible={isVisible}
      onPress={items.find(item => item.isActive)?.title ? onPress(items.find(item => item.isActive)!.type) : () => {}}
      backgroundColor={colors.white}>
      <View
        style={{
          paddingHorizontal: 46,
          paddingTop: 10,
          overflow: 'hidden',
          height: '100%',
          paddingBottom: isAndroid ? 30 : bottom + 10,
          justifyContent: 'space-around',
          // backgroundColor: colors.black,
        }}>
        {items.map((item, index) => (
          <ModalItem key={index} item={item} onPress={() => handleItemClick(index)} />
        ))}
      </View>
    </ModalWithButtons>
  );
};

export default ModalWithGenders;
