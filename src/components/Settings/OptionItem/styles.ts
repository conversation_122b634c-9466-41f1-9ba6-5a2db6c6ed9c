import {StyleSheet} from 'react-native';
const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.surface,
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginVertical: 6,
      borderWidth: 1,
      borderColor: colors.border,
    },
    wrapper: {flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start'},
    nameText: {fontSize: 16, fontWeight: 'normal', color: colors.textPrimary, marginLeft: 10, textAlign: 'center'},
    subTitleWrapper: {flex: 1, alignItems: 'flex-end', justifyContent: 'center'},
    subTitleText: {textTransform: 'uppercase', color: colors.textSecondary},
  });
export default createStyles;
