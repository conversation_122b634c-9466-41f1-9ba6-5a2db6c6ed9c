import React from 'react';
import {View, Text, StyleSheet, RefreshControl, ActivityIndicator, TouchableOpacity} from 'react-native';
import {Event} from '~types/api/event';
import ModernEventCard from '../ModernEventCard';
import ModernEventCardSkeleton from '../ModernEventCardSkeleton';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, responsive} from '~constants';
import {useTranslation} from 'react-i18next';
import OptimizedList from '~components/performance/OptimizedList';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import {haptics} from '~utils/haptics';

interface ModernEventsListProps {
  data: Event[];
  isLoading?: boolean;
  isFetching?: boolean;
  isFetchingNextPage?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  isMyEvents?: boolean;
  emptyStateTitle?: string;
  emptyStateSubtitle?: string;
  contentContainerStyle?: any;
  estimatedItemSize?: number;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
}

// Using OptimizedList instead of AnimatedFlashList for better performance

const ModernEventsList: React.FC<ModernEventsListProps> = ({
  data,
  isLoading = false,
  isFetching = false,
  isFetchingNextPage = false,
  refreshing = false,
  onRefresh,
  onEndReached,
  onEndReachedThreshold = 0.5,
  isMyEvents = false,
  emptyStateTitle,
  emptyStateSubtitle,
  contentContainerStyle,
  estimatedItemSize = 280,
  ListHeaderComponent,
  ListFooterComponent,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();

  const styles = createStyles(colors);

  const renderItem = ({item, index}: {item: Event; index: number}) => (
    <ModernEventCard item={item} index={index} isMyEvents={isMyEvents} />
  );

  const renderSkeletonList = () => {
    return (
      <View style={styles.container}>
        {Array.from({length: 3}).map((_, index) => (
          <ModernEventCardSkeleton key={index} index={index} />
        ))}
      </View>
    );
  };

  const renderEmptyState = () => {
    if (isLoading) {
      return renderSkeletonList();
    }

    return (
      <View style={styles.emptyStateContainer}>
        <View style={styles.emptyStateContent}>
          {/* Empty State Icon */}
          <View style={styles.emptyStateIcon}>
            <Text style={styles.emptyStateEmoji}>📅</Text>
          </View>

          {/* Empty State Text */}
          <Text style={styles.emptyStateTitle}>{emptyStateTitle || t('events.no_events_found')}</Text>

          {emptyStateSubtitle && <Text style={styles.emptyStateSubtitle}>{emptyStateSubtitle}</Text>}

          {/* Create Event Button */}
          <TouchableOpacity
            style={styles.createEventButton}
            onPress={() => {
              haptics.buttonPress();
              navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE);
            }}
            activeOpacity={0.8}>
            <Text style={styles.createEventButtonText}>{t('home.create_event')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!isFetchingNextPage) {
      return null;
    }

    return (
      <View style={styles.footerContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.footerText}>{t('common.loading_more')}</Text>
      </View>
    );
  };

  const renderRefreshControl = () => (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={onRefresh}
      tintColor={colors.primary}
      colors={[colors.primary]}
      progressBackgroundColor={colors.surface}
      style={styles.refreshControl}
    />
  );

  return (
    <View style={styles.container}>
      <OptimizedList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.event_id}-${index}`}
        estimatedItemSize={estimatedItemSize}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        refreshControl={onRefresh ? renderRefreshControl() : undefined}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={ListFooterComponent || renderFooter}
        ListHeaderComponent={ListHeaderComponent}
        // contentContainerStyle handled by OptimizedList internally
        showsVerticalScrollIndicator={false}
        // Performance optimizations
        enableDynamicSizing={true}
        enableViewabilityTracking={true}
        enablePrefetching={true}
        prefetchDistance={5}
        chunkSize={20}
        maxCachedItems={50}
        recycleItems={true}
        // Advanced features
        onItemVisible={(item, index) => {
          // Track visible events for analytics
          console.log(`Event ${item.event_id} visible at index ${index}`);
        }}
        onPrefetch={(items, startIndex) => {
          // Prefetch event details or related data
          console.log(`Prefetching ${items.length} items starting at ${startIndex}`);
        }}
      />
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    contentContainer: {
      paddingTop: spacing.xs,
      paddingBottom: responsive.getValue({xs: 80, sm: 100, md: 120}, 100),
    },
    refreshControl: {
      backgroundColor: colors.background,
    },
    emptyStateContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
      paddingVertical: spacing.xxxxl,
      minHeight: 400,
    },
    emptyStateContent: {
      alignItems: 'center',
      maxWidth: 300,
    },
    emptyStateIcon: {
      width: 80,
      height: 80,
      borderRadius: borderRadius.full,
      backgroundColor: colors.gray100,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.xl,
    },
    emptyStateEmoji: {
      fontSize: 32,
    },
    emptyStateTitle: {
      color: colors.textPrimary,
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      textAlign: 'center',
      marginBottom: spacing.md,
      lineHeight: typography.fontSize.xl * typography.lineHeight.tight,
    },
    emptyStateSubtitle: {
      color: colors.textSecondary,
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      textAlign: 'center',
      lineHeight: typography.fontSize.base * typography.lineHeight.normal,
      marginBottom: spacing.xl,
    },
    createEventButton: {
      backgroundColor: colors.secondary,
      paddingHorizontal: spacing.xl,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.xl,
      marginTop: spacing.lg,
      minWidth: 160,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    createEventButtonText: {
      color: colors.white,
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      textAlign: 'center',
    },
    loadingContainer: {
      alignItems: 'center',
      gap: spacing.md,
    },
    loadingText: {
      color: colors.textSecondary,
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
    },
    footerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.xl,
      gap: spacing.sm,
    },
    footerText: {
      color: colors.textSecondary,
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
    },
  });

export default ModernEventsList;
