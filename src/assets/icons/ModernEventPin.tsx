import React, {useEffect} from 'react';
import Svg, {Path, Circle, Defs, LinearGradient, Stop} from 'react-native-svg';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

interface ModernEventPinProps {
  eventType: 'business' | 'influencer' | 'community' | 'pyxi_select';
  size?: number;
  isSelected?: boolean;
  disableInternalScaling?: boolean;
  onPress?: () => void;
  animateOnMount?: boolean;
}

const ModernEventPin: React.FC<ModernEventPinProps> = ({
  eventType,
  size = 40,
  isSelected = false,
  disableInternalScaling = false,
  onPress,
  animateOnMount = true,
}) => {
  const {colors} = useTheme();

  // Animation values
  const scale = useSharedValue(0);
  const bounce = useSharedValue(0);
  const pulse = useSharedValue(1);

  // Initialize animations
  useEffect(() => {
    if (animateOnMount) {
      // Entry animation with bounce
      scale.value = withSequence(withTiming(1.2, {duration: 200}), withSpring(1, {damping: 8, stiffness: 100}));
    } else {
      scale.value = 1;
    }
  }, [animateOnMount]);

  // Selection animation - only if internal scaling is not disabled
  useEffect(() => {
    if (!disableInternalScaling && isSelected) {
      // Bounce animation when selected
      bounce.value = withSequence(withTiming(1.3, {duration: 150}), withSpring(1, {damping: 6, stiffness: 120}));

      // Pulse animation for selected state
      pulse.value = withSequence(withTiming(1.1, {duration: 300}), withTiming(1, {duration: 300}));
    } else {
      bounce.value = withSpring(1, {damping: 8, stiffness: 100});
      pulse.value = 1;
    }
  }, [isSelected, disableInternalScaling]);

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    // Only apply internal animations if not disabled
    const combinedScale = disableInternalScaling ? 1 : scale.value * bounce.value * pulse.value;
    return {
      transform: [{scale: combinedScale}],
    };
  });

  const AnimatedSvg = Animated.createAnimatedComponent(Svg);

  const getEventColor = () => {
    switch (eventType) {
      case 'business':
        return colors.eventBusiness;
      case 'influencer':
        return colors.eventInfluencer;
      case 'community':
        return colors.eventCommunity;
      case 'pyxi_select':
        return colors.eventPyxiSelect;
      default:
        return colors.primary;
    }
  };

  const getEventIcon = () => {
    switch (eventType) {
      case 'business':
        return (
          <Path
            d="M12 7V3H8V7H12ZM14 7H18V11H14V7ZM12 9V13H8V9H12ZM6 7V11H2V7H6Z"
            fill="white"
            transform="translate(6, 8)"
          />
        );
      case 'influencer':
        return (
          <Path
            d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9L21 9ZM3 9L9 8.5V7L3 7V9ZM15 10.5V19L13.5 17.5L12 19L10.5 17.5L9 19V10.5L15 10.5Z"
            fill="white"
            transform="translate(6, 6)"
          />
        );
      case 'community':
        return (
          <Path
            d="M16 4C18.2 4 20 5.8 20 8C20 10.2 18.2 12 16 12C13.8 12 12 10.2 12 8C12 5.8 13.8 4 16 4ZM8 4C10.2 4 12 5.8 12 8C12 10.2 10.2 12 8 12C5.8 12 4 10.2 4 8C4 5.8 5.8 4 8 4ZM8 14C12.42 14 16 15.79 16 18V20H0V18C0 15.79 3.58 14 8 14ZM16 14C20.42 14 24 15.79 24 18V20H18V18C18 16.9 17.16 15.62 15.61 14.66C15.75 14.44 15.88 14.22 16 14Z"
            fill="white"
            transform="translate(4, 6) scale(0.7)"
          />
        );
      case 'pyxi_select':
        return (
          <Path
            d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
            fill="white"
            transform="translate(6, 8) scale(0.8)"
          />
        );
      default:
        return <Circle cx="20" cy="20" r="4" fill="white" />;
    }
  };

  const pinColor = getEventColor();
  // Only apply internal scaling if not disabled (prevents double scaling with AnimatedPin)
  const scaleFactor = disableInternalScaling ? 1 : isSelected ? 1.2 : 1;
  const pinSize = size * scaleFactor;

  return (
    <AnimatedSvg
      width={pinSize + 10}
      height={pinSize + 15}
      viewBox="0 0 50 60"
      style={[
        {
          transform: disableInternalScaling ? [] : [{scale: scaleFactor}],
        },
        animatedStyle,
      ]}>
      <Defs>
        <LinearGradient id={`gradient-${eventType}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={pinColor} stopOpacity="1" />
          <Stop offset="100%" stopColor={pinColor} stopOpacity="0.8" />
        </LinearGradient>
      </Defs>

      {/* Pin shadow */}
      <Path
        d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
        fill="rgba(0,0,0,0.1)"
        transform="translate(1, 2)"
      />

      {/* Main pin body */}
      <Path
        d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
        fill={`url(#gradient-${eventType})`}
      />

      {/* Inner circle for icon */}
      <Circle cx="25" cy="18" r="10" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" strokeWidth="1" />

      {/* Event type icon */}
      {getEventIcon()}

      {/* Selection indicator - only show when internal scaling is enabled */}
      {isSelected && !disableInternalScaling && (
        <Circle cx="25" cy="18" r="12" fill="none" stroke="white" strokeWidth="2" opacity="0.8" />
      )}
    </AnimatedSvg>
  );
};

export default ModernEventPin;
