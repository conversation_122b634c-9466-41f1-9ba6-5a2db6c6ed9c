import {Link} from '@react-navigation/native';

import {Dimensions, StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    attendeesCount: {
      color: colors.secondary,
      fontWeight: '700',
      fontSize: 16,
      lineHeight: 22,
    },
    profileImg: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: colors.background,
    },
    attendeeRowStyle: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    spinnerView: {
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 100,
    },
    errorText: {
      fontSize: 16,
      color: colors.error,
      fontWeight: 'bold',
    },
    buttonView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    commentTextView: {
      fontSize: 12,
      marginLeft: 10,
    },
    userImage: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.gray400,
    },
    commentItemView: {
      flexDirection: 'row',
      marginTop: 10,
    },
    commentTitle: {
      fontWeight: 'bold',
      fontSize: 14,
    },
    commentView: {
      backgroundColor: colors.gray100,
      borderRadius: 15,
      padding: 15,
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 10,
      borderColor: colors.gray400,
      padding: 15,
    },
    flex: {
      flex: 1,
    },
    attendeeLeftContainer: {
      flexDirection: 'row',
      flex: 1,
      paddingRight: 10,
    },
    attendeePhoto: {
      height: 40,
      width: 40,
      borderRadius: 12,
      overflow: 'hidden',
    },
    attendeeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    attendeeSub: {
      flex: 1,
      marginLeft: 15,
      fontSize: 10,
      color: colors.gray400,
    },
    attendeeName: {
      flex: 1,
      marginLeft: 12,
      justifyContent: 'center',
      fontSize: 17,
      lineHeight: 20,
      fontWeight: '500',
      color: colors.black,
      alignSelf: 'center',
    },
    attendeeRole: {
      fontSize: 12,
      lineHeight: 20,
      fontWeight: '400',
      color: colors.textSecondary,
    },
    checkContainer: {
      flexDirection: 'row',
    },
    checkRightContainer: {
      marginLeft: 14,
      flex: 1,
    },
    loaderJoinBtnView: {
      aspectRatio: 1,
      width: 100,
      height: 80,
      alignSelf: 'center',
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkLabel: {
      fontWeight: '600',
      fontSize: 15,
      lineHeight: 20,
      color: colors.black,
    },
    checkDescription: {
      color: colors.textSecondary + '99',
      fontWeight: '400',
      fontSize: 15,
      lineHeight: 20,
      flex: 1,
    },
    divider: {
      backgroundColor: colors.border,
      height: 1,
      marginVertical: 16,
    },
    gradient: {
      position: 'absolute',
      left: 0,
      bottom: 0,
      right: 0,
      alignItems: 'center',
    },
    artistContainer: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: 'center',
      alignItems: 'center',
    },
    artist: {
      textAlign: 'center',
      color: colors.white,
      fontSize: 48,
      fontWeight: 'bold',
    },
    tracks: {
      paddingTop: 32,
      backgroundColor: colors.white,
    },
    buttonRejectContainer: {
      backgroundColor: colors.error,
      borderRadius: 50,
    },
    buttonText: {
      fontSize: 15,
      lineHeight: 16,
      fontWeight: '500',
      color: colors.white,
    },
    likeContainer: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
    },
    textRowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    verifiedIcon: {
      width: 35,
      height: 35,
    },
    customMarkerContainer: {
      height: 80,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },
    markerContent: {
      backgroundColor: colors.white,
      padding: 8,
      borderRadius: 8,
      marginHorizontal: 20,
      height: 60,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 3},
      shadowOpacity: 0.3,
      shadowRadius: 5,
      elevation: 5, // For Android shadow
      alignItems: 'center',
      position: 'relative', // For absolute positioning of the arrow
    },
    addressText: {
      fontSize: 14,
      color: colors.textPrimary,
      textAlign: 'center',
    },
    arrowContainer: {
      position: 'absolute',
      bottom: 0, // Position the arrow outside the bubble
      left: '50%',
      transform: [{translateX: -15}], // Center the arrow
      zIndex: 1, // Ensure the arrow appears above the background
    },
    arrow: {
      width: 0,
      height: 0,
      borderLeftWidth: 10,
      borderRightWidth: 10,
      borderTopWidth: 10,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderTopColor: colors.white, // Same color as the background
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 3},
      shadowOpacity: 0.3,
    },
  });

const getDetailStyles = (colors: any) =>
  StyleSheet.create({
    background: {
      backgroundColor: colors.background,
      flex: 1,
    },
    imageWrapper: {
      height: 300,
    },
    image: {
      ...StyleSheet.absoluteFillObject,
      width: undefined,
      height: undefined,
    },
    header: {
      fontSize: 40,
      color: colors.textPrimary,
    },
    price: {
      fontSize: 46,
      color: colors.warning,
    },
    priceWrapper: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'space-between',
    },
    description: {
      color: colors.textPrimary,
      fontSize: 16,
      lineHeight: 25,
    },
    readMoreTest: {
      color: colors.statusBlue,
    },
    callToAction: {
      backgroundColor: colors.black,
      padding: 16,
      borderRadius: 50,
      width: '100%',
      marginBottom: 48,
    },
    callToActionText: {
      color: colors.white,
      textAlign: 'center',
      textTransform: 'uppercase',
      fontSize: 22,
      fontWeight: 'bold',
    },
    margin: {
      marginBottom: 24,
    },
    container: {
      flex: 1,
      marginHorizontal: 16,
      paddingTop: 32,
    },
    picker: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 32,
      backgroundColor: colors.gray100,
      borderRadius: 20,
    },
    pickerItem: {
      padding: 8,
      width: 90,
      borderRadius: 20,
      textAlign: 'center',
    },
    pickerItemSelected: {
      borderWidth: 1,
      borderColor: colors.textPrimary,
    },
    linkColor: {
      color: colors.statusBlue,
    },
  });

export {getStyles, getDetailStyles};
