export enum CATEGORY_ENUM {
  Animals = 'Animals',
  ArtsAndCrafts = 'Arts and Crafts',
  Extreme = 'Extreme',
  FoodAndDrink = 'Food and Drink',
  Games = 'Games',
  Intellectual = 'Intellectual',
  Music = 'Music',
  Other = 'Other',
  Outdoors = 'Outdoors',
  Party = 'Party',
  PerformingArts = 'Performing Arts',
  Sports = 'Sports',
}

export type CategoryType = {
  category_id: number;
  category_name: string;
  image_url: string;
  category_repr?: string;
};

export interface SubCategoryType {
  category_id: number;
  subcategory_id: number;
  subcategory_name: string;
  image_url: string;
  subcategory_repr?: string;
}
