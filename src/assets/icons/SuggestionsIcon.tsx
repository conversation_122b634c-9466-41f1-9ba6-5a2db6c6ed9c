import Svg, {Path} from 'react-native-svg';

const SuggestionsIcon = ({color = 'white'}) => (
  <Svg width="21" height="21" viewBox="0 0 21 21" fill="none">
    <Path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M21 10.5C21 16.2443 16.2512 21 10.4991 21C4.75719 21 0 16.2443 0 10.5C0 4.74744 4.74886 0 10.4908 0C16.2428 0 21 4.74744 21 10.5ZM9.25176 11.6954C9.25176 12.2974 9.617 12.6113 10.2655 12.6113C10.8452 12.6113 11.2236 12.2703 11.2529 11.8353C11.2534 11.8237 11.2539 11.8114 11.2544 11.7992C11.2557 11.7667 11.257 11.7341 11.2583 11.7133C11.2912 11.1622 11.6596 10.7952 12.3438 10.3504C13.3896 9.67034 14.0682 9.0641 14.0682 7.8311C14.0682 6.06809 12.4784 5.07355 10.598 5.07355C8.77976 5.07355 7.55677 5.89163 7.22828 6.88291C7.16831 7.05836 7.12399 7.23848 7.12399 7.43609C7.12399 7.94393 7.52933 8.26392 7.95907 8.26392C8.34225 8.26392 8.61079 8.09294 8.82217 7.82337L8.98324 7.60218C9.34648 7.02887 9.83938 6.72718 10.4731 6.72718C11.3195 6.72718 11.8901 7.22932 11.8901 7.9362C11.8901 8.60567 11.4494 8.93295 10.5566 9.55506C9.80634 10.0818 9.25176 10.6197 9.25176 11.5834V11.6954ZM9.00067 14.6845C9.00067 15.3483 9.56947 15.8453 10.2507 15.8453C10.9319 15.8453 11.4933 15.3566 11.4933 14.6845C11.4933 14.0179 10.9402 13.5153 10.2507 13.5153C9.56114 13.5153 9.00067 14.0244 9.00067 14.6845Z"
      fill={color}
    />
  </Svg>
);

export default SuggestionsIcon;
