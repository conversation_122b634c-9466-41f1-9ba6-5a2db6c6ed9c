import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {UserType} from '~types/api/user';
import {api, timing, error} from '~utils/debugLogger';

export const useGetUserType = (user_id?: string) => {
  return useQuery<unknown, Error, UserType>(['userType'], async () => {
    api('Starting getUserType API call');
    const apiStartTime = Date.now();

    if (!user_id) {
      api('getUserType: No user_id provided');
      return;
    }

    api('getUserType: Getting auth token');
    const tokenStart = Date.now();
    const token = await FirebaseAuth.getAuthToken();
    timing('getUserType: Auth token obtained', tokenStart);

    api('getUserType: Making API request');
    const requestStart = Date.now();
    const response = await fetch(`${Config.BASE_API_URL}accounts/${user_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      error(`getUserType: Request failed with status ${response.status}`);
      throw new Error('Failed to get user type');
    }

    timing('getUserType: Request completed', requestStart);
    const data = await response.json();
    timing('getUserType: Total time', apiStartTime);

    return data;
  });
};
