import {StyleSheet} from 'react-native';
const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
    },
    title: {
      fontSize: 15,
      fontWeight: '400',
    },
    rightContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginLeft: 10,
      paddingRight: 12,
      height: '100%',
    },
    dateTime: {
      fontSize: 13,
      fontWeight: '400',
      lineHeight: 15,
      color: colors.error,
    },
    pinImageSize: {
      width: 24,
      height: 24,
    },
    pickerContainer: {
      paddingHorizontal: 10,
      paddingVertical: 10,
      borderTopLeftRadius: 5,
      borderBottomLeftRadius: 5,
      backgroundColor: colors.background,
    },
  });

export default createStyles;
