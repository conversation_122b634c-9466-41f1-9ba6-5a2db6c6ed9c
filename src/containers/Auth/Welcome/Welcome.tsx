import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, StatusBar, Text, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {PyxiLabel} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import {SCREENS} from '~constants';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import createStyles from '../styles';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';

const WelcomeView = () => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {t} = useTranslation();
  const {width, height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const isAndroid = Platform.OS === 'android';

  useEffect(() => {
    logScreenView('Welcome', 'WelcomeView');
  }, []);

  // Auto-redirect to login after 3 seconds, but allow user interaction to override
  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.navigate(SCREENS.LOGIN);
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigation]);

  const handleGetStarted = () => {
    navigation.navigate(SCREENS.LOGIN);
  };

  const handleSignUp = () => {
    navigation.navigate(SCREENS.SIGN_UP);
  };

  const paddingBottom = isAndroid ? bottom + 20 : bottom;

  return (
    <SafeAreaView style={styles.flex} edges={['right', 'left']}>
      <ScrollView showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
        <KeyboardAwareScrollView
          style={[styles.flex, {minHeight: height - (StatusBar.currentHeight || 0) - 20}]}
          bounces={false}
          enableOnAndroid={true}
          extraHeight={Platform.OS === 'android' ? 50 : 0}
          extraScrollHeight={50}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always">
          <View style={[styles.flex, {minHeight: height - (StatusBar.currentHeight || 0) - 20}]}>
            <View style={[styles.fullWidth, {height: width * 0.631, position: 'relative'}]}>
              <FastImage
                source={require('../../../assets/images/SignInImage.png')}
                style={{width: '100%', height: '100%'}}
                resizeMode="cover"
              />
              <View style={styles.overlay}>
                <Text style={styles.welcomeText}>Welcome to Pyxi!</Text>
                <Text style={styles.descText}>Discover events & make friends!</Text>
              </View>
            </View>

            <View style={styles.iconContainer}>
              <PyxiLabel />
              <View style={{marginTop: 20, width: '100%', alignItems: 'center'}}>
                <Text style={{fontSize: 24, fontWeight: '600', marginBottom: 8, textAlign: 'center'}}>
                  Ready to explore?
                </Text>
                <Text style={{fontSize: 16, color: colors.textSecondary, textAlign: 'center', marginBottom: 32}}>
                  Join thousands of people discovering amazing events and making new connections.
                </Text>
              </View>
            </View>

            <View style={styles.formContainer}>
              <ModernButton
                onPress={handleGetStarted}
                title="Get Started"
                variant="primary"
                size="lg"
                fullWidth
                style={styles.loginButton}
              />

              <ModernButton
                onPress={handleSignUp}
                title="Create Account"
                variant="outline"
                size="lg"
                fullWidth
                style={styles.loginButton}
              />
            </View>

            <View
              style={[
                styles.bottomContainer,
                {
                  paddingBottom,
                },
              ]}>
              <Text style={styles.bottomText}>Already have an account?</Text>
              <TouchableOpacity style={styles.registerButton} onPress={handleGetStarted}>
                <Text style={styles.registerText}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </ScrollView>
    </SafeAreaView>
  );
};

export default WelcomeView;
