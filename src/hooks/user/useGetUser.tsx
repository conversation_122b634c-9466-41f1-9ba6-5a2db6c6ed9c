import Config from 'react-native-config';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {User} from '~types/api/user';
import {api, timing, error} from '~utils/debugLogger';

export function useGetUserAccount(user_id: any, shouldFetch = true) {
  return useQuery<Omit<User, 'subcategories' | 'groups'> | undefined, Error>(
    ['userAccount', user_id],
    async () => {
      api('Starting getUserAccount API call');
      const apiStartTime = Date.now();

      if (!user_id) {
        api('getUserAccount: No user_id provided');
        return undefined;
      }

      api('getUserAccount: Getting auth token');
      const tokenStart = Date.now();
      const token = await FirebaseAuth.getAuthToken();
      timing('getUserAccount: Auth token obtained', tokenStart);

      api('getUserAccount: Making API request');
      const requestStart = Date.now();
      const userResponse = await fetch(`${Config.BASE_API_URL}users/${user_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (!userResponse.ok) {
        if (userResponse.status === 404) {
          api(`getUserAccount: User account not found for user ${user_id}`);
          return undefined; // Return undefined instead of throwing for 404
        }
        error(`getUserAccount: Request failed with status ${userResponse.status}`);
        throw new Error(`Failed to get user account: ${userResponse.status}`);
      }

      timing('getUserAccount: Request completed', requestStart);
      const userData = (await userResponse.json()) as Omit<User, 'subcategories' | 'groups'>;
      timing('getUserAccount: Total time', apiStartTime);

      return userData;
    },
    {enabled: shouldFetch},
  );
}
