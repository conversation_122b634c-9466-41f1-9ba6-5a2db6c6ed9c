import {ChatIcon, LogoIcon, LogoIconBar, MyEvents, SettingsIcon} from '~assets/icons';
import {SCREENS} from '~constants';
import {LoginView} from '~containers/Auth/Login';
import {SignUpView} from '~containers/Auth/SignUp';
import {ForgotPasswordView} from '~containers/Auth/ForgotPassword';
import {WelcomeView} from '~containers/Auth/Welcome';
import ModernSplash from '~containers/Auth/ModernSplash';
import MagicalSplash from '~containers/Auth/MagicalSplash';
import ModernIntro from '~containers/Auth/ModernIntro';
import ModernAuth from '~containers/Auth/ModernAuth';
import {SettingsView} from '~containers/Settings';
import Home from '~containers/Home';
import ModernEventDetails from '~containers/Event/ModernEventDetails/ModernEventDetails';
import SingleUserInfo from '~containers/SingleUserInfo';
import {HomeStack} from './HomeStack';
import ChangePassword from '~containers/Settings/ChangePassword';
import {SettingsStack} from './SettingsStack';
import HelpCenter from '~containers/Settings/HelpCenter';
import DeleteLoading from '~containers/Settings/DeleteLoading';
import PasswordForDeleteAccount from '~containers/Settings/PasswordForDeleteAccount/PasswordForDeleteAccount';
import LogOut from '~containers/Settings/LogOut';
import PersonalInfo from '~containers/Settings/PersonalInfo';
import {
  CreateEventGroups,
  CreateEventInfo,
  CreateEventPublishModal,
  CreateEventTemplate,
} from '~containers/Event/CreateEvent';
import {AccountTypeView} from '~containers/Onboarding/AccountType';
import OnboardingPersonalInfo from '~containers/Onboarding/PersonalInfo';
import OnboardingBusinessInfo from '~containers/Onboarding/BusinessInfo';
import GroupsOnboarding from '~containers/Onboarding/Group';
import OnboardingSubcategories from '~containers/Onboarding/Subcategories';
import OnboardingChildrenQuantity from '~containers/Onboarding/Children';
import ChatView from '~containers/Chats/Chat/ChatView';
import {ChatStack} from './ChatStack';
import ChatWithConcierge from '~containers/Chats/ChatWithConcierge/ChatWithConcierge';
import MatchingUsersScreen from '~containers/Event/Matching/MatchingUsersScreen/MatchingUsersScreen';
import ChatWithUsers from '~containers/Chats/ChatWithUsers';
import EditEvent from '~containers/Event/EditEvent/EditEvent';
import EditSubcategory from '~containers/Event/EditEvent/EditSubcategory/EditSubcategory';
import PendingAttendeesScreen from '~containers/Event/EventDetails/PendingAttendeesScreen/PendingAttendeesScreen';
import PersonalInfoBusiness from '~containers/Onboarding/PersonalInfo/PersonalInfoBusiness';
import EditSubcategories from '~containers/Settings/EditSubcategories';
import BuyTicket from '~containers/Event/BuyTicket/BuyTicket';
import PaymentSuccess from '~containers/Event/PaymentSuccess/PaymentSuccess';
import PurchaseHistory from '~containers/Settings/PurchaseHistory/PurchaseHistory';
import Groups from '~containers/Settings/Groups';
import OnboardingQuestion from '~containers/Onboarding/Preferances';
import RateEvent from '~containers/Event/RateEvent/RateEvent';

export const matchingScreens = [
  {
    name: SCREENS.MATCHING_USERS,
    options: {headerShown: false},
    component: MatchingUsersScreen,
  },
];
export const createEventScreens = [
  {name: SCREENS.CREATE_EVENT_GROUPS, options: {headerShown: false}, component: CreateEventGroups},
  {name: SCREENS.CREATE_EVENT_TEMPLATE, options: {headerShown: false}, component: CreateEventTemplate},
  {name: SCREENS.CREATE_EVENT_INFO, options: {headerShown: false}, component: CreateEventInfo},
  {
    name: SCREENS.CREATE_EVENT_SUBMITTING,
    options: {headerShown: false, animation: 'slide_from_bottom' as never},
    component: CreateEventPublishModal,
  },
];

export const appScreens = [
  {
    name: SCREENS.HOME_STACK,
    component: HomeStack,
    title: '',
    icon: LogoIcon,
    iconBar: LogoIconBar,
    marker: false,
    params: {},
  },
  {
    name: SCREENS.MY_EVENTS,
    component: HomeStack,
    title: 'My events',
    icon: MyEvents,
    iconBar: MyEvents,
    marker: false,
    params: {
      myEvent: true,
    },
  },
  {
    name: SCREENS.CHAT_STACK,
    component: ChatStack,
    title: 'chat.header',
    icon: ChatIcon,
    iconBar: ChatIcon,
    marker: false,
    params: {},
  },
  {
    name: SCREENS.SETTINGS_STACK,
    component: SettingsStack,
    title: 'settings.title',
    icon: SettingsIcon,
    iconBar: SettingsIcon,
    marker: true,
    params: {},
  },
];

export const chatScreens = [
  {name: SCREENS.CHATS, options: {headerShown: false}, component: ChatView},
  {
    name: SCREENS.CONCIERGE_CHAT,
    options: {headerShown: false},
    component: ChatWithConcierge,
  },
  {
    name: SCREENS.USER_CHAT,
    options: {headerShown: false, gestureEnabled: false},
    component: ChatWithUsers,
  },
  {
    name: SCREENS.PERSONAL_INFO,
    options: {headerShown: false, animation: 'slide_from_bottom' as never},
    component: SingleUserInfo,
  },
  {name: SCREENS.HOME_EVENT, options: {headerShown: false}, component: ModernEventDetails},
];
export const authScreens = [
  {name: SCREENS.MAGICAL_SPLASH, options: {headerShown: false}, component: MagicalSplash},
  {name: SCREENS.MODERN_INTRO, options: {headerShown: false}, component: ModernIntro},
  {name: SCREENS.MODERN_AUTH, options: {headerShown: false}, component: ModernAuth},
  {name: SCREENS.MODERN_SPLASH, options: {headerShown: false}, component: ModernSplash},
  {name: SCREENS.WELCOME, options: {headerShown: false}, component: WelcomeView},
  {name: SCREENS.LOGIN, options: {headerShown: false}, component: LoginView},
  {name: SCREENS.SIGN_UP, options: {headerShown: false}, component: SignUpView},
  {name: SCREENS.FORGOT_PASSWORD, options: {headerShown: false}, component: ForgotPasswordView},
];

export const onboardingScreens = [
  {name: SCREENS.ONBOARDING_ACCOUNT_TYPE, options: {headerShown: false}, component: AccountTypeView},
  {name: SCREENS.ONBOARDING_PERSONAL_INFO, options: {headerShown: false}, component: OnboardingPersonalInfo},
  {name: SCREENS.ONBOARDING_BUSINESS_INFO, options: {headerShown: false}, component: OnboardingBusinessInfo},
  {name: SCREENS.ONBOARDING_GROUP, options: {headerShown: false}, component: GroupsOnboarding},
  {name: SCREENS.EDIT_PREFERANCE, options: {headerShown: false}, component: OnboardingQuestion},
  {
    name: SCREENS.ONBOARDING_CHILDREN,
    options: {headerShown: false, animation: 'slide_from_bottom' as never},
    component: OnboardingChildrenQuantity,
  },
  {name: SCREENS.ONBOARDING_SUBCATEGORIES, options: {headerShown: false}, component: OnboardingSubcategories},
];

export const homeScreens = [
  {name: SCREENS.HOME, options: {headerShown: false}, component: Home},
  {name: SCREENS.HOME_EVENT, options: {headerShown: false}, component: ModernEventDetails},
  {name: SCREENS.BUY_TICKET, options: {headerShown: false}, component: BuyTicket},
  {name: SCREENS.RATE_EVENT, options: {headerShown: false}, component: RateEvent},
  {name: SCREENS.PAYMENT_SUCCESS, options: {headerShown: false}, component: PaymentSuccess},
  {name: SCREENS.EDIT_EVENT, options: {headerShown: false}, component: EditEvent},
  {name: SCREENS.EDIT_PREFERANCE, options: {headerShown: false}, component: OnboardingQuestion},
  {name: SCREENS.EDIT_SUBCATEGORY, options: {headerShown: false}, component: EditSubcategory},
  {name: SCREENS.PENDING_ATTENDEES, options: {headerShown: false}, component: PendingAttendeesScreen},
  {
    name: SCREENS.PERSONAL_INFO,
    options: {headerShown: false, animation: 'slide_from_bottom' as never},
    component: SingleUserInfo,
  },
  ...createEventScreens,
  ...matchingScreens,
];

export const calendarScreens = [];
export const eventsScreens = [];

export const settingsScreens = [
  {name: SCREENS.SETTINGS, options: {headerShown: false}, component: SettingsView},
  {name: SCREENS.CHANGE_PASSWORD, options: {headerShown: false}, component: ChangePassword},
  {name: SCREENS.PURCHASE_HISTORY, options: {headerShown: false}, component: PurchaseHistory},
  {name: SCREENS.HELP_CENTER, options: {headerShown: false}, component: HelpCenter},
  {name: SCREENS.DELETE_LOADING, options: {headerShown: false}, component: DeleteLoading},
  {name: SCREENS.PASSWORD_FOR_DELETE_ACCOUNT, options: {headerShown: false}, component: PasswordForDeleteAccount},
  {name: SCREENS.LOGOUT, options: {headerShown: false, animation: 'slide_from_bottom' as never}, component: LogOut},
  {name: SCREENS.CHANGE_PROFILE_INFO, options: {headerShown: false}, component: PersonalInfo},
  {name: SCREENS.CHANGE_PROFILE_INFO_BUSINESS, options: {headerShown: false}, component: PersonalInfoBusiness},
  {name: SCREENS.EDIT_SUBCATEGORIES, options: {headerShown: false}, component: EditSubcategories},
  {name: SCREENS.EDIT_PREFERANCE, options: {headerShown: false}, component: OnboardingQuestion},
  {name: SCREENS.RATE_EVENT, options: {headerShown: false}, component: RateEvent},
  {name: SCREENS.PAYMENT_SUCCESS, options: {headerShown: false}, component: PaymentSuccess},
  {name: SCREENS.GROUPS, options: {headerShown: false}, component: Groups},
];
export const screensOutOfBottomTab = [];
