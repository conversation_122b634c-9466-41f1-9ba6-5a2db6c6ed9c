#!/usr/bin/env node

/**
 * Script to add missing useTheme hooks to components that reference colors
 * Usage: node scripts/add-missing-theme-hooks.js
 */

const fs = require('fs');
const path = require('path');

// File extensions to process
const EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to process
const DIRECTORIES = ['src/components', 'src/containers'];

function addMissingThemeHook(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Check if file references colors but doesn't have useTheme
  const hasColorsReference = content.includes('colors.');
  const hasThemeImport = content.includes('useTheme') && content.includes('~contexts/ThemeContext');
  const hasThemeHook =
    content.includes('const {colors} = useTheme()') || content.includes('const { colors } = useTheme()');

  if (hasColorsReference && !hasThemeHook) {
    console.log(`Adding theme hook to: ${filePath}`);

    // Add import if not present
    if (!hasThemeImport) {
      // Find existing imports from ~contexts/ThemeContext
      const themeContextImportRegex = /import\s+{([^}]+)}\s+from\s+['"]~contexts\/ThemeContext['"];?/;
      const match = content.match(themeContextImportRegex);

      if (match) {
        // Add useTheme to existing import
        const existingImports = match[1];
        if (!existingImports.includes('useTheme')) {
          const newImports = existingImports.trim().includes(',')
            ? `${existingImports}, useTheme`
            : `${existingImports}, useTheme`;
          content = content.replace(match[0], `import {${newImports}} from '~contexts/ThemeContext';`);
          hasChanges = true;
        }
      } else {
        // Add new import after other imports
        const importRegex = /import[^;]+;/g;
        const imports = content.match(importRegex) || [];
        if (imports.length > 0) {
          const lastImport = imports[imports.length - 1];
          const lastImportIndex = content.lastIndexOf(lastImport);
          const insertIndex = lastImportIndex + lastImport.length;
          content =
            content.slice(0, insertIndex) +
            "\nimport {useTheme} from '~contexts/ThemeContext';" +
            content.slice(insertIndex);
          hasChanges = true;
        }
      }
    }

    // Add useTheme hook in component
    const componentRegex = /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*{/;
    const componentMatch = content.match(componentRegex);
    if (componentMatch) {
      const hookInsertPoint = componentMatch.index + componentMatch[0].length;
      if (!content.includes('const {colors} = useTheme();') && !content.includes('const { colors } = useTheme();')) {
        content =
          content.slice(0, hookInsertPoint) + '\n  const {colors} = useTheme();' + content.slice(hookInsertPoint);
        hasChanges = true;
      }
    } else {
      // Try function component pattern
      const funcComponentRegex = /function\s+(\w+)\s*\([^)]*\)\s*{/;
      const funcMatch = content.match(funcComponentRegex);
      if (funcMatch) {
        const hookInsertPoint = funcMatch.index + funcMatch[0].length;
        if (!content.includes('const {colors} = useTheme();') && !content.includes('const { colors } = useTheme();')) {
          content =
            content.slice(0, hookInsertPoint) + '\n  const {colors} = useTheme();' + content.slice(hookInsertPoint);
          hasChanges = true;
        }
      }
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Added theme hook to: ${filePath}`);
  }

  return hasChanges;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath, {withFileTypes: true});

  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);

    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (EXTENSIONS.some(ext => file.name.endsWith(ext))) {
      try {
        addMissingThemeHook(fullPath);
      } catch (error) {
        console.error(`❌ Error processing ${fullPath}:`, error.message);
      }
    }
  }
}

// Main execution
console.log('🎨 Adding missing theme hooks...\n');

for (const dir of DIRECTORIES) {
  console.log(`Processing directory: ${dir}`);
  processDirectory(dir);
}

console.log('\n✨ Theme hook addition completed!');
