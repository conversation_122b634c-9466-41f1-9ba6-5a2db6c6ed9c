import React from 'react';
import {View, ViewStyle} from 'react-native';
import Animated, {useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing} from 'react-native-reanimated';
import Svg, {Circle, Path, Defs, LinearGradient, Stop} from 'react-native-svg';
import {lightTheme} from '~constants/colors';
import {useTheme} from '~contexts/ThemeContext';

interface ModernSpinnerProps {
  size?: number;
  color?: string;
  variant?: 'circular' | 'dots' | 'pulse' | 'gradient';
  style?: ViewStyle;
}

const ModernSpinner: React.FC<ModernSpinnerProps> = ({
  size = 40,
  color = lightTheme.primary,
  variant = 'circular',
  style,
}) => {
  const {colors} = useTheme();
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);

  React.useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 2000, // Slower animation - increased from 1000ms to 2000ms
        easing: Easing.linear,
      }),
      -1,
      false,
    );

    if (variant === 'pulse') {
      scale.value = withRepeat(
        withTiming(1.2, {
          duration: 1600, // Slower pulse animation - increased from 800ms to 1600ms
          easing: Easing.inOut(Easing.ease),
        }),
        -1,
        true,
      );
    }
  }, [rotation, scale, variant]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{rotate: `${rotation.value}deg`}, {scale: variant === 'pulse' ? scale.value : 1}],
  }));

  const renderCircularSpinner = () => (
    <Animated.View style={animatedStyle}>
      <Svg width={size} height={size} viewBox="0 0 24 24">
        <Defs>
          <LinearGradient id="spinnerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={color} stopOpacity="1" />
            <Stop offset="100%" stopColor={color} stopOpacity="0.3" />
          </LinearGradient>
        </Defs>
        <Circle cx="12" cy="12" r="10" stroke={colors.gray200} strokeWidth="2" fill="none" />
        <Path
          d="M12 2 A10 10 0 0 1 22 12"
          stroke="url(#spinnerGradient)"
          strokeWidth="2"
          strokeLinecap="round"
          fill="none"
        />
      </Svg>
    </Animated.View>
  );

  const renderDotsSpinner = () => {
    const dotSize = size / 8;
    const radius = size / 3;

    return (
      <Animated.View style={animatedStyle}>
        <View style={{width: size, height: size, position: 'relative'}}>
          {[0, 1, 2, 3, 4, 5].map(index => {
            const angle = index * 60 * (Math.PI / 180);
            const x = radius * Math.cos(angle) + size / 2 - dotSize / 2;
            const y = radius * Math.sin(angle) + size / 2 - dotSize / 2;

            return (
              <View
                key={index}
                style={{
                  position: 'absolute',
                  left: x,
                  top: y,
                  width: dotSize,
                  height: dotSize,
                  borderRadius: dotSize / 2,
                  backgroundColor: color,
                  opacity: 1 - index * 0.15,
                }}
              />
            );
          })}
        </View>
      </Animated.View>
    );
  };

  const renderPulseSpinner = () => (
    <Animated.View style={[animatedStyle, {opacity: 0.8}]}>
      <View
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
        }}
      />
    </Animated.View>
  );

  const renderGradientSpinner = () => (
    <Animated.View style={animatedStyle}>
      <Svg width={size} height={size} viewBox="0 0 24 24">
        <Defs>
          <LinearGradient id="gradientSpinner" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={colors.primary} stopOpacity="1" />
            <Stop offset="50%" stopColor={colors.secondary} stopOpacity="0.8" />
            <Stop offset="100%" stopColor={colors.primary} stopOpacity="0.3" />
          </LinearGradient>
        </Defs>
        <Circle
          cx="12"
          cy="12"
          r="10"
          stroke="url(#gradientSpinner)"
          strokeWidth="3"
          strokeLinecap="round"
          fill="none"
          strokeDasharray="31.416"
          strokeDashoffset="15.708"
        />
      </Svg>
    </Animated.View>
  );

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return renderDotsSpinner();
      case 'pulse':
        return renderPulseSpinner();
      case 'gradient':
        return renderGradientSpinner();
      case 'circular':
      default:
        return renderCircularSpinner();
    }
  };

  return (
    <View
      style={[
        {
          justifyContent: 'center',
          alignItems: 'center',
          width: size,
          height: size,
        },
        style,
      ]}>
      {renderSpinner()}
    </View>
  );
};

// Pre-built spinner variants for common use cases
export const LoadingSpinner: React.FC<{size?: number}> = ({size = 40}) => (
  <ModernSpinner size={size} variant="circular" />
);

export const PulseSpinner: React.FC<{size?: number; color?: string}> = ({size = 20, color = lightTheme.primary}) => (
  <ModernSpinner size={size} variant="pulse" color={color} />
);

export const GradientSpinner: React.FC<{size?: number}> = ({size = 50}) => (
  <ModernSpinner size={size} variant="gradient" />
);

export const DotsSpinner: React.FC<{size?: number; color?: string}> = ({size = 40, color = lightTheme.primary}) => (
  <ModernSpinner size={size} variant="dots" color={color} />
);

export default ModernSpinner;
