import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    buttonContainer1: {
      flex: 1,
      backgroundColor: colors.gray400,
    },
    buttonContainer2: {
      flex: 1,
      backgroundColor: colors.error,
    },
    rowView: {
      flexDirection: 'row',
      marginTop: 5,
    },
    safeAreaView: {
      flex: 1,
      paddingBottom: 20,
      marginTop: 40,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContentContainerStyle: {
      flexGrow: 0,
      padding: 16,
    },
    updateEventText: {
      lineHeight: 41,
      fontSize: 34,
      color: colors.textPrimary,
      fontWeight: '700',
    },
    buttonContainer: {
      paddingHorizontal: 16,
    },
    buttonContainerStyle: {
      backgroundColor: colors.primary,
    },
    buttonTextStyle: {
      color: colors.white,
    },
  });

export default createStyles;
