import React from 'react';
import {View, ViewStyle} from 'react-native';
import Animated, {useSharedValue, useAnimatedStyle, withRepeat, withTiming, interpolate} from 'react-native-reanimated';
import {borderRadius, spacing, shadows} from '~constants/design';
import {useTheme} from '~contexts/ThemeContext';

interface ModernSkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: keyof typeof borderRadius;
  style?: ViewStyle;
  animated?: boolean;
}

const ModernSkeleton: React.FC<ModernSkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius: borderRadiusProp = 'md',
  style,
  animated = true,
}) => {
  const {colors} = useTheme();
  const shimmerValue = useSharedValue(0);

  React.useEffect(() => {
    if (animated) {
      shimmerValue.value = withRepeat(withTiming(1, {duration: 1500}), -1, false);
    }

    // Cleanup animation on unmount
    return () => {
      try {
        if (shimmerValue) {
          shimmerValue.value = 0;
        }
      } catch (error) {
        // Silently handle cleanup errors
        if (__DEV__) {
          console.warn('ModernSkeleton cleanup warning:', error);
        }
      }
    };
  }, [animated, shimmerValue]);

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(shimmerValue.value, [0, 0.5, 1], [0.3, 0.7, 0.3]);

    return {
      opacity: animated ? opacity : 0.3,
    };
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: colors.gray200,
          borderRadius: borderRadius[borderRadiusProp],
        },
        animatedStyle,
        style,
      ]}
    />
  );
};

// Pre-built skeleton components for common use cases
export const EventCardSkeleton: React.FC = () => {
  const {colors} = useTheme();

  return (
    <View
      style={{
        backgroundColor: colors.surface,
        borderRadius: borderRadius.xl,
        padding: spacing.xs,
        margin: spacing.sm,
        ...shadows.sm,
      }}>
      <ModernSkeleton width="100%" height={200} borderRadius="lg" style={{marginBottom: spacing.sm}} />
      <View style={{padding: spacing.md}}>
        <ModernSkeleton width="80%" height={24} borderRadius="sm" style={{marginBottom: spacing.xs}} />
        <ModernSkeleton width="60%" height={16} borderRadius="sm" style={{marginBottom: spacing.sm}} />
        <ModernSkeleton width="40%" height={14} borderRadius="sm" />
      </View>
    </View>
  );
};

export const EventListSkeleton: React.FC<{count?: number}> = ({count = 3}) => (
  <View style={{paddingHorizontal: spacing.lg}}>
    {Array.from({length: count}).map((_, index) => (
      <EventCardSkeleton key={index} />
    ))}
  </View>
);

export const ProfileSkeleton: React.FC = () => (
  <View style={{padding: spacing.lg, alignItems: 'center'}}>
    <ModernSkeleton width={80} height={80} borderRadius="full" style={{marginBottom: spacing.md}} />
    <ModernSkeleton width="60%" height={24} borderRadius="sm" style={{marginBottom: spacing.xs}} />
    <ModernSkeleton width="40%" height={16} borderRadius="sm" style={{marginBottom: spacing.lg}} />
    <ModernSkeleton width="100%" height={100} borderRadius="lg" />
  </View>
);

export const ChatListSkeleton: React.FC<{count?: number}> = ({count = 5}) => (
  <View>
    {Array.from({length: count}).map((_, index) => (
      <View
        key={index}
        style={{
          flexDirection: 'row',
          padding: spacing.md,
          alignItems: 'center',
        }}>
        <ModernSkeleton width={50} height={50} borderRadius="full" style={{marginRight: spacing.md}} />
        <View style={{flex: 1}}>
          <ModernSkeleton width="70%" height={16} borderRadius="sm" style={{marginBottom: spacing.xs}} />
          <ModernSkeleton width="50%" height={14} borderRadius="sm" />
        </View>
        <ModernSkeleton width={40} height={12} borderRadius="sm" />
      </View>
    ))}
  </View>
);

export const EventDetailsSkeleton: React.FC = () => (
  <View>
    <ModernSkeleton width="100%" height={300} borderRadius="none" style={{marginBottom: spacing.lg}} />
    <View style={{padding: spacing.lg}}>
      <ModernSkeleton width="90%" height={32} borderRadius="sm" style={{marginBottom: spacing.md}} />
      <ModernSkeleton width="100%" height={80} borderRadius="lg" style={{marginBottom: spacing.lg}} />
      <ModernSkeleton width="100%" height={200} borderRadius="lg" style={{marginBottom: spacing.lg}} />
      <View style={{flexDirection: 'row', gap: spacing.sm}}>
        <ModernSkeleton width={60} height={40} borderRadius="lg" />
        <ModernSkeleton width={80} height={40} borderRadius="lg" />
        <ModernSkeleton width={70} height={40} borderRadius="lg" />
      </View>
    </View>
  </View>
);

export default ModernSkeleton;
