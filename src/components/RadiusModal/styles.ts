import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    container: {
      width: '100%',
      backgroundColor: colors.surface,
      padding: 18,
      paddingBottom: 44,
      borderRadius: 20,
    },
    iconContainer: {
      alignSelf: 'flex-end',
    },
    distanceAdjuster: {
      fontSize: 17,
      lineHeight: 20,
      letterSpacing: 0.35,
      fontWeight: '700',
    },
    distanceAdjusterContainer: {
      flexDirection: 'row',
      gap: 5,
      alignItems: 'center',
    },
    buttonContainer: {
      marginTop: 42,
      paddingVertical: 11,
      backgroundColor: colors.statusGreen,
      borderRadius: 6,
      alignItems: 'center',
    },
    buttonText: {
      fontSize: 12,
      fontWeight: '700',
      color: colors.white,
    },
    maxMinDistancesContainer: {
      marginVertical: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    maxMinDistances: {
      fontSize: 15,
      fontWeight: '400',
      color: colors.textSecondary,
    },
    flex: {
      flex: 1,
    },
  });

export default createStyles;
