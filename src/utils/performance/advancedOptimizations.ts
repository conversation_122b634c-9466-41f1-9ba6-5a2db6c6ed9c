import React from 'react';
import {InteractionManager, Platform, View, Text} from 'react-native';
import {QueryClient} from 'react-query';

// ============================================================================
// ADVANCED PERFORMANCE OPTIMIZATIONS
// ============================================================================

// 1. LAZY LOADING & CODE SPLITTING
// ============================================================================

/**
 * Lazy component loader with preloading capability
 */
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{default: T}>,
  preloadCondition?: () => boolean,
) => {
  let componentPromise: Promise<{default: T}> | null = null;

  const preload = () => {
    if (!componentPromise) {
      componentPromise = importFn();
    }
    return componentPromise;
  };

  // Preload if condition is met
  if (preloadCondition?.()) {
    setTimeout(preload, 100);
  }

  const LazyComponent = React.lazy(() => {
    if (componentPromise) {
      return componentPromise;
    }
    return preload();
  });

  return {LazyComponent, preload};
};

/**
 * Bundle splitting for heavy screens
 */
export const createAsyncScreen = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{default: T}>,
  fallback?: React.ComponentType,
) => {
  const {LazyComponent, preload} = createLazyComponent(importFn);

  return {
    Component: LazyComponent,
    preload,
    fallback: fallback || (() => React.createElement(LoadingPlaceholder)),
  };
};

// 2. PARALLEL API CALLS & BATCHING
// ============================================================================

/**
 * Parallel API call manager
 */
class ParallelAPIManager {
  private batchQueue: Array<{
    key: string;
    request: () => Promise<any>;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];

  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY = 50; // 50ms batching window

  async executeParallel<T>(requests: Array<() => Promise<T>>): Promise<T[]> {
    return Promise.all(requests.map(request => request()));
  }

  async executeBatched<T>(key: string, request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({key, request, resolve, reject});

      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
      }

      this.batchTimeout = setTimeout(() => {
        this.processBatch();
      }, this.BATCH_DELAY);
    });
  }

  private async processBatch() {
    const batch = [...this.batchQueue];
    this.batchQueue = [];
    this.batchTimeout = null;

    // Group by similar requests
    const grouped = batch.reduce(
      (acc, item) => {
        if (!acc[item.key]) {
          acc[item.key] = [];
        }
        acc[item.key].push(item);
        return acc;
      },
      {} as Record<string, typeof batch>,
    );

    // Execute each group in parallel
    await Promise.all(
      Object.values(grouped).map(async group => {
        try {
          const results = await Promise.all(group.map(item => item.request()));
          group.forEach((item, index) => {
            item.resolve(results[index]);
          });
        } catch (error) {
          group.forEach(item => item.reject(error));
        }
      }),
    );
  }
}

export const parallelAPIManager = new ParallelAPIManager();

// 3. ADVANCED IMAGE OPTIMIZATION
// ============================================================================

/**
 * Progressive image loading with multiple quality levels
 */
export interface ProgressiveImageConfig {
  lowQuality: string;
  mediumQuality: string;
  highQuality: string;
  placeholder?: string;
}

export const createProgressiveImageLoader = () => {
  const imageCache = new Map<string, string>();
  const loadingPromises = new Map<string, Promise<string>>();

  const preloadImage = async (uri: string): Promise<string> => {
    if (imageCache.has(uri)) {
      return imageCache.get(uri)!;
    }

    if (loadingPromises.has(uri)) {
      return loadingPromises.get(uri)!;
    }

    const promise = new Promise<string>((resolve, reject) => {
      const image = new Image();
      image.onload = () => {
        imageCache.set(uri, uri);
        resolve(uri);
      };
      image.onerror = reject;
      image.src = uri;
    });

    loadingPromises.set(uri, promise);
    return promise;
  };

  const preloadImageSet = async (config: ProgressiveImageConfig) => {
    // Preload in order: placeholder -> low -> medium -> high
    const loadOrder = [config.placeholder, config.lowQuality, config.mediumQuality, config.highQuality].filter(
      Boolean,
    ) as string[];

    for (const uri of loadOrder) {
      try {
        await preloadImage(uri);
      } catch (error) {
        console.warn(`Failed to preload image: ${uri}`, error);
      }
    }
  };

  return {preloadImage, preloadImageSet, imageCache};
};

// 4. MEMORY MANAGEMENT
// ============================================================================

/**
 * Memory pressure manager
 */
class MemoryManager {
  private memoryWarningListeners: Array<() => void> = [];
  private cleanupTasks: Array<() => void> = [];

  constructor() {
    this.setupMemoryWarningListener();
  }

  private setupMemoryWarningListener() {
    if (Platform.OS === 'ios') {
      // iOS memory warning handling
      const {DeviceEventEmitter} = require('react-native');
      DeviceEventEmitter.addListener('memoryWarning', () => {
        this.handleMemoryPressure();
      });
    }
  }

  addMemoryWarningListener(callback: () => void) {
    this.memoryWarningListeners.push(callback);
  }

  addCleanupTask(task: () => void) {
    this.cleanupTasks.push(task);
  }

  private handleMemoryPressure() {
    console.warn('🚨 Memory pressure detected, cleaning up...');

    // Execute cleanup tasks
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        console.error('Cleanup task failed:', error);
      }
    });

    // Notify listeners
    this.memoryWarningListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Memory warning listener failed:', error);
      }
    });
  }

  forceCleanup() {
    this.handleMemoryPressure();
  }
}

export const memoryManager = new MemoryManager();

// 5. INTERACTION MANAGER OPTIMIZATION
// ============================================================================

/**
 * Deferred execution manager for heavy operations
 */
export const deferHeavyOperation = <T>(
  operation: () => Promise<T> | T,
  priority: 'high' | 'normal' | 'low' = 'normal',
): Promise<T> => {
  return new Promise((resolve, reject) => {
    const execute = async () => {
      try {
        const result = await operation();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    if (priority === 'high') {
      // Execute immediately after current interaction
      InteractionManager.runAfterInteractions(execute);
    } else {
      // Defer to next frame or idle time
      const delay = priority === 'normal' ? 0 : 100;
      setTimeout(() => {
        InteractionManager.runAfterInteractions(execute);
      }, delay);
    }
  });
};

/**
 * Chunked processing for large datasets
 */
export const processInChunks = async <T, R>(
  items: T[],
  processor: (chunk: T[]) => Promise<R[]> | R[],
  chunkSize: number = 50,
  delayBetweenChunks: number = 10,
): Promise<R[]> => {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize);

    await deferHeavyOperation(async () => {
      const chunkResults = await processor(chunk);
      results.push(...chunkResults);
    });

    // Small delay to prevent blocking
    if (i + chunkSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delayBetweenChunks));
    }
  }

  return results;
};

// 6. VIRTUALIZATION OPTIMIZATION
// ============================================================================

/**
 * Dynamic item size calculator for FlashList
 */
export const createDynamicSizeCalculator = () => {
  const sizeCache = new Map<string, number>();
  const averageSize = {value: 100, count: 0};

  const recordSize = (key: string, size: number) => {
    if (!sizeCache.has(key)) {
      sizeCache.set(key, size);

      // Update running average
      averageSize.value = (averageSize.value * averageSize.count + size) / (averageSize.count + 1);
      averageSize.count++;
    }
  };

  const getEstimatedSize = (key: string): number => {
    return sizeCache.get(key) || averageSize.value;
  };

  const getAverageSize = (): number => {
    return averageSize.value;
  };

  return {recordSize, getEstimatedSize, getAverageSize};
};

// 7. NETWORK OPTIMIZATION
// ============================================================================

/**
 * Request prioritization system
 */
export enum RequestPriority {
  CRITICAL = 0,
  HIGH = 1,
  NORMAL = 2,
  LOW = 3,
  BACKGROUND = 4,
}

class RequestQueue {
  private queues: Map<RequestPriority, Array<() => Promise<any>>> = new Map();
  private activeRequests = 0;
  private readonly MAX_CONCURRENT = 6;

  constructor() {
    // Initialize queues
    Object.values(RequestPriority).forEach(priority => {
      if (typeof priority === 'number') {
        this.queues.set(priority, []);
      }
    });
  }

  async enqueue<T>(request: () => Promise<T>, priority: RequestPriority = RequestPriority.NORMAL): Promise<T> {
    return new Promise((resolve, reject) => {
      const wrappedRequest = async () => {
        try {
          this.activeRequests++;
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };

      this.queues.get(priority)!.push(wrappedRequest);
      this.processQueue();
    });
  }

  private processQueue() {
    if (this.activeRequests >= this.MAX_CONCURRENT) {
      return;
    }

    // Process highest priority requests first
    for (const priority of [
      RequestPriority.CRITICAL,
      RequestPriority.HIGH,
      RequestPriority.NORMAL,
      RequestPriority.LOW,
      RequestPriority.BACKGROUND,
    ]) {
      const queue = this.queues.get(priority)!;
      if (queue.length > 0) {
        const request = queue.shift()!;
        request();
        break;
      }
    }
  }
}

export const requestQueue = new RequestQueue();

// Helper components
const LoadingPlaceholder = () =>
  React.createElement(
    View,
    {style: {flex: 1, justifyContent: 'center', alignItems: 'center'}},
    React.createElement(Text, null, 'Loading...'),
  );
