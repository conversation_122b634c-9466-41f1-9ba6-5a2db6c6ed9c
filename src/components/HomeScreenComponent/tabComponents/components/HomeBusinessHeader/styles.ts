import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      overflow: 'hidden',
      backgroundColor: colors.white,
      paddingBottom: 6,
      paddingHorizontal: 16,
      flexDirection: 'row',
    },
    titleText: {
      color: colors.black,
      fontSize: 28,
      fontWeight: '700',
    },
    descriptionText: {
      color: colors.gray400,
      fontSize: 18,
      fontWeight: '500',
    },
    image: {
      width: 70,
      height: 70,
      borderRadius: 12,
    },
    leftContainer: {
      flex: 1,
      justifyContent: 'space-between',
      marginRight: 10,
    },
  });

export default getStyles;
