import {FC} from 'react';
import {TouchableOpacity} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {ModalHeight} from '../../types/modal';
import {CustomModal} from '../CustomModal';
import {ModernCloseIcon} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  isOverflowHidden?: boolean;
}

const EditProfileModal = ({children, isOpen, isOverflowHidden = true, onClose}: IProps) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();

  return (
    <CustomModal
      modalIsVisible={isOpen}
      height={ModalHeight.MAX}
      onCloseModal={onClose}
      backgroundColor={colors.white}
      isOverflowHidden={isOverflowHidden}>
      <TouchableOpacity
        onPress={onClose}
        style={{
          marginTop: top + 5,
          justifyContent: 'center',
          zIndex: 1000,
        }}>
        <ModernCloseIcon size={32} variant="filled" />
      </TouchableOpacity>
      {children}
    </CustomModal>
  );
};

export default EditProfileModal;
