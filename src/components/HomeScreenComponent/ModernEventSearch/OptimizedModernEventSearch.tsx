import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ScrollView,
  Keyboard,
  DeviceEventEmitter,
  Pressable,
} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {BigSearchIcon, LocationIcon, CalendarIcon, FunnelIcon} from '~assets/icons';
import ModernSpinner from '~components/ModernSpinner/ModernSpinner';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import Config from 'react-native-config';
import {haptics} from '~utils/haptics';
import {useTranslation} from 'react-i18next';
import {useOptimizedSearch} from '~hooks/performance/useOptimizedSearch';

interface OptimizedModernEventSearchProps {
  globalInputValue: string;
  setGlobalInputValue: (value: string) => void;
  onFilterPress?: () => void;
  selectedTimeframe?: any;
  selectedEventType?: any;
  isForKids?: boolean;
  radius?: number;
  timeframes?: any[];
  eventTypes?: any[];
}

const OptimizedModernEventSearch: React.FC<OptimizedModernEventSearchProps> = ({
  globalInputValue,
  setGlobalInputValue,
  onFilterPress,
  selectedTimeframe,
  selectedEventType,
  isForKids,
  radius,
  timeframes,
  eventTypes,
}) => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {t} = useTranslation();

  const [isFocused, setIsFocused] = useState(false);

  // Use optimized search hook
  const {
    inputValue,
    setInputValue,
    showSuggestions,
    setShowSuggestions,
    suggestions,
    isLoading,
    handleLocationSelect: optimizedLocationSelect,
    handleEventSelect: optimizedEventSelect,
  } = useOptimizedSearch({
    onLocationSelect: async (placeId: string) => {
      haptics.light();
      setShowSuggestions(false);
      Keyboard.dismiss();

      try {
        const apiKey = Config.GOOGLE_API_KEY;
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${apiKey}`,
          {
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
            },
          },
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        if (data.status === 'OK') {
          const location = data.result;
          setInputValue('');

          DeviceEventEmitter.emit('homeLocationChange', {
            lat: location.geometry.location.lat,
            long: location.geometry.location.lng,
          });
        }
      } catch (error) {
        console.error('Error fetching location details:', error);
      }
    },
    onEventSelect: (event: any) => {
      haptics.light();
      setShowSuggestions(false);
      Keyboard.dismiss();
      DeviceEventEmitter.emit('eventSelection', {event});

      if (event.coords && event.coords.lat) {
        DeviceEventEmitter.emit('homeLocationChange', {
          lat: event.coords.lat,
          long: event.coords.long,
        });
      }
    },
    debounceMs: 300,
    enablePrefetch: true,
    enableOptimistic: true,
  });

  // Calculate number of active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (selectedTimeframe && selectedTimeframe.title !== t('events.all')) {
      count++;
    }
    if (selectedEventType && selectedEventType.title !== t('events.all')) {
      count++;
    }
    if (isForKids) {
      count++;
    }
    return count;
  };

  const activeFilterCount = getActiveFilterCount();
  const hasActiveFilters = activeFilterCount > 0;

  // Sync with global input value
  useEffect(() => {
    setGlobalInputValue(inputValue);
  }, [inputValue, setGlobalInputValue]);

  const handleFilterPress = () => {
    haptics.light();
    onFilterPress?.();
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleDismiss = () => {
    setShowSuggestions(false);
    Keyboard.dismiss();
  };

  // Separate location and event suggestions
  const locationSuggestions = suggestions.filter((item: any) => item.type === 'location');
  const eventSuggestions = suggestions.filter((item: any) => item.type === 'event');

  return (
    <TouchableWithoutFeedback onPress={handleDismiss}>
      <View style={{paddingHorizontal: spacing.md, zIndex: 1000}}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: spacing.sm,
          }}>
          {/* Search Input Container */}
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: colors.surface,
              borderRadius: borderRadius.lg,
              borderWidth: 1,
              borderColor: isFocused ? colors.primary : colors.border,
              paddingHorizontal: spacing.sm,
              paddingVertical: spacing.xs,
              ...shadows.sm,
              height: 44, // Fixed compact height
            }}>
            {isLoading ? <ModernSpinner size={24} variant="circular" /> : <BigSearchIcon />}

            <TextInput
              style={{
                flex: 1,
                fontSize: typography.fontSize.sm,
                color: colors.textPrimary,
                marginLeft: spacing.xs,
                fontWeight: typography.fontWeight.medium,
                height: 40,
              }}
              placeholder="Search events or locations..."
              placeholderTextColor={colors.textSecondary}
              value={inputValue}
              onChangeText={setInputValue}
              onFocus={handleFocus}
              onBlur={handleBlur}
              returnKeyType="search"
              onSubmitEditing={handleDismiss}
            />
          </View>

          {/* Filter Button */}
          <Pressable
            onPress={handleFilterPress}
            style={({pressed}) => ({
              width: 44,
              height: 44,
              borderRadius: borderRadius.lg,
              backgroundColor: hasActiveFilters ? colors.statusOrange : pressed ? colors.border : colors.surface,
              borderWidth: 1,
              borderColor: hasActiveFilters ? colors.statusOrange : colors.border,
              alignItems: 'center',
              justifyContent: 'center',
              ...shadows.sm,
              transform: [{scale: pressed ? 0.95 : 1}],
            })}>
            <View style={{position: 'relative'}}>
              {/* Filter Icon - funnel icon */}
              <FunnelIcon color={hasActiveFilters ? colors.white : colors.textPrimary} size={20} />

              {/* Filter Count Badge */}
              {activeFilterCount > 0 && (
                <View
                  style={{
                    position: 'absolute',
                    top: -8,
                    right: -8,
                    backgroundColor: colors.error,
                    borderRadius: 10,
                    minWidth: 20,
                    height: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderWidth: 2,
                    borderColor: colors.background,
                  }}>
                  <Text
                    style={{
                      color: colors.white,
                      fontSize: 12,
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}>
                    {activeFilterCount}
                  </Text>
                </View>
              )}
            </View>
          </Pressable>
        </View>

        {/* Optimized Suggestions Dropdown */}
        {showSuggestions && (locationSuggestions.length > 0 || eventSuggestions.length > 0) && (
          <FadeIn duration={200}>
            <View
              style={{
                position: 'absolute',
                top: 52,
                left: 0,
                right: 0,
                backgroundColor: colors.surface,
                borderRadius: borderRadius.lg,
                borderWidth: 1,
                borderColor: colors.border,
                maxHeight: 300,
                ...shadows.lg,
                zIndex: 3000,
                marginHorizontal: spacing.md,
              }}>
              <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
                {/* Location Results */}
                {locationSuggestions.map((item: any, index: number) => (
                  <TouchableOpacity
                    key={`location-${index}`}
                    onPress={() => optimizedLocationSelect(item.place_id)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: spacing.md,
                      borderBottomWidth: index < locationSuggestions.length - 1 || eventSuggestions.length > 0 ? 1 : 0,
                      borderBottomColor: colors.border,
                    }}>
                    <LocationIcon />
                    <Text
                      style={{
                        flex: 1,
                        fontSize: typography.fontSize.sm,
                        color: colors.textPrimary,
                        fontWeight: typography.fontWeight.medium,
                        marginLeft: spacing.sm,
                      }}
                      numberOfLines={2}>
                      {item.description}
                    </Text>
                  </TouchableOpacity>
                ))}

                {/* Event Results */}
                {eventSuggestions.map((item: any, index: number) => (
                  <TouchableOpacity
                    key={`event-${index}`}
                    onPress={() => optimizedEventSelect(item)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: spacing.md,
                      borderBottomWidth: index < eventSuggestions.length - 1 ? 1 : 0,
                      borderBottomColor: colors.border,
                    }}>
                    <CalendarIcon color={colors.primary} />
                    <Text
                      style={{
                        flex: 1,
                        fontSize: typography.fontSize.sm,
                        color: colors.textPrimary,
                        fontWeight: typography.fontWeight.medium,
                        marginLeft: spacing.sm,
                      }}
                      numberOfLines={2}>
                      {item.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </FadeIn>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default OptimizedModernEventSearch;
