import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import BottomSheet from '@gorhom/bottom-sheet';
import StarRating from 'react-native-star-rating-widget';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useTheme} from '~contexts/ThemeContext';

const RatingSheet = ({bottomSheetRef, rate, onSubmit}: {bottomSheetRef: any; rate: number; onSubmit: () => void}) => {
  const {colors} = useTheme();
  const snapPoints = ['78%'];
  const [rating, setRating] = useState(rate);
  const [companyRating, setCompanyRating] = useState(0);
  const [answer, setAnswer] = useState('');

  useEffect(() => {
    setRating(rate);
  }, [rate]);

  const onSheetClose = () => {
    bottomSheetRef.current?.close();
  };

  const styles = StyleSheet.create({
    buttonText: {
      color: colors.white,
      fontWeight: '600',
      textAlign: 'center',
    },
    button: {
      backgroundColor: colors.secondary,
      paddingVertical: 10,
      width: '100%',
      borderRadius: 20,
      marginTop: 30,
    },
    answerInput: {
      borderWidth: 1,
      borderColor: colors.textSecondary,
      borderRadius: 5,
      padding: 12,
      paddingTop: 10,
      fontSize: 16,
      minHeight: 105,
      maxHeight: 105,
    },
    ratingBar: {
      left: -8,
      marginBottom: 6,
    },
    infoContainer: {
      flex: 1,
      marginHorizontal: 20,
    },
    questionTxt: {
      color: colors.textPrimary,
      fontSize: 19,
      marginTop: 15,
      fontWeight: '500',
      marginBottom: 6,
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginHorizontal: 20,
      marginTop: 12,
    },
    closeIcon: {
      paddingHorizontal: 10,
      paddingBottom: 5,
      alignSelf: 'flex-end',
    },
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    image: {
      width: '100%',
      height: 200,
      borderRadius: 12,
      marginBottom: 12,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    address: {
      fontSize: 14,
      color: colors.gray400,
      marginBottom: 8,
    },
    description: {
      fontSize: 14,
    },
  });

  return (
    <BottomSheet
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      index={-1}
      handleIndicatorStyle={{width: 70}}
      handleStyle={{
        backgroundColor: colors.background,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
      }}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
      }}
      onClose={onSheetClose}
      enablePanDownToClose={false}>
      <KeyboardAwareScrollView
        enableOnAndroid={true}
        contentContainerStyle={styles.container}
        keyboardShouldPersistTaps="handled">
        <View style={styles.divider} />
        <View style={styles.infoContainer}>
          <Text style={styles.questionTxt}>Rate the event</Text>
          <StarRating
            style={styles.ratingBar}
            rating={rating}
            onChange={setRating}
            color={colors.eventInfluencer}
            emptyColor={colors.gray400}
            starSize={24}
          />
          <Text style={styles.questionTxt}>Rate your companions</Text>
          <StarRating
            style={styles.ratingBar}
            rating={companyRating}
            onChange={setCompanyRating}
            color={colors.eventInfluencer}
            emptyColor={colors.gray400}
            starSize={24}
          />
          <Text style={styles.questionTxt}>Tell us anything else in your mind:</Text>
          <TextInput
            placeholder="Enter your answer"
            style={styles.answerInput}
            multiline
            onChangeText={setAnswer}
            value={answer}
            placeholderTextColor={colors.statusGray}
          />
          <TouchableOpacity onPress={onSubmit} style={styles.button}>
            <Text style={styles.buttonText}>Submit</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </BottomSheet>
  );
};

export default RatingSheet;
