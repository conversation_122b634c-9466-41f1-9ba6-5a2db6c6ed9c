import {Circle, Path, Svg} from 'react-native-svg';

const SendArrow = ({isActive = false}: {isActive?: boolean}) => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="12" fill={isActive ? '#FF9500' : '#AEAEB2'} />
    <Path d="M12.1161 18.2227V6.22266" stroke="white" strokeWidth="1.75" strokeLinecap="round" />
    <Path d="M16.8033 11.3437L12.1161 6.22238" stroke="white" strokeWidth="1.75" strokeLinecap="round" />
    <Path d="M7.22738 11.3439L12.1161 6.22257" stroke="white" strokeWidth="1.75" strokeLinecap="round" />
  </Svg>
);

export default SendArrow;
