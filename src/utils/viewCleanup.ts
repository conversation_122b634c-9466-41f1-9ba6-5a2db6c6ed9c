import {useEffect, useRef} from 'react';
import {cancelAnimation, SharedValue} from 'react-native-reanimated';

/**
 * Hook to safely cleanup animated values when component unmounts
 * Prevents view management errors by canceling ongoing animations
 */
export const useAnimationCleanup = (...animatedValues: SharedValue<any>[]) => {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;

      // Cancel all animations safely with a small delay to ensure proper cleanup
      setTimeout(() => {
        animatedValues.forEach(value => {
          try {
            if (value && typeof value.value !== 'undefined') {
              cancelAnimation(value);
            }
          } catch (error) {
            // Silently handle cleanup errors to prevent crashes
            if (__DEV__) {
              console.warn('Animation cleanup warning:', error);
            }
          }
        });
      }, 0);
    };
  }, animatedValues);

  return isMountedRef;
};

/**
 * Safely execute a function only if component is still mounted
 */
export const safeRunOnJS = (isMountedRef: React.RefObject<boolean>, fn: () => void) => {
  return () => {
    if (isMountedRef.current) {
      try {
        fn();
      } catch (error) {
        if (__DEV__) {
          console.error('Safe runOnJS error:', error);
        }
      }
    }
  };
};

/**
 * Safe wrapper for view updates that might cause hierarchy errors
 */
export const safeViewUpdate = (isMountedRef: React.RefObject<boolean>, updateFn: () => void, errorContext?: string) => {
  if (!isViewOperationSafe(isMountedRef)) {
    if (__DEV__ && errorContext) {
      console.warn(`Skipping view update for unmounted component: ${errorContext}`);
    }
    return;
  }

  try {
    updateFn();
  } catch (error) {
    if (__DEV__) {
      console.warn(`View update error${errorContext ? ` in ${errorContext}` : ''}:`, error);
    }
  }
};

/**
 * Utility to check if a view operation is safe to perform
 */
export const isViewOperationSafe = (isMountedRef: React.RefObject<boolean>): boolean => {
  return isMountedRef.current === true;
};

/**
 * Enhanced error logging for view-related errors
 */
export const logViewError = (context: string, error: any) => {
  if (__DEV__) {
    console.error(`[ViewError] ${context}:`, error);
  }

  // In production, you might want to send this to your error reporting service
  // Example: Crashlytics.recordError(error);
};

/**
 * Debounced function to prevent rapid successive calls that might cause view errors
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttled function to limit the frequency of function calls
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
