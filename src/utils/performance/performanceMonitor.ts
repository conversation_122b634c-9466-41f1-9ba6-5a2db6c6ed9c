import {QueryClient} from 'react-query';

// Performance metrics tracking
interface PerformanceMetrics {
  searchLatency: number[];
  cacheHitRate: number;
  requestCount: number;
  errorRate: number;
  prefetchSuccess: number;
  backgroundRefreshCount: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    searchLatency: [],
    cacheHitRate: 0,
    requestCount: 0,
    errorRate: 0,
    prefetchSuccess: 0,
    backgroundRefreshCount: 0,
  };

  private cacheHits = 0;
  private cacheMisses = 0;
  private errors = 0;
  private startTimes = new Map<string, number>();

  // Track search performance
  startSearch(searchId: string) {
    this.startTimes.set(searchId, Date.now());
  }

  endSearch(searchId: string, fromCache = false) {
    const startTime = this.startTimes.get(searchId);
    if (startTime) {
      const latency = Date.now() - startTime;
      this.metrics.searchLatency.push(latency);

      // Keep only last 100 measurements
      if (this.metrics.searchLatency.length > 100) {
        this.metrics.searchLatency.shift();
      }

      this.startTimes.delete(searchId);

      if (fromCache) {
        this.cacheHits++;
      } else {
        this.cacheMisses++;
      }

      this.updateCacheHitRate();
    }
  }

  // Track cache performance
  recordCacheHit() {
    this.cacheHits++;
    this.updateCacheHitRate();
  }

  recordCacheMiss() {
    this.cacheMisses++;
    this.updateCacheHitRate();
  }

  private updateCacheHitRate() {
    const total = this.cacheHits + this.cacheMisses;
    this.metrics.cacheHitRate = total > 0 ? (this.cacheHits / total) * 100 : 0;
  }

  // Track request counts
  recordRequest() {
    this.metrics.requestCount++;
  }

  recordError() {
    this.errors++;
    this.metrics.errorRate = (this.errors / this.metrics.requestCount) * 100;
  }

  recordPrefetchSuccess() {
    this.metrics.prefetchSuccess++;
  }

  recordBackgroundRefresh() {
    this.metrics.backgroundRefreshCount++;
  }

  // Get performance statistics
  getMetrics() {
    const latencies = this.metrics.searchLatency;
    return {
      ...this.metrics,
      averageSearchLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
      p95SearchLatency: latencies.length > 0 ? this.percentile(latencies, 95) : 0,
      p99SearchLatency: latencies.length > 0 ? this.percentile(latencies, 99) : 0,
      totalCacheOperations: this.cacheHits + this.cacheMisses,
    };
  }

  private percentile(arr: number[], p: number): number {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  // Reset metrics
  reset() {
    this.metrics = {
      searchLatency: [],
      cacheHitRate: 0,
      requestCount: 0,
      errorRate: 0,
      prefetchSuccess: 0,
      backgroundRefreshCount: 0,
    };
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.errors = 0;
    this.startTimes.clear();
  }

  // Log performance summary
  logSummary() {
    const metrics = this.getMetrics();
    console.log('🚀 Performance Summary:', {
      'Average Search Latency': `${metrics.averageSearchLatency.toFixed(2)}ms`,
      'P95 Search Latency': `${metrics.p95SearchLatency.toFixed(2)}ms`,
      'Cache Hit Rate': `${metrics.cacheHitRate.toFixed(1)}%`,
      'Total Requests': metrics.requestCount,
      'Error Rate': `${metrics.errorRate.toFixed(1)}%`,
      'Prefetch Success': metrics.prefetchSuccess,
      'Background Refreshes': metrics.backgroundRefreshCount,
    });
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React Query performance plugin
export const createPerformancePlugin = () => {
  return {
    onSuccess: (data: any, query: any) => {
      performanceMonitor.recordRequest();

      // Check if data came from cache
      if (query.state.dataUpdatedAt === query.state.dataFetchedAt) {
        performanceMonitor.recordCacheHit();
      } else {
        performanceMonitor.recordCacheMiss();
      }
    },
    onError: (error: any, query: any) => {
      performanceMonitor.recordRequest();
      performanceMonitor.recordError();
    },
  };
};

// Hook to monitor query performance
export const usePerformanceTracking = (queryKey: string) => {
  const trackStart = () => {
    performanceMonitor.startSearch(queryKey);
  };

  const trackEnd = (fromCache = false) => {
    performanceMonitor.endSearch(queryKey, fromCache);
  };

  return {trackStart, trackEnd};
};

// Background task to log performance periodically
let performanceLogInterval: NodeJS.Timeout;

export const startPerformanceLogging = (intervalMs = 60000) => {
  if (performanceLogInterval) {
    clearInterval(performanceLogInterval);
  }

  performanceLogInterval = setInterval(() => {
    performanceMonitor.logSummary();
  }, intervalMs);
};

export const stopPerformanceLogging = () => {
  if (performanceLogInterval) {
    clearInterval(performanceLogInterval);
  }
};

// Memory usage monitoring
export const getMemoryUsage = () => {
  if (global.performance && global.performance.memory) {
    return {
      usedJSHeapSize: global.performance.memory.usedJSHeapSize,
      totalJSHeapSize: global.performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: global.performance.memory.jsHeapSizeLimit,
    };
  }
  return null;
};

// Network performance tracking
export const trackNetworkRequest = async <T>(requestName: string, requestFn: () => Promise<T>): Promise<T> => {
  const startTime = Date.now();
  performanceMonitor.recordRequest();

  try {
    const result = await requestFn();
    const endTime = Date.now();

    console.log(`📡 ${requestName} completed in ${endTime - startTime}ms`);
    return result;
  } catch (error) {
    performanceMonitor.recordError();
    const endTime = Date.now();
    console.error(`❌ ${requestName} failed after ${endTime - startTime}ms:`, error);
    throw error;
  }
};
