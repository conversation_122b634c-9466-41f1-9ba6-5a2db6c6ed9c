import {useMutation} from 'react-query';
import Config from 'react-native-config';
import FirebaseAuth from '~services/FirebaseAuthService';

type Answer = {
  question: string;
  answer: string;
};

type SubmitOnboardingAnswersRequest = {
  answers: Answer[];
};

export function useSubmitOnboardingAnswers(userId: string) {
  return useMutation<void, Error, SubmitOnboardingAnswersRequest>(async data => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${Config.BASE_API_URL}users/${userId}/onboarding-answers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to submit answers');
    }

    return response.json();
  });
}
