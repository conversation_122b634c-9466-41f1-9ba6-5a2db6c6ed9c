import {StyleSheet} from 'react-native';
const getStyles = (colors: any) =>
  StyleSheet.create({
    flatList: {
      paddingHorizontal: 16,
      marginTop: 16,
    },
    fleaListContentContainerStyle: {
      gap: 12,
    },
    attendeesText: {
      color: colors.black,
      fontWeight: '700',
      fontSize: 19,
      lineHeight: 22,
      paddingHorizontal: 16,
    },
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    leftContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
      flex: 1,
    },
    image: {
      height: 40,
      width: 40,
      borderRadius: 12,
      overflow: 'hidden',
    },
    name: {
      fontSize: 18,
      fontWeight: '600',
      flex: 1,
    },
    buttonContainer: {
      backgroundColor: colors.statusPurple,
      borderRadius: 50,
    },
    buttonRejectContainer: {
      backgroundColor: colors.error,
      borderRadius: 50,
    },
    buttonText: {
      fontSize: 15,
      lineHeight: 16,
      fontWeight: '500',
      color: colors.white,
    },
    buttonsContainer: {
      flexDirection: 'row',
      gap: 6,
      paddingLeft: 10,
    },
  });

export default getStyles;
