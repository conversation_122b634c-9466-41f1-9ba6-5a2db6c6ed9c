import React, {useState, useEffect} from 'react';
import {View, Text, ScrollView, TouchableOpacity, useWindowDimensions, Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {Formik} from 'formik';
import * as yup from 'yup';
import {PyxiLabel} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernCard from '~components/ModernCard/ModernCard';
import SocialButtons from '~components/SocialButtons';
import {FadeIn, SlideIn} from '~components/MicroInteractions/MicroInteractions';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {LanguagesModal} from '~components/ModalWithItems/LanguagesModal';
import {useLanguage} from '~containers/Core/navigation/SettingsStack/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';

const validationSchema = yup.object().shape({
  email: yup.string().email('Please enter a valid email address').required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
    .matches(/[0-9]/, 'Password must contain at least one number')
    .required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
});

const ModernSignUpView: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();
  const isFocused = useIsFocused();
  const {language} = useLanguage();

  const [isLoading, setIsLoading] = useState(false);
  const [isLanguagesModalVisible, setIsLanguagesModalVisible] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('ModernSignUp', 'ModernSignUpView');
  }, []);

  const calculatePasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    return strength;
  };

  const handleSubmit = async (values: any) => {
    setIsLoading(true);
    try {
      await FirebaseAuth.signUp(values.email.trim(), values.password, values.confirmPassword, setIsLoading);
    } catch (error) {
      console.error('Signup error:', error);
      Alert.alert('Signup Failed', 'Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getPasswordStrengthColor = (strength: number): string => {
    if (strength <= 1) return colors.error;
    if (strength <= 2) return colors.warning;
    if (strength <= 3) return colors.warningLight;
    return colors.success;
  };

  const getPasswordStrengthText = (strength: number): string => {
    if (strength <= 1) return 'Weak';
    if (strength <= 2) return 'Fair';
    if (strength <= 3) return 'Good';
    return 'Strong';
  };

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      minHeight: height,
    },
    heroSection: {
      height: height * 0.35,
      position: 'relative' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      overflow: 'hidden' as const,
    },
    heroGradient: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    heroContent: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 2,
      paddingHorizontal: spacing.lg,
    },
    languageButton: {
      position: 'absolute' as const,
      top: spacing.md,
      right: spacing.md,
      backgroundColor: colors.surface,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.full,
      borderWidth: 1,
      borderColor: colors.border,
      zIndex: 3,
      minWidth: 44,
      height: 32,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...shadows.sm,
    },
    languageText: {
      color: colors.textSecondary,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      letterSpacing: 0.3,
      opacity: 0.9,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: spacing.lg,
      transform: [{scale: 1.1}],
    },
    taglineText: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textInverse,
      textAlign: 'center' as const,
      marginTop: spacing.md,
      opacity: 0.95,
      letterSpacing: 1,
    },
    formSection: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.xl,
    },
    formCard: {
      marginBottom: spacing.lg,
    },
    formTitle: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      marginBottom: spacing.sm,
      letterSpacing: -0.3,
    },
    formSubtitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      textAlign: 'center' as const,
      marginBottom: spacing.lg,
      opacity: 0.85,
      letterSpacing: 0.1,
      lineHeight: typography.fontSize.base * 1.4,
    },
    inputContainer: {
      marginBottom: spacing.md,
    },
    passwordStrengthContainer: {
      marginTop: spacing.xs,
      marginBottom: spacing.sm,
    },
    passwordStrengthBar: {
      height: 4,
      backgroundColor: colors.border,
      borderRadius: borderRadius.full,
      overflow: 'hidden' as const,
    },
    passwordStrengthFill: {
      height: 4,
      borderRadius: borderRadius.full,
    },
    passwordStrengthText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium,
      marginTop: spacing.xs,
      textAlign: 'right' as const,
    },
    dividerContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginVertical: spacing.lg,
    },
    divider: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginHorizontal: spacing.md,
      fontWeight: typography.fontWeight.medium,
    },
    bottomSection: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.xl,
      alignItems: 'center' as const,
    },
    switchContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginTop: spacing.lg,
    },
    switchText: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      fontWeight: typography.fontWeight.normal,
    },
    switchButton: {
      marginLeft: spacing.xs,
    },
    switchButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.primary,
    },
    decorativeCircle1: {
      position: 'absolute' as const,
      top: height * 0.2,
      right: height * 0.15,
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.textInverse + '10',
      opacity: 0.6,
    },
    decorativeCircle2: {
      position: 'absolute' as const,
      bottom: height * 0.3,
      left: height * 0.1,
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.textInverse + '15',
      opacity: 0.4,
    },
    decorativeCircle3: {
      position: 'absolute' as const,
      top: height * 0.6,
      right: height * 0.25,
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.textInverse + '20',
      opacity: 0.5,
    },
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Language Selection Button */}
      <TouchableOpacity style={styles.languageButton} onPress={() => setIsLanguagesModalVisible(true)}>
        <Text style={styles.languageText}>{language.toUpperCase()}</Text>
      </TouchableOpacity>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {/* Form Section */}
        <View style={styles.formSection}>
          <FadeIn delay={50} duration={300}>
            <ModernCard variant="default" padding="xl" style={{...styles.formCard, ...shadows.none}}>
              <Text style={styles.formTitle}>Join Pyxi</Text>
              <Text style={styles.formSubtitle}>Create your account to discover amazing events</Text>

              <Formik
                initialValues={{email: '', password: '', confirmPassword: ''}}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
                validateOnChange={true}
                validateOnBlur={true}>
                {formikProps => (
                  <>
                    <View style={styles.inputContainer}>
                      <ModernTextInput
                        label={t('signin.email')}
                        placeholder="Enter your email address"
                        value={formikProps.values.email}
                        onChangeText={formikProps.handleChange('email')}
                        errorText={formikProps.touched.email && formikProps.errors.email}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <View style={styles.inputContainer}>
                      <ModernTextInput
                        label={t('signin.password')}
                        placeholder="Create a strong password"
                        value={formikProps.values.password}
                        onChangeText={value => {
                          formikProps.handleChange('password')(value);
                          setPasswordStrength(calculatePasswordStrength(value));
                        }}
                        errorText={formikProps.touched.password && formikProps.errors.password}
                        isPassword
                        variant="outlined"
                        size="lg"
                      />
                      {formikProps.values.password.length > 0 && (
                        <View style={styles.passwordStrengthContainer}>
                          <View style={styles.passwordStrengthBar}>
                            <View
                              style={[
                                styles.passwordStrengthFill,
                                {
                                  width: `${(passwordStrength / 5) * 100}%`,
                                  backgroundColor: getPasswordStrengthColor(passwordStrength),
                                },
                              ]}
                            />
                          </View>
                          <Text
                            style={[styles.passwordStrengthText, {color: getPasswordStrengthColor(passwordStrength)}]}>
                            {getPasswordStrengthText(passwordStrength)}
                          </Text>
                        </View>
                      )}
                    </View>

                    <View style={styles.inputContainer}>
                      <ModernTextInput
                        label={t('signin.confirm_password')}
                        placeholder="Confirm your password"
                        value={formikProps.values.confirmPassword}
                        onChangeText={formikProps.handleChange('confirmPassword')}
                        errorText={formikProps.touched.confirmPassword && formikProps.errors.confirmPassword}
                        isPassword
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <ModernButton
                      onPress={formikProps.handleSubmit}
                      title="Create Account"
                      variant="primary"
                      size="lg"
                      fullWidth
                      loading={isLoading}
                      hapticFeedback
                    />

                    <View style={styles.dividerContainer}>
                      <View style={styles.divider} />
                      <Text style={styles.dividerText}>or continue with</Text>
                      <View style={styles.divider} />
                    </View>

                    <SocialButtons />
                  </>
                )}
              </Formik>
            </ModernCard>
          </FadeIn>
        </View>

        {/* Bottom Section */}
        <FadeIn delay={100} duration={300}>
          <View style={styles.bottomSection}>
            <View style={styles.switchContainer}>
              <Text style={styles.switchText}>Already have an account?</Text>
              <TouchableOpacity
                style={styles.switchButton}
                onPress={() => {
                  navigation.navigate(SCREENS.LOGIN);
                }}>
                <Text style={styles.switchButtonText}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </FadeIn>
      </ScrollView>

      <LanguagesModal isVisible={isLanguagesModalVisible} onClose={() => setIsLanguagesModalVisible(false)} />
    </SafeAreaView>
  );
};

export default ModernSignUpView;
