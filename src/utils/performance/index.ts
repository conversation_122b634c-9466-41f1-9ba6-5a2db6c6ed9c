// Performance optimization utilities
export * from './queryConfig';
export * from './searchOptimization';
export * from './performanceMonitor';
export * from './advancedOptimizations';
export * from './networkOptimization';
export * from './bundleOptimization';
export * from './lazyLoading';

// Re-export commonly used items for convenience
export {createOptimizedQueryClient, queryKeys, invalidateQueries, prefetchHelpers} from './queryConfig';

export {
  useSmartDebounce,
  searchCache,
  requestDeduplicator,
  useOptimisticSearch,
  usePredictivePrefetch,
} from './searchOptimization';

export {
  performanceMonitor,
  createPerformancePlugin,
  usePerformanceTracking,
  startPerformanceLogging,
  stopPerformanceLogging,
  getMemoryUsage,
  trackNetworkRequest,
} from './performanceMonitor';

export {
  parallelAPIManager,
  createProgressiveImageLoader,
  memoryManager,
  deferHeavyOperation,
  processInChunks,
  createDynamicSizeCalculator,
  requestQueue,
  RequestPriority,
} from './advancedOptimizations';

export {networkManager, useNetworkRequest, offlineSync} from './networkOptimization';

export {
  dynamicImport,
  createPlatformSplit,
  createFeatureSplit,
  routeSplitter,
  dependencyManager,
  bundleAnalyzer,
  createTreeShakableImport,
  createCapabilityBasedImport,
  moduleFederation,
} from './bundleOptimization';

export {
  createLazyComponent,
  createLazyScreen,
  preloadComponentGroup,
  intelligentPreloader,
  LazyComponent,
  useLazyImport,
  preloadAllRegistered,
  getRegistryStats,
} from './lazyLoading';
