import React, {useRef, useEffect} from 'react';
import {StyleSheet, Text, TextInput, TouchableOpacity, View, ViewStyle, Keyboard} from 'react-native';
import {BigSearchIcon, ItemFlagIcon} from '../../assets/icons';
import {useTheme} from '~contexts/ThemeContext';

type SearchBarProps = {
  value: string;
  style?: ViewStyle | ViewStyle[];
  onChangeText: (text: string) => void;
  predictions: any[];
  showPredictions: boolean;
  onPredictionTapped: (placeId: string, description: string) => void;
};

const SearchBarWithAutocomplete = ({
  value,
  style,
  onChangeText,
  onPredictionTapped,
  predictions,
  showPredictions,
}: SearchBarProps) => {
  const {colors} = useTheme();

  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
    },
    inputStyle: {
      paddingVertical: 10,
      paddingHorizontal: 16,
      backgroundColor: colors.gray100,
      borderRadius: 10,
      color: colors.black,
      fontSize: 16,
    },
    predictionsContainer: {
      backgroundColor: colors.border,
      padding: 10,
      borderBottomLeftRadius: 10,
      borderBottomRightRadius: 10,
    },
    predictionRow: {
      paddingBottom: 8,
      marginBottom: 10,
      borderBottomColor: colors.gray100 + 'CC',
      borderBottomWidth: 1,
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
  });

  const {container, inputStyle} = styles;
  const passedStyles = Array.isArray(style) ? Object.assign({}, ...style) : style;
  const textInputRef = useRef<TextInput>(null);

  const handlePredictionTapped = (placeId: string, description: string) => {
    // Dismiss keyboard when prediction is tapped
    Keyboard.dismiss();
    textInputRef.current?.blur();
    onPredictionTapped(placeId, description);
  };

  // Dismiss keyboard when component unmounts or when search is cleared
  useEffect(() => {
    return () => {
      Keyboard.dismiss();
    };
  }, []);

  // Dismiss keyboard when value is cleared
  useEffect(() => {
    if (!value) {
      Keyboard.dismiss();
      textInputRef.current?.blur();
    }
  }, [value]);

  const _renderPredictions = (predictions: any[]) => {
    const {predictionRow} = styles;

    return (
      <>
        <View style={{marginBottom: 20}} />
        {predictions.map((item, _index) => (
          <TouchableOpacity
            key={item.place_id}
            style={predictionRow}
            onPress={() => {
              handlePredictionTapped(item.place_id, item.description);
            }}>
            <ItemFlagIcon />
            <View style={{marginLeft: 8, paddingVertical: 8}}>
              <Text numberOfLines={1}>{item.description}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </>
    );
  };

  return (
    <View style={[container, {...passedStyles}]}>
      <TextInput
        ref={textInputRef}
        style={[inputStyle]}
        placeholder="Search by address"
        placeholderTextColor={colors.gray400}
        value={value}
        onChangeText={onChangeText}
        returnKeyType="search"
        onSubmitEditing={() => {
          Keyboard.dismiss();
          textInputRef.current?.blur();
        }}
      />
      {showPredictions && value.length ? (
        _renderPredictions(predictions)
      ) : (
        <View
          style={{
            width: '100%',
            marginTop: 15,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <BigSearchIcon />
          <Text
            style={{
              textAlign: 'center',
              fontWeight: '500',
              fontSize: 14,
              color: colors.statusGray,
              marginTop: 10,
            }}>
            Start searching to set up {'\n'}a location
          </Text>
        </View>
      )}
    </View>
  );
};

export default SearchBarWithAutocomplete;
