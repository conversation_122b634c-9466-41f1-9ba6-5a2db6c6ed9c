import {useState, useEffect} from 'react';
import {performanceMonitor} from '~utils/performance/performanceMonitor';
import {searchCache} from '~utils/performance/searchOptimization';

/**
 * Demo hook to showcase performance improvements
 * Use this in development to see the impact of optimizations
 */
export const usePerformanceDemo = () => {
  const [metrics, setMetrics] = useState({
    averageSearchLatency: 0,
    cacheHitRate: 0,
    totalRequests: 0,
    cacheSize: 0,
    errorRate: 0,
  });

  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (!isMonitoring) {
      return;
    }

    const interval = setInterval(() => {
      const currentMetrics = performanceMonitor.getMetrics();
      setMetrics({
        averageSearchLatency: Math.round(currentMetrics.averageSearchLatency),
        cacheHitRate: Math.round(currentMetrics.cacheHitRate * 10) / 10,
        totalRequests: currentMetrics.requestCount,
        cacheSize: searchCache.size(),
        errorRate: Math.round(currentMetrics.errorRate * 10) / 10,
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isMonitoring]);

  const startMonitoring = () => {
    setIsMonitoring(true);
    performanceMonitor.reset();
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
  };

  const logSummary = () => {
    performanceMonitor.logSummary();
  };

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    logSummary,
  };
};

/**
 * Performance comparison utility
 * Compare old vs new implementation performance
 */
export const usePerformanceComparison = () => {
  const [oldMetrics, setOldMetrics] = useState<any>(null);
  const [newMetrics, setNewMetrics] = useState<any>(null);

  const recordOldImplementation = () => {
    const metrics = performanceMonitor.getMetrics();
    setOldMetrics({
      ...metrics,
      timestamp: Date.now(),
      label: 'Old Implementation',
    });
    performanceMonitor.reset();
  };

  const recordNewImplementation = () => {
    const metrics = performanceMonitor.getMetrics();
    setNewMetrics({
      ...metrics,
      timestamp: Date.now(),
      label: 'New Implementation',
    });
  };

  const getComparison = () => {
    if (!oldMetrics || !newMetrics) {
      return null;
    }

    const latencyImprovement =
      oldMetrics.averageSearchLatency > 0
        ? ((oldMetrics.averageSearchLatency - newMetrics.averageSearchLatency) / oldMetrics.averageSearchLatency) * 100
        : 0;

    const cacheImprovement = newMetrics.cacheHitRate - oldMetrics.cacheHitRate;

    return {
      latencyImprovement: Math.round(latencyImprovement * 10) / 10,
      cacheImprovement: Math.round(cacheImprovement * 10) / 10,
      requestReduction: oldMetrics.requestCount - newMetrics.requestCount,
      oldMetrics,
      newMetrics,
    };
  };

  const logComparison = () => {
    const comparison = getComparison();
    if (comparison) {
      console.log('📊 Performance Comparison:', {
        'Latency Improvement': `${comparison.latencyImprovement}%`,
        'Cache Hit Rate Improvement': `+${comparison.cacheImprovement}%`,
        'Request Reduction': comparison.requestReduction,
        'Old Average Latency': `${Math.round(comparison.oldMetrics.averageSearchLatency)}ms`,
        'New Average Latency': `${Math.round(comparison.newMetrics.averageSearchLatency)}ms`,
      });
    }
  };

  return {
    oldMetrics,
    newMetrics,
    recordOldImplementation,
    recordNewImplementation,
    getComparison,
    logComparison,
  };
};
