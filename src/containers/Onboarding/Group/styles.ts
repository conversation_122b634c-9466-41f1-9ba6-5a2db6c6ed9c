import {StyleSheet} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors';
const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 16,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    box: {
      flex: 1,
      borderRadius: 6,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 6,
      backgroundColor: colors.surface,
      aspectRatio: 1,
    },

    text: {
      textAlignVertical: 'bottom',
      fontSize: 15,
      fontWeight: '500',
      color: colors.textPrimary,
    },
    bottomButtonWrapper: {width: '100%', flex: 1, justifyContent: 'flex-end'},
    bottomButton: {
      width: '100%',
      height: 40,
      borderRadius: 6,
      // backgroundColor: !selectedItems.length ? colors.gray100 : colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomButtonText: {
      fontSize: 15,
      fontWeight: '500',
    },
  });

export default createStyles;
