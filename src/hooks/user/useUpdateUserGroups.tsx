import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import Config from 'react-native-config';
import auth from '@react-native-firebase/auth';

export function useUpdateUserGroups() {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (userData: {ids: number[]}) => {
      const userId = auth().currentUser!.uid;

      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${Config.BASE_API_URL}users/${userId}/groups`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify({...userData}),
      });
      if (!response.ok) {
        throw new Error('Failed to update user groups');
      }

      return response.json() as Promise<string>;
    },
    {onSuccess: () => queryClient.refetchQueries(['userAccount'])},
  );

  return mutation;
}
