import {StyleSheet} from 'react-native';
const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      flexDirection: 'row',
      paddingLeft: 12,
      marginTop: 12,
      paddingVertical: 8,
      borderRadius: 10,
      backgroundColor: colors.background,
    },
    title: {
      fontSize: 14,
      fontWeight: '400',
      marginBottom: 4,
    },
    rightContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginLeft: 12,
      paddingRight: 12,
      height: '100%',
    },
    location: {
      fontSize: 13,
      fontWeight: '400',
      lineHeight: 15,
      color: colors.error,
    },
    pinImageSize: {
      width: 22,
      height: 22,
    },
  });

export default createStyles;
