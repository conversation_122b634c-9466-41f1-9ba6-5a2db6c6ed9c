import React from 'react';
import {Modal, View, Text, TouchableOpacity, StyleSheet, Linking} from 'react-native';
import {useTranslation} from 'react-i18next';
import {ModernCloseIcon} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';

interface UpdateAlertProps {
  visible: boolean;
  onDismiss: () => void;
  storeUrl: string;
}

const UpdateAlert: React.FC<UpdateAlertProps> = ({visible, onDismiss, storeUrl}) => {
  const {t} = useTranslation();
  const {colors} = useTheme();

  const handleGoToStore = () => {
    Linking.openURL(storeUrl);
    onDismiss();
  };

  const styles = getStyles(colors);

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onDismiss}>
      <View style={styles.overlay}>
        <View style={styles.alertContainer}>
          {/* Modern close button at top right */}
          <TouchableOpacity style={styles.crossButton} onPress={onDismiss}>
            <ModernCloseIcon size={20} color={colors.gray600} variant="minimal" />
          </TouchableOpacity>

          {/* Alert content */}
          <View style={styles.content}>
            <Text style={styles.title}>{t('settings.update_your_Pyxi')}</Text>

            <TouchableOpacity style={styles.storeButton} onPress={handleGoToStore}>
              <Text style={styles.storeButtonText}>Go to store</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const getStyles = (colors: any) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: colors.overlayBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    alertContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 20,
      margin: 20,
      minWidth: 280,
      maxWidth: 320,
      position: 'relative',
    },
    crossButton: {
      position: 'absolute',
      top: 12,
      right: 12,
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.gray100,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    content: {
      paddingTop: 20,
      alignItems: 'center',
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 20,
      color: colors.textPrimary,
    },
    storeButton: {
      backgroundColor: colors.eventInfluencer,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      minWidth: 120,
    },
    storeButtonText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: '600',
      textAlign: 'center',
    },
  });

export default UpdateAlert;
