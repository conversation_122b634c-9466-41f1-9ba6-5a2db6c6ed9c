import {FC} from 'react';
import {useTranslation} from 'react-i18next';
import {Text, TouchableOpacity, View} from 'react-native';
import Animated, {StretchInX} from 'react-native-reanimated';
import {randomizedColors} from '~Utils/Color';
import {AcceptIcon, PlusIcon} from '~assets/icons';
import {SubCategoryType} from '~types/categories';
import {createStyles} from '../styles';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  item: SubCategoryType;
  selectedSubCats: number[];
  handlePress: (value: number) => void;
  categoryIndex: number;
  itemsLength: number;
}

const RenderItem: FC<IProps> = ({item, selectedSubCats, handlePress, categoryIndex}) => {
  const isChosen = selectedSubCats.includes(item.subcategory_id);
  const {t} = useTranslation();
  const {colors} = useTheme();
  const styles = createStyles(colors);

  const subcategoryName =
    t(`subcategories.${item.subcategory_name}`) === `subcategories.${item.subcategory_name}`
      ? item?.subcategory_repr
      : t(`subcategories.${item.subcategory_name}`);

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          handlePress(item.subcategory_id);
        }}
        style={[
          styles.itemContainer,
          {
            backgroundColor: !isChosen ? colors.textSecondary + '33' : colors.textSecondary,
          },
        ]}>
        {isChosen ? <AcceptIcon /> : <PlusIcon color={colors.textSecondary} />}

        <Text style={{color: isChosen ? colors.white : colors.textSecondary, marginLeft: 4}}>{subcategoryName}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default RenderItem;
