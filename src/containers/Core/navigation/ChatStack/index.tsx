import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {useEffect} from 'react';
import {SCREENS} from '~constants';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {chatScreens} from '../screens';
import firestore from '@react-native-firebase/firestore';
import {ChatTypeWithKey} from '~types/chat';
import auth from '@react-native-firebase/auth';

const Stack = createNativeStackNavigator();

export const ChatStack = () => {
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.CHAT_STACK>>();
  const navigation = useNavigation<NavigationProps>();
  const uid = auth().currentUser?.uid;

  useEffect(() => {
    if (!params?.key) {
      return;
    }

    if (params?.chatType === 'AdminChat') {
      setTimeout(() => {
        navigation.navigate(SCREENS.CONCIERGE_CHAT);
      }, 400);
      return;
    }

    const chatSubscribe = firestore()
      .collection('chats')
      .doc(params?.key)
      .onSnapshot(documentSnapshot => {
        const data = documentSnapshot.data() as ChatTypeWithKey | undefined;
        if (data) {
          const interlocutorId = data.userIds.find(userId => userId !== uid);
          const interlocutorMessage = data.history.find(message => message.sender_id === interlocutorId);
          const interlocutorImage = interlocutorMessage?.sender_image;

          const image = data.eventImage || interlocutorImage;
          const userIndex = data.userIds.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase());
          const userName = data.users[userIndex];
          setTimeout(() => {
            navigation.navigate(SCREENS.USER_CHAT, {
              chatId: params.key,
              image: image,
              userName: userName,
            });
          }, 400);
        }
      });

    return () => {
      chatSubscribe();
    };
  }, [params?.key, params?.chatType, navigation]);

  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      {chatScreens.map(screen => (
        <Stack.Screen name={screen.name} key={screen.name} component={screen.component} options={screen.options} />
      ))}
    </Stack.Navigator>
  );
};
