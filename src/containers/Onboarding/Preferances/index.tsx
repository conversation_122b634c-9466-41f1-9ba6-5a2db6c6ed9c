import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ModernHeader} from '~components/ModernHeader';
import {SCREENS} from '~constants';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useSubmitOnboardingAnswers} from '~hooks/user/useSubmitOnboardingAnswers';
import auth from '@react-native-firebase/auth';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useUserStore} from '~providers/userStore/zustand';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import {spacing, typography} from '~constants/design';
import Animated, {FadeInDown} from 'react-native-reanimated';

type NestedOption = {
  label: string;
  subOptions?: string[];
  allowTextInput?: boolean;
};

type Question = {
  id: number;
  question: string;
  options: (string | NestedOption)[];
  selectedAnswer?: string;
  customText?: string;
};

type Answer = {
  question: string;
  answer: string;
};

const getInitialQuestions = (t: any): Question[] => [
  {
    id: 1,
    question: t('preferences.question_1') || 'Do you consider yourself an introvert, an extrovert or a bit of both?',
    options: [
      t('preferences.introvert') || 'Introvert',
      t('preferences.extrovert') || 'Extrovert',
      t('preferences.bit_of_both') || 'A bit of both',
    ],
  },
  {
    id: 2,
    question: t('preferences.question_2') || 'Do you have any strong religious views?',
    options: [
      {
        label: t('preferences.yes') || 'Yes',
        subOptions: [
          t('preferences.christian') || 'Christian',
          t('preferences.muslim') || 'Muslim',
          t('preferences.hindu') || 'Hindu',
          t('preferences.jewish') || 'Jewish',
          t('preferences.other') || 'Other',
        ],
        allowTextInput: true,
      },
      t('preferences.no') || 'No',
    ],
  },
  {
    id: 3,
    question: t('preferences.question_3') || 'Do you have any strong political views?',
    options: [
      {
        label: t('preferences.yes') || 'Yes',
        subOptions: [
          t('preferences.very_left') || 'Very left',
          t('preferences.very_right') || 'Very right',
          t('preferences.passionate_about_politics') || 'I just get passionate about politics',
        ],
      },
      t('preferences.no') || 'No',
    ],
  },
];

const OnboardingQuestion: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [questionsState, setQuestionsState] = useState<Question[]>(getInitialQuestions(t));
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setLoading] = useState(false);

  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.EDIT_PREFERANCE>>();
  const {setUser} = useUserStore();
  const navigation = useNavigation<NavigationProps>();

  const userId = auth().currentUser?.uid || '';
  const {mutateAsync: submitOnboardingAnswers} = useSubmitOnboardingAnswers(userId);
  const {data: userData, refetch: refetchUser} = useGetUserAccount(userId);
  const {mutateAsync: updateUser} = useUpdateUser();

  const currentQuestion = questionsState[currentQuestionIndex];

  useEffect(() => {
    if (userData && userData.onboarding_answers && userData.onboarding_answers.length > 0) {
      setUser(userData);
    }
  }, [userData]);

  useEffect(() => {
    if (params?.setting && userData?.onboarding_answers) {
      const preFilledQuestions = getInitialQuestions(t).map(q => {
        const found = userData.onboarding_answers.find(a => a.question === q.question);
        if (found) {
          const isNestedQuestion = q.options.some(opt => typeof opt !== 'string');
          if (isNestedQuestion) {
            const nestedOption = q.options.find(
              opt => typeof opt !== 'string' && opt.subOptions?.includes(t('preferences.other') || 'Other'),
            ) as NestedOption | undefined;
            if (nestedOption && nestedOption.allowTextInput) {
              const subOptions = nestedOption.subOptions || [];
              const isCustomAnswer =
                !subOptions.includes(found.answer) &&
                found.answer !== nestedOption.label &&
                found.answer !== (t('preferences.no') || 'No');
              if (isCustomAnswer) {
                return {...q, selectedAnswer: t('preferences.other') || 'Other', customText: found.answer};
              }
            }
          }
          return {...q, selectedAnswer: found.answer};
        }
        return q;
      });
      setQuestionsState(preFilledQuestions);
    }
  }, [params?.setting, userData?.onboarding_answers, t]);

  const handleOptionSelect = (option: string) => {
    setQuestionsState(prev =>
      prev.map((q, idx) => (idx === currentQuestionIndex ? {...q, selectedAnswer: option, customText: undefined} : q)),
    );
  };

  const handleCustomTextChange = (text: string) => {
    setQuestionsState(prev => prev.map((q, idx) => (idx === currentQuestionIndex ? {...q, customText: text} : q)));
  };

  const handleNext = async () => {
    if (currentQuestionIndex < questionsState.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      setLoading(true);
      try {
        const finalAnswers: Answer[] = questionsState.map(q => ({
          question: q.question,
          answer:
            q.selectedAnswer === (t('preferences.other') || 'Other') && q.customText
              ? q.customText
              : q.selectedAnswer || '',
        }));

        const response: any = await submitOnboardingAnswers({answers: finalAnswers});

        if (response && response.detail) {
          if (params.setting) {
            navigation.goBack();
          } else {
            await updateUser({
              is_registration_finished: true,
            });
            await refetchUser();
          }
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to submit answers:', error);
        setLoading(false);
      }
    }
  };

  // BUG-036 FIX: Add proper back navigation for onboarding questions
  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    } else {
      navigation.goBack();
    }
  };

  const isContinueDisabled = () => {
    const selected = currentQuestion.selectedAnswer;
    if (!selected) {
      return true;
    }

    const nestedOption = currentQuestion.options.find(
      opt => typeof opt !== 'string' && (opt as NestedOption).label === selected,
    ) as NestedOption | undefined;

    if (nestedOption) {
      return true;
    }

    // BUG-037 FIX: Validate that custom text is provided when "Other" is selected
    if (selected === (t('preferences.other') || 'Other')) {
      const customText = currentQuestion.customText;
      return !customText || customText.trim().length === 0;
    }

    return false;
  };

  const modernStyles = {
    loaderContainer: {
      position: 'absolute' as const,
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    mainContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flexGrow: 1,
      paddingHorizontal: spacing.md,
      paddingTop: spacing.xl,
      paddingBottom: spacing['6xl'],
    },
    question: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
      marginBottom: spacing.sm,
    },
    subText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.normal,
      color: colors.textSecondary,
      lineHeight: typography.fontSize.base * 1.5,
      marginBottom: spacing.lg,
    },
    optionContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.sm,
      borderRadius: spacing.md,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border + '40',
    },
    selectedOptionContainer: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '10',
    },
    radioCircle: {
      height: 24,
      width: 24,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: colors.border,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    selectedRadio: {
      borderColor: colors.primary,
    },
    selectedRb: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: colors.primary,
    },
    optionText: {
      marginLeft: spacing.md,
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textPrimary,
      flex: 1,
    },
    input: {
      padding: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: spacing.md,
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      backgroundColor: colors.surface,
      marginTop: spacing.md,
      marginBottom: spacing.md,
    },
    footer: {
      paddingHorizontal: spacing.md,
      paddingTop: spacing.md,
      paddingBottom: spacing.md,
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border + '40',
    },
    dotsContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'center' as const,
      marginTop: spacing.md,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: colors.border,
      marginHorizontal: spacing.xs,
    },
    activeDot: {
      backgroundColor: colors.primary,
    },
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={
          params?.setting
            ? t('settings.edit_preferences') || 'Edit Preferences'
            : t('onboarding.preferences') || 'Preferences'
        }
        variant="default"
        onBackPress={handleBack}
      />

      <ScrollView contentContainerStyle={modernStyles.container} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <Animated.View entering={FadeInDown.delay(100).duration(400)}>
          <ModernCard variant="elevated" padding="xl" margin="md" style={{alignItems: 'center'}}>
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: colors.primary + '20',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: spacing.md,
              }}>
              <Text style={{fontSize: 32}}>⚙️</Text>
            </View>
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.sm,
                textAlign: 'center',
              }}>
              {params?.setting
                ? t('settings.edit_preferences') || 'Edit Preferences'
                : t('onboarding.preferences_title') || 'Your Preferences'}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.base * 1.5,
                textAlign: 'center',
              }}>
              {params?.setting
                ? t('settings.preferences_description') || 'Update your preferences to get better recommendations.'
                : t('onboarding.preferences_description') ||
                  'Help us personalize your experience with a few questions.'}
            </Text>
          </ModernCard>
        </Animated.View>

        {/* Question Card */}
        <Animated.View entering={FadeInDown.delay(200).duration(400)}>
          <ModernCard variant="default" padding="lg" margin="sm">
            <Text style={modernStyles.question}>
              {t('onboarding.question') || 'Question'} {currentQuestionIndex + 1}:
            </Text>
            <Text style={modernStyles.subText}>
              {t(`preferences.question_${currentQuestion.id}`) || currentQuestion.question}
            </Text>

            {currentQuestion.options.map((option, index) => {
              const isNested = typeof option !== 'string';

              if (isNested) {
                const nested = option as NestedOption;
                const isParentSelected =
                  currentQuestion.selectedAnswer === nested.label ||
                  (nested.subOptions && nested.subOptions.includes(currentQuestion.selectedAnswer || ''));

                return (
                  <View key={nested.label}>
                    <TouchableOpacity
                      onPress={() => handleOptionSelect(nested.label)}
                      style={[modernStyles.optionContainer, isParentSelected && modernStyles.selectedOptionContainer]}
                      activeOpacity={0.7}>
                      <View style={[modernStyles.radioCircle, isParentSelected && modernStyles.selectedRadio]}>
                        {isParentSelected && <View style={modernStyles.selectedRb} />}
                      </View>
                      <Text style={modernStyles.optionText}>{nested.label}</Text>
                    </TouchableOpacity>

                    {isParentSelected &&
                      nested.subOptions?.map(sub => (
                        <View key={sub} style={{paddingLeft: spacing.xl}}>
                          <TouchableOpacity
                            onPress={() => handleOptionSelect(sub)}
                            style={[
                              modernStyles.optionContainer,
                              currentQuestion.selectedAnswer === sub && modernStyles.selectedOptionContainer,
                            ]}
                            activeOpacity={0.7}>
                            <View
                              style={[
                                modernStyles.radioCircle,
                                currentQuestion.selectedAnswer === sub && modernStyles.selectedRadio,
                              ]}>
                              {currentQuestion.selectedAnswer === sub && <View style={modernStyles.selectedRb} />}
                            </View>
                            <Text style={modernStyles.optionText}>
                              {sub === 'Other' ? 'Other (please tell us)' : sub}
                            </Text>
                          </TouchableOpacity>

                          {sub === 'Other' && nested.allowTextInput && currentQuestion.selectedAnswer === 'Other' && (
                            <TextInput
                              style={modernStyles.input}
                              placeholder="Enter your answer"
                              placeholderTextColor={colors.textSecondary}
                              value={currentQuestion.customText || ''}
                              onChangeText={handleCustomTextChange}
                            />
                          )}
                        </View>
                      ))}
                  </View>
                );
              }

              return (
                <TouchableOpacity
                  key={option}
                  style={[
                    modernStyles.optionContainer,
                    currentQuestion.selectedAnswer === option && modernStyles.selectedOptionContainer,
                  ]}
                  onPress={() => handleOptionSelect(option)}
                  activeOpacity={0.7}>
                  <View
                    style={[
                      modernStyles.radioCircle,
                      currentQuestion.selectedAnswer === option && modernStyles.selectedRadio,
                    ]}>
                    {currentQuestion.selectedAnswer === option && <View style={modernStyles.selectedRb} />}
                  </View>
                  <Text style={modernStyles.optionText}>{option}</Text>
                </TouchableOpacity>
              );
            })}
          </ModernCard>
        </Animated.View>
      </ScrollView>

      {/* Footer */}
      <SafeAreaView edges={['bottom']} style={{backgroundColor: colors.background}}>
        <Animated.View entering={FadeInDown.delay(300).duration(400)} style={modernStyles.footer}>
          <ModernButton
            title={
              currentQuestionIndex === questionsState.length - 1
                ? params?.setting
                  ? t('settings.save_changes') || 'Save Changes'
                  : t('onboarding.finish') || 'Finish'
                : t('onboarding.continue') || 'Continue'
            }
            onPress={handleNext}
            loading={isLoading}
            variant="primary"
            size="lg"
            disabled={isContinueDisabled()}
          />

          <View style={modernStyles.dotsContainer}>
            {params?.setting
              ? questionsState.map((_, i) => (
                  <View key={i} style={[modernStyles.dot, currentQuestionIndex === i && modernStyles.activeDot]} />
                ))
              : [...['', '', ''], ...questionsState].map((_, i) => (
                  <View key={i} style={[modernStyles.dot, currentQuestionIndex + 3 === i && modernStyles.activeDot]} />
                ))}
          </View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
};

export default OnboardingQuestion;
