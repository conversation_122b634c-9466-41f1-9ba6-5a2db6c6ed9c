import React from 'react';
import {ViewStyle} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  useAnimatedGestureHandler,
  Extrapolate,
} from 'react-native-reanimated';
import {
  TapGestureHandler,
  PanGestureHandler,
  State,
  TapGestureHandlerGestureEvent,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import {haptics} from '~utils/haptics';
import {useAnimationCleanup, safeRunOnJS} from '~utils/viewCleanup';

interface AnimatedPressableProps {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  scaleValue?: number;
  hapticFeedback?: boolean;
  disabled?: boolean;
}

export const AnimatedPressable: React.FC<AnimatedPressableProps> = ({
  children,
  onPress,
  onLongPress,
  style,
  scaleValue = 0.95,
  hapticFeedback = true,
  disabled = false,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(scale, opacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: scale.value}],
    opacity: opacity.value,
  }));

  const tapHandler = useAnimatedGestureHandler<TapGestureHandlerGestureEvent>({
    onStart: () => {
      if (!disabled) {
        scale.value = withSpring(scaleValue, {damping: 15, stiffness: 300});
        opacity.value = withTiming(0.8, {duration: 100});

        if (hapticFeedback) {
          runOnJS(safeRunOnJS(isMountedRef, haptics.light))();
        }
      }
    },
    onEnd: () => {
      if (!disabled) {
        scale.value = withSpring(1, {damping: 15, stiffness: 300});
        opacity.value = withTiming(1, {duration: 150});

        if (onPress) {
          runOnJS(safeRunOnJS(isMountedRef, onPress))();
        }
      }
    },
    onFail: () => {
      scale.value = withSpring(1, {damping: 15, stiffness: 300});
      opacity.value = withTiming(1, {duration: 150});
    },
  });

  const longPressHandler = useAnimatedGestureHandler<TapGestureHandlerGestureEvent>({
    onStart: () => {
      if (!disabled && onLongPress) {
        scale.value = withSpring(scaleValue * 0.9, {damping: 15, stiffness: 300});

        if (hapticFeedback) {
          runOnJS(safeRunOnJS(isMountedRef, haptics.heavy))();
        }
      }
    },
    onEnd: () => {
      if (!disabled) {
        scale.value = withSpring(1, {damping: 15, stiffness: 300});
        opacity.value = withTiming(1, {duration: 150});

        if (onLongPress) {
          runOnJS(safeRunOnJS(isMountedRef, onLongPress))();
        }
      }
    },
  });

  return (
    <TapGestureHandler onGestureEvent={tapHandler} enabled={!disabled}>
      <Animated.View>
        <TapGestureHandler onGestureEvent={longPressHandler} minDurationMs={500} enabled={!disabled && !!onLongPress}>
          <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>
        </TapGestureHandler>
      </Animated.View>
    </TapGestureHandler>
  );
};

interface SwipeableCardProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  style?: ViewStyle;
  swipeThreshold?: number;
  hapticFeedback?: boolean;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  style,
  swipeThreshold = 100,
  hapticFeedback = true,
}) => {
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(translateX, opacity);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
    opacity: opacity.value,
  }));

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      if (hapticFeedback) {
        runOnJS(safeRunOnJS(isMountedRef, haptics.light))();
      }
    },
    onActive: event => {
      translateX.value = event.translationX;

      // Provide haptic feedback at threshold
      if (Math.abs(event.translationX) > swipeThreshold && hapticFeedback) {
        runOnJS(safeRunOnJS(isMountedRef, haptics.medium))();
      }
    },
    onEnd: event => {
      const shouldSwipeLeft = event.translationX < -swipeThreshold;
      const shouldSwipeRight = event.translationX > swipeThreshold;

      if (shouldSwipeLeft && onSwipeLeft) {
        translateX.value = withTiming(-300, {duration: 200});
        opacity.value = withTiming(0, {duration: 200});
        runOnJS(safeRunOnJS(isMountedRef, onSwipeLeft))();

        if (hapticFeedback) {
          runOnJS(safeRunOnJS(isMountedRef, haptics.success))();
        }
      } else if (shouldSwipeRight && onSwipeRight) {
        translateX.value = withTiming(300, {duration: 200});
        opacity.value = withTiming(0, {duration: 200});
        runOnJS(safeRunOnJS(isMountedRef, onSwipeRight))();

        if (hapticFeedback) {
          runOnJS(safeRunOnJS(isMountedRef, haptics.success))();
        }
      } else {
        translateX.value = withSpring(0, {damping: 15, stiffness: 300});
      }
    },
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>
    </PanGestureHandler>
  );
};

interface PulseAnimationProps {
  children: React.ReactNode;
  style?: ViewStyle;
  pulseScale?: number;
  duration?: number;
  repeat?: boolean;
}

export const PulseAnimation: React.FC<PulseAnimationProps> = ({
  children,
  style,
  pulseScale = 1.1,
  duration = 1000,
  repeat = true,
}) => {
  const scale = useSharedValue(1);
  const isMountedRef = useAnimationCleanup(scale);

  React.useEffect(() => {
    const animation = () => {
      if (!isMountedRef.current) return;

      scale.value = withTiming(pulseScale, {duration: duration / 2}, () => {
        if (!isMountedRef.current) return;

        scale.value = withTiming(1, {duration: duration / 2}, () => {
          if (repeat && isMountedRef.current) {
            animation();
          }
        });
      });
    };

    animation();
  }, [scale, pulseScale, duration, repeat, isMountedRef]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: scale.value}],
  }));

  return <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>;
};

interface FadeInProps {
  children: React.ReactNode;
  style?: ViewStyle;
  duration?: number;
  delay?: number;
}

export const FadeIn: React.FC<FadeInProps> = ({children, style, duration = 300, delay = 0}) => {
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);
  const isMountedRef = useAnimationCleanup(opacity, translateY);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        opacity.value = withTiming(1, {duration});
        translateY.value = withTiming(0, {duration});
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [opacity, translateY, duration, delay, isMountedRef]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{translateY: translateY.value}],
  }));

  return <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>;
};

interface ScaleInProps {
  children: React.ReactNode;
  style?: ViewStyle;
  duration?: number;
  delay?: number;
  fromScale?: number;
}

export const ScaleIn: React.FC<ScaleInProps> = ({children, style, duration = 300, delay = 0, fromScale = 0.8}) => {
  const scale = useSharedValue(fromScale);
  const opacity = useSharedValue(0);
  const isMountedRef = useAnimationCleanup(scale, opacity);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        scale.value = withSpring(1, {damping: 15, stiffness: 300});
        opacity.value = withTiming(1, {duration});
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [scale, opacity, duration, delay, isMountedRef]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: scale.value}],
    opacity: opacity.value,
  }));

  return <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>;
};

interface SlideInProps {
  children: React.ReactNode;
  style?: ViewStyle;
  duration?: number;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  style,
  duration = 300,
  delay = 0,
  direction = 'up',
  distance = 30,
}) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? distance : direction === 'down' ? -distance : 0);
  const opacity = useSharedValue(0);
  const isMountedRef = useAnimationCleanup(translateX, translateY, opacity);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (isMountedRef.current) {
        translateX.value = withTiming(0, {duration});
        translateY.value = withTiming(0, {duration});
        opacity.value = withTiming(1, {duration});
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [translateX, translateY, opacity, duration, delay, isMountedRef]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}, {translateY: translateY.value}],
    opacity: opacity.value,
  }));

  return <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>;
};

export default {
  AnimatedPressable,
  SwipeableCard,
  PulseAnimation,
  FadeIn,
  ScaleIn,
  SlideIn,
};
