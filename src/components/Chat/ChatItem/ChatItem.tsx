import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import moment from 'moment-timezone';
import {ImageBackground, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {ShapeIcon} from '~assets/icons';
import {SCREENS} from '~constants';
import {msgType} from '~constants/chat';
import {BUTTON_STATUS_ENUM, CHAT_TYPE_ENUM} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import ModernChatBubble from '../ModernChatBubble/ModernChatBubble';
import {spacing, borderRadius, shadows} from '~constants/design';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';

interface ChatItemProps {
  item: any;
  chatType?: any;
  index?: number;
}

export default function ChatItem({item, chatType, index = 0}: ChatItemProps) {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const uid = auth().currentUser?.uid;
  const navigation = useNavigation<NavigationProps>();

  const senderImage = item.sender_image;
  const senderName = item.senderName || item.sender || '';
  const senderId = item.senderId || item.sender_id;
  const isOwn = senderId === uid;
  const isImage = senderId !== uid && senderImage;

  const isSenderVisible = senderId !== uid && chatType !== CHAT_TYPE_ENUM.PRIVATE;

  const onEventClick = () => {
    const eventId = item.event.event_id;
    navigation.navigate(SCREENS.HOME_EVENT, {eventId: Number(eventId)});
  };

  const handleMessagePress = () => {
    // Handle message press (e.g., show message details)
  };

  const handleMessageLongPress = () => {
    // Handle long press (e.g., show message options)
  };

  return (
    <View style={{flex: 1}}>
      {item.type === msgType.info ? (
        <View style={{width: '100%'}}>
          <Text style={{textAlign: 'center', color: colors.gray400}}>
            <Text style={{fontWeight: '600', textTransform: 'capitalize'}}>{item.names}</Text>
            {item.message}
          </Text>
        </View>
      ) : item.type === 'date' ? (
        <FadeIn delay={index * 50} duration={300}>
          <View style={{width: '100%', marginVertical: spacing.md}}>
            <View
              style={{
                backgroundColor: colors.surface,
                paddingHorizontal: spacing.md,
                paddingVertical: spacing.xs,
                borderRadius: borderRadius.full,
                alignSelf: 'center',
                ...shadows.sm,
              }}>
              <Text
                style={{
                  textAlign: 'center',
                  color: colors.textSecondary,
                  fontSize: 12,
                  fontWeight: '500',
                }}>
                {item.message}
              </Text>
            </View>
          </View>
        </FadeIn>
      ) : item.type === 'event' ? (
        <View
          style={{
            flexDirection: senderId !== uid ? 'row' : 'row-reverse',
            padding: 4,
            // marginLeft: Theme.wp('2%'),
          }}>
          {isImage && (
            <TouchableOpacity
              onPress={() => {
                // @ts-ignore
                navigation.navigate(SCREENS.PERSONAL_INFO, {
                  user: {
                    tag: senderId + 'image',
                    source: senderImage,
                    description: senderName || '',
                    name: senderName,
                    user_id: senderId,
                  },
                });
              }}>
              <FastImage
                source={{uri: senderImage, priority: 'high'}}
                style={{height: 40, width: 40, borderRadius: 8, alignSelf: 'flex-end'}}
              />
            </TouchableOpacity>
          )}
          <View style={{position: 'absolute', bottom: 4, left: isImage ? 46 : 0}}>
            <ShapeIcon isMessageFromCurrentUser={senderId === uid} />
          </View>

          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onEventClick}
            style={{
              left: isImage ? 6 : 0,
              backgroundColor: senderId !== uid ? colors.gray100 : colors.statusPurple,
              paddingTop: 8,
              paddingBottom: 4,
              paddingLeft: 12,
              paddingRight: 12,
              borderRadius: 10,
              maxWidth: '85%',
            }}>
            {isSenderVisible && senderName && (
              <Text style={{color: colors.primary, fontSize: 11, fontWeight: '600'}}>{senderName}</Text>
            )}
            <View style={styles.eventCard}>
              <ImageBackground
                style={[styles.imageEvent, {borderRadius: 10, overflow: 'hidden'}]}
                resizeMode={'cover'}
                source={{uri: item.event.image_url}}>
                <View style={[StyleSheet.absoluteFill, {backgroundColor: colors.overlayBackground}]} />
                <Text numberOfLines={1} style={styles.eventTitle}>
                  {item.event.name}
                </Text>
                <Text numberOfLines={1} style={styles.calloutDescription}>
                  {item.event.coord_address || item.event.address_name}
                </Text>
                <View style={styles.dateRangeContainer}>
                  <Text
                    style={{
                      color: colors.white,
                      fontSize: 10,
                      fontWeight: 'bold',
                    }}>
                    {`${moment.utc(item.event.start_date).format('DD MMM YYYY')} - ${moment.utc(item.event.end_date).format('DD MMM YYYY')}`}
                  </Text>
                </View>
              </ImageBackground>
            </View>

            <Text
              style={{
                fontSize: 10,
                color: senderId !== uid ? colors.black : colors.white,
                textAlign: 'right',
              }}>
              {moment(item.timestamp).format('hh:mm a')}
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ModernChatBubble
          message={item.message}
          timestamp={item.timestamp}
          isOwn={isOwn}
          senderName={senderName}
          showSender={isSenderVisible}
          messageStatus={isOwn ? item.status || item.messageStatus || 'sent' : undefined}
          onPress={handleMessagePress}
          onLongPress={handleMessageLongPress}
          index={index}
        />
      )}
      {item.requestorId && item.status === BUTTON_STATUS_ENUM.SENT && (
        <View
          style={{
            width: '100%',
            marginVertical: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View style={{flex: 1, marginLeft: 14, marginRight: 4}}>
            <TouchableOpacity
              // onPress={disableUserRequest}
              style={{
                borderRadius: 6,
                borderWidth: 1.25,
                borderColor: colors.statusPurple,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 11,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontWeight: '500',
                  color: colors.textPrimary,
                }}>
                Disable
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              flex: 1,
              marginRight: 14,
              marginLeft: 4,
            }}>
            <TouchableOpacity
              // onPress={approveUserRequest}
              style={{
                borderRadius: 6,
                borderWidth: 1.25,
                borderColor: colors.white,
                backgroundColor: colors.statusPurple,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 11,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontWeight: '500',
                  color: colors.white,
                }}>
                Accept
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    calloutDescription: {
      fontSize: 11,
      color: colors.warning,
      position: 'absolute',
      bottom: 7,
      left: 10,
    },
    imageEvent: {
      width: '100%',
      height: '100%',
      borderRadius: 10,
    },
    dateRangeContainer: {
      zIndex: 1,
      position: 'absolute',
      top: 10,
      left: 10,
      backgroundColor: colors.overlayBackground,
      borderRadius: 10,
      padding: 6,
    },
    eventTitle: {
      fontWeight: 'bold',
      color: colors.white,
      fontSize: 12,
      position: 'absolute',
      bottom: 20,
      left: 10,
    },
    eventCard: {
      height: 100,
      width: 300,
      alignSelf: 'center',
      borderRadius: 10,
    },
  });
